image: $BASE_IMAGE

stages:
  - install
  - helm-lint
  - quality
  - build-staging
  - build-sandbox
  - build-fe-prod
  - gitlab-build
  - docker-staging
  - docker-sandbox
  - docker-fe-prod
  - deploy-staging
  - qa-api-staging
  - qa-fe-staging
  - deploy-api-sandbox
  - qa-api-sandbox
  - deploy-fe-sandbox
  - qa-fe-sandbox-cpv2
  - qa-fe-sandbox-mp
  - deploy-api-prod
  - deploy-fe-prod
  - qa-fe-prod

variables:
  VERIFY_CHECKSUM: false
  INDICINA_NPM_READER: $INDICINA_NPM_READER
  APP_NODE_IMAGE: node:18-alpine
  FF_USE_FASTZIP: "true"
  TRANSFER_METER_FREQUENCY: "2s"
  ARTIFACT_COMPRESSION_LEVEL: "slowest"
  CACHE_COMPRESSION_LEVEL: "slowest"
  IMAGE_TAG: "${APP_ENVIRONMENT}-${CI_COMMIT_SHORT_SHA}"
  GITLAB_IMAGE: $CI_REGISTRY_IMAGE:${APP_NAME}-${IMAGE_TAG}
  ##### QA Variables
  POSTMAN_COLLECTION_URL: $POSTMAN_COLLECTION_URL
  SLACK_WEBHOOK_URL: $SLACK_WEBHOOK_URL_PROD
  POSTMAN_ENVIRONMENT_URL: $POSTMAN_ENVIRONMENT_URL_SANDBOX
  POSTMAN_API_KEY: $POSTMAN_API_KEY
  POSTMAN_ENV: $APP_ENVIRONMENT
  CYPRESS_CACHE_FOLDER: "$CI_PROJECT_DIR/cache/Cypress"

.build-gitlab: &build-gitlab
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - if [ -n "$FOLDER_PATH" ]; then cd $FOLDER_PATH; fi
    - docker build -f $DOCKERFILE -t $GITLAB_IMAGE --build-arg ARTIFACT_PATH=$ARTIFACT_PATH .
    - |
      if [ -n "$ARTIFACT_PATH" ]; then
        docker build -f $DOCKERFILE -t $GITLAB_IMAGE --build-arg ARTIFACT_PATH=$ARTIFACT_PATH .
      else
        docker build -f $DOCKERFILE -t $GITLAB_IMAGE .
      fi
    - docker push $GITLAB_IMAGE

.imageBuild: &imageBuild
  image:
    name: us-docker.pkg.dev/gcb-catalog-release/preview/gar-upload@sha256:468bec8ae7097469ffdc41f702936c4d92edeaa72719f52009367c27b5b6788a
  identity: google_cloud
  script:
    - |
      gar-upload --source $GITLAB_IMAGE --target europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/${APP_NAME}:${APP_ENVIRONMENT}-${CI_COMMIT_SHORT_SHA} \
      --version 0.1.1

.deploy_k8s: &deploy_k8s
  image: google/cloud-sdk
  when: manual
  before_script:
    - curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
    - chmod 700 get_helm.sh
    - ./get_helm.sh
    - echo "$OLD_GOOGLE_APPLICATION_CREDENTIALS" > ${CI_PROJECT_DIR}/gcloud-key.json
    - gcloud auth activate-service-account --key-file=${CI_PROJECT_DIR}/gcloud-key.json
    - gcloud config set project $GCP_PROJECT
    - gcloud container clusters get-credentials $GKE_CLUSTER --region $GKE_REGION --project $GCP_PROJECT
  script:
    - |
      helm upgrade --install  $K8S_CMD --set image.tag="${IMAGE_TAG}" \
       --set image.repository="${IMAGE_REPO}" --atomic --cleanup-on-fail  \
       --timeout 900s -n $NAMESPACE $HELM_APP_NAME $K8S_DIR/

.rules:
  main_branch: &main_branch_rules
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: on_success
      changes:
        - apps/$APP_NAME_GIT/**/*
  development_job: &development_job_rules
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: manual
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: never
  merge_request_success: &merge_request_success_rules
    - if: $CI_MERGE_REQUEST_ID
      when: on_success

# Global cache
cache: &global_cache
  key:
    files:
      - package-lock.json
      - package.json
  paths:
    - node_modules
    - cache/Cypress
  policy: pull

.download_install_artifact:
  before_script:
    - |
      apk add --no-cache git curl p7zip
      SOURCE_BRANCH_NAME=$(git log -1 --pretty=format:'%s' | sed -n -E "s/.*branch '([^']*)'.*/\1/p")
      if [ "$SOURCE_BRANCH_NAME" = "main" ] || [ -z "$SOURCE_BRANCH_NAME" ]; then
        SOURCE_BRANCH_NAME=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
      fi
      echo "The resolved source branch name is: $SOURCE_BRANCH_NAME"
      if [ -z "$SOURCE_BRANCH_NAME" ]; then
        echo "Error: SOURCE_BRANCH_NAME is empty"
        exit 1
      fi
      curl --location --output artifacts.zip "https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/jobs/artifacts/${SOURCE_BRANCH_NAME}/download?job=Install&job_token=$CI_JOB_TOKEN"
      7z x artifacts.zip -o./artifacts -y -snl
      cp -R artifacts/* ${CI_PROJECT_DIR}/

# Installation Step
Install:
  stage: install
  image: $APP_NODE_IMAGE
  script:
    - chmod +x gen.sh
    - sh ./gen.sh
    - yarn install --refresh-lockfile
  artifacts:
    paths:
      - node_modules/
      - apps/originate-mp/node_modules
      - apps/Ignite/node_modules
      - apps/customer-portal-v1/node_modules
      - apps/customer-portal-v2/node_modules
      - apps/collection-mp/node_modules
      - apps/collection-be/node_modules
      - packages/collections-*/node_modules/
      - packages/external-integration/node_modules/
      - packages/redis-client/node_modules/
      - packages/ui/node_modules/
      - apps/qa/node_modules/
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: manual

# Helm Linting
HelmLint:
  stage: helm-lint
  script:
    - |
      for dir in apps/*/k8s; do
          for env in demo-ignite production-ignite staging sandbox release prod production; do
            values_file="$dir/values/environment/${env}.yaml";
            if [ -f "$values_file" ]; then
              helm lint "$dir" 
              helm lint "$dir" -f "$values_file";
            else
              echo "Skipping $values_file, file does not exist.";
            fi
          done
      done
  rules:
    - if: $CI_MERGE_REQUEST_ID
      changes:
        - apps/**/k8s/*
      when: on_success

# Quality checks
CodeLint:
  extends: .download_install_artifact
  stage: quality
  image: $APP_NODE_IMAGE
  script:
    - yarn run lint
  rules: *merge_request_success_rules

.turbo_build_originate_fe: &turbo_build_originate_fe
  - npx turbo prune --scope=$APP_NAME --docker --out-dir $APP_ENVIRONMENT-$APP_NAME-prune
  - cp -R ./$APP_ENVIRONMENT-$APP_NAME-prune/full/. $APP_ENVIRONMENT-$APP_NAME-final/
  - cp ./$APP_ENVIRONMENT-$APP_NAME-prune/yarn.lock $APP_ENVIRONMENT-$APP_NAME-final/
  - cp ./gen.sh $APP_ENVIRONMENT-$APP_NAME-final/
  - cp ./$APP_ENVIRONMENT-$APP_NAME-final/apps/$APP_NAME_GIT/public/index.$APP_ENVIRONMENT.html $APP_ENVIRONMENT-$APP_NAME-final/apps/$APP_NAME_GIT/public/index.html
  - cp ./$APP_ENVIRONMENT-$APP_NAME-final/apps/$APP_NAME_GIT/.$APP_ENVIRONMENT.env $APP_ENVIRONMENT-$APP_NAME-final/apps/$APP_NAME_GIT/.env
  - cd $APP_ENVIRONMENT-$APP_NAME-final
  - chmod +x gen.sh
  - sh ./gen.sh
  - yarn install
  - npx turbo build

.build_originate_fe:
  image: $APP_NODE_IMAGE
  script: *turbo_build_originate_fe
  artifacts:
    paths:
      - $APP_ENVIRONMENT-$APP_NAME-final/


###########-originate-mp
BuildStagingOriginate:
  extends: .build_originate_fe
  stage: build-staging
  image: $APP_NODE_IMAGE
  allow_failure: true
  variables:
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: originate-mp
    APP_NAME: originate-mp
  rules: *development_job_rules


GitlabBuildStagingOriginate:
  needs: ["BuildStagingOriginate"]
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    APP_ENVIRONMENT: staging
    ARTIFACT_PATH: $APP_ENVIRONMENT-originate-mp-final/apps/originate-mp
    DOCKERFILE: $APP_ENVIRONMENT-originate-mp-final/apps/originate-mp/Dockerfile
    APP_NAME: originate-mp
  rules: *merge_request_success_rules


DockerStagingOriginate:
  needs: ["GitlabBuildStagingOriginate"]
  stage: docker-staging
  <<: *imageBuild 
  variables:
    APP_NAME: originate-mp
    APP_ENVIRONMENT: staging
  rules: *merge_request_success_rules

DeployStagingOriginate:
  stage: deploy-staging
  <<: *deploy_k8s
  variables:
    K8S_CMD: ""
    APP_NAME_GIT: originate-mp
    NAMESPACE: staging
    HELM_APP_NAME: merchant-portal
    APP_ENVIRONMENT: staging
    K8S_DIR: "apps/originate-mp/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/originate-mp"
  tags:
    - indicina
  needs:
    - job: DockerStagingOriginate
      artifacts: false
  rules: *merge_request_success_rules

QA-UI-staging:
  image:
    name: cypress/included
    entrypoint: [""]
  allow_failure: true
  needs:
    - job: DeployStagingOriginate
  stage: qa-fe-staging
  variables:
    APP_NAME: originate-mp
    APP_ENVIRONMENT: staging
    MP_BASE_URL: https://kwikpay.staging-mp-orig.indicina.net
    CYPRESS_TAGS: '@mpE2E'
    MP_EMAIL: $MP_EMAIL_STAGE
    MP_PASSWORD: $MP_PASSWORD_STAGE
    SLACK_WEBHOOK_URL: $SLACK_WEBHOOK_URL
    CI_JOB_ID: $CI_JOB_ID

  artifacts:
    when: always
    paths:
      - apps/qa/cypress/mochareports/**
      - apps/qa/cypress/reports/**
    expire_in: 1 day
  cache:
    <<: *global_cache
  script:
    - cd apps/qa
    - yarn install
    - yarn cypress run --config-file cypress.mp.config.ts --config baseUrl=$MP_BASE_URL --env tags=$CYPRESS_TAGS
  after_script:
    - cd apps/qa
    - node postTest.js
  tags:
    - indicina
  rules: *merge_request_success_rules


BuildSandboxOriginate:
  extends: .build_originate_fe
  stage: build-sandbox
  image: $APP_NODE_IMAGE
  allow_failure: true
  variables:
    APP_ENVIRONMENT: sandbox
    APP_NAME_GIT: originate-mp
    APP_NAME: originate-mp
  rules: *main_branch_rules


GitlabBuildSandboxOriginate:
  dependencies: ["BuildSandboxOriginate"]
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    APP_ENVIRONMENT: sandbox
    ARTIFACT_PATH: $APP_ENVIRONMENT-originate-mp-final/apps/originate-mp
    DOCKERFILE: $APP_ENVIRONMENT-originate-mp-final/apps/originate-mp/Dockerfile
    APP_NAME: originate-mp
    APP_NAME_GIT: originate-mp
  rules: *main_branch_rules


DockerSandboxOriginate:
  dependencies: ["GitlabBuildSandboxOriginate"]
  stage: docker-sandbox
  <<: *imageBuild 
  variables:
    APP_NAME: originate-mp
    APP_ENVIRONMENT: sandbox
    APP_NAME_GIT: originate-mp
  rules: *main_branch_rules

DeploySandboxOriginate:
  stage: deploy-fe-sandbox
  <<: *deploy_k8s
  dependencies: []
  variables:
    K8S_CMD: "-f apps/originate-mp/k8s/values/environment/sandbox.yaml"
    APP_NAME_GIT: originate-mp
    NAMESPACE: sandbox
    HELM_APP_NAME: merchant-portal
    APP_ENVIRONMENT: sandbox
    K8S_DIR: "apps/originate-mp/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/originate-mp"
  tags:
    - indicina
  rules: *main_branch_rules

QA-UI-sandbox-mp:
  image:
    name: cypress/included
    entrypoint: [""]
  needs: 
    - job: DeploySandboxOriginate
  stage: qa-fe-sandbox-mp
  variables:
    APP_NAME: originate-mp
    APP_ENVIRONMENT: sandbox
    MP_BASE_URL: https://kwikpay.sandbox-mp-orig.indicina.co
    CYPRESS_TAGS: '@mpRegression'
    MP_EMAIL: $MP_EMAIL_SANDBOX
    MP_PASSWORD: $MP_PASSWORD_SANDBOX
    SLACK_WEBHOOK_URL: $SLACK_WEBHOOK_URL
  artifacts:
    when: always
    paths:
      - apps/qa/cypress/mochareports/**
      - apps/qa/cypress/reports/**
    expire_in: 1 day
  cache:
    <<: *global_cache
  script:
    - cd apps/qa
    - yarn install --frozen-lockfile
    - yarn cypress run --config-file cypress.mp.config.ts --config baseUrl=$MP_BASE_URL --env tags=$CYPRESS_TAGS
  after_script:
    - cd apps/qa
    - chmod +x ./postTest.sh
    - yarn run slack:posttest
  tags:
    - indicina
  rules: *main_branch_rules


QA-UI-sandbox-CPV2:
  image:
    name: cypress/included
    entrypoint: [""]
  needs: 
    - job: DeploySandboxCustomerPortalV2
  stage: qa-fe-sandbox-cpv2
  variables:
    APP_NAME_GIT: customer-portal-v2
    APP_NAME: customer-portal-v2
    APP_ENVIRONMENT: sandbox
    CP_BASE_URL: https://sandbox-cpv2-orig.indicina.net/kwikpay
    CYPRESS_TAGS: '@cpRegression'
    CP_EMAIL: $CP_EMAIL_SANDBOX
    CP_PASSWORD: $CP_PASSWORD_SANDBOX
    SLACK_WEBHOOK_URL: $SLACK_WEBHOOK_URL
  artifacts:
    when: always
    paths:
      - apps/qa/cypress/mochareports/**
      - apps/qa/cypress/reports/**
    expire_in: 1 day
  cache:
    <<: *global_cache
  script:
    - cd apps/qa
    - yarn install --frozen-lockfile
    - yarn cypress run --config-file cypress.cp.config.ts --config baseUrl=$CPV2_BASE_URL_SANDBOX --env tags=$CYPRESS_TAGS
  after_script:
    - cd apps/qa
    - chmod +x ./postTest.sh
    - yarn run slack:posttest
  tags:
    - indicina
  rules: *main_branch_rules

BuildProdOriginate:
  extends: .build_originate_fe
  stage: build-fe-prod
  image: $APP_NODE_IMAGE
  allow_failure: true
  variables:
    APP_ENVIRONMENT: production
    APP_NAME_GIT: originate-mp
    APP_NAME: originate-mp
  rules: *main_branch_rules


GitlabBuildProdOriginate:
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    APP_ENVIRONMENT: production
    ARTIFACT_PATH: $APP_ENVIRONMENT-originate-mp-final/apps/originate-mp
    DOCKERFILE: $APP_ENVIRONMENT-originate-mp-final/apps/originate-mp/Dockerfile
    APP_NAME: originate-mp
    APP_NAME_GIT: originate-mp
  dependencies:
    - BuildProdOriginate
  rules: *main_branch_rules


DockerProdOriginate:
  <<: *imageBuild 
  stage: docker-fe-prod
  variables:
    APP_ENVIRONMENT: production
    APP_NAME: originate-mp
    APP_NAME_GIT: originate-mp
  dependencies:
    - GitlabBuildProdOriginate
  rules: *main_branch_rules




DeployProdOriginate:
  stage: deploy-fe-prod
  <<: *deploy_k8s
  dependencies: []
  variables:
    K8S_CMD: '-f apps/originate-mp/k8s/values/environment/production.yaml'
    NAMESPACE: frontend
    HELM_APP_NAME: merchant-portal
    APP_ENVIRONMENT: production
    K8S_DIR: "apps/originate-mp/k8s"
    APP_NAME_GIT: originate-mp
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/originate-mp"
  tags:
    - indicina
 
  rules: *main_branch_rules

QA-UI-prod:
  image:
    name: cypress/included
    entrypoint: [""]
  allow_failure: true
  needs:
    - job: DeployProdOriginate
  stage: qa-fe-prod
  variables:
    APP_NAME: originate-mp
    APP_ENVIRONMENT: production
    MP_BASE_URL: https://indicina.originate.ng/
    CYPRESS_TAGS: '@mpSanity'
    MP_EMAIL: $MP_EMAIL_PROD
    MP_PASSWORD: $MP_PASSWORD_PROD
    SLACK_WEBHOOK_URL: $SLACK_WEBHOOK_URL_PROD

  artifacts:
    when: always
    paths:
      - apps/qa/cypress/mochareports/**
      - apps/qa/cypress/reports/**
    expire_in: 1 day
  cache:
    <<: *global_cache
  script:
    - cd apps/qa
    - yarn install --frozen-lockfile
    - yarn cypress run --config-file cypress.mp.config.ts --config baseUrl=$MP_BASE_URL --env tags=$CYPRESS_TAGS
  after_script:
    - cd apps/qa
    - chmod +x .postTest.sh
    - yarn run slack:posttest
  tags:
    - indicina
  rules: *main_branch_rules

##########-originate-end


########### ignite

.turbo_build_ignite: &turbo_build_ignite
  - npx turbo build --filter=ignite
  - npx turbo prune --scope=ignite --docker --out-dir $APP_ENVIRONMENT-ignite-prune
  - mkdir -p $APP_ENVIRONMENT-ignite-final
  - cp -R ./$APP_ENVIRONMENT-ignite-prune/json/. $APP_ENVIRONMENT-ignite-final/
  - cp ./$APP_ENVIRONMENT-ignite-prune/full/turbo.json $APP_ENVIRONMENT-ignite-final/
  - cp ./$APP_ENVIRONMENT-ignite-prune/full/apps/Ignite/puppeteer.config.js $APP_ENVIRONMENT-ignite-final
  - cp ./$APP_ENVIRONMENT-ignite-prune/full/apps/Ignite/Dockerfile.k8s $APP_ENVIRONMENT-ignite-final
  - cp -R ./$APP_ENVIRONMENT-ignite-prune/full/apps/Ignite/docker $APP_ENVIRONMENT-ignite-final/apps/Ignite/docker
  - cp -R ./apps/Ignite/build $APP_ENVIRONMENT-ignite-final/apps/Ignite/build
  - cp -R ./$APP_ENVIRONMENT-ignite-prune/full/apps/Ignite/prisma $APP_ENVIRONMENT-ignite-final/apps/Ignite/prisma
  - cp -R ./$APP_ENVIRONMENT-ignite-prune/full/apps/Ignite/scripts $APP_ENVIRONMENT-ignite-final/apps/Ignite/scripts
  - cp -R ./$APP_ENVIRONMENT-ignite-prune/full/apps/Ignite/bin $APP_ENVIRONMENT-ignite-final/apps/Ignite/bin
  - cp ./gen.sh $APP_ENVIRONMENT-ignite-final/
  - cd $APP_ENVIRONMENT-ignite-final
  - chmod +x gen.sh
  - sh ./gen.sh
  - yarn workspaces focus --production --all


.build_ignite:
  extends: .download_install_artifact
  image: $APP_NODE_IMAGE
  script: *turbo_build_ignite
  artifacts:
    paths:
      - $APP_ENVIRONMENT-ignite-final/


BuildStagingIgnite:
  extends: .build_ignite
  stage: build-staging
  allow_failure: true
  variables:
    APP_ENVIRONMENT: staging
  rules: *development_job_rules



GitlabBuildStagingIgnite:
  <<: *build-gitlab 
  stage: gitlab-build
  needs: ["BuildStagingIgnite"]
  variables:
    DOCKERFILE: $APP_ENVIRONMENT-ignite-final/Dockerfile.k8s
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: Ignite
    APP_NAME: ignite
    ARTIFACT_PATH: $APP_ENVIRONMENT-ignite-final
  rules: *merge_request_success_rules


DockerStagingIgnite:
  <<: *imageBuild 
  stage: docker-staging
  variables:
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: Ignite
    APP_NAME: ignite
  needs: ["GitlabBuildStagingIgnite"]
  rules: *merge_request_success_rules

DeployStagingIgnite:
  <<: *deploy_k8s
  stage: deploy-staging
  needs:
    - job: DockerStagingIgnite
      artifacts: false
  variables:
    APP_ENVIRONMENT: staging
    K8S_CMD: ""
    NAMESPACE: staging
    HELM_APP_NAME: ignite
    K8S_DIR: "apps/Ignite/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/ignite"
  rules: *merge_request_success_rules

############ QA Jobs
QA-API-Staging:
  image: $APP_NODE_IMAGE
  stage: qa-api-staging
  variables:
    APP_ENVIRONMENT: staging
    SLACK_WEBHOOK_URL: $SLACK_WEBHOOK_URL
    POSTMAN_COLLECTION_URL: $POSTMAN_COLLECTION_URL
    POSTMAN_ENVIRONMENT_URL: $POSTMAN_ENVIRONMENT_URL_STAGE
    POSTMAN_API_KEY: $POSTMAN_API_KEY
    POSTMAN_ENV: staging

  artifacts:
    when: always
    paths:
      - apps/qa/apiTestReport/**
    expire_in: 1 day
  script:
    - cd apps/qa
    - yarn install
    - node runNewman.js
  needs: ["DeployStagingIgnite"]
  rules: *merge_request_success_rules

QA-API-Sandbox:
  image: $APP_NODE_IMAGE
  stage: qa-api-sandbox
  variables:
    APP_NAME_GIT: Ignite
    APP_ENVIRONMENT: sandbox
    SLACK_WEBHOOK_URL: $SLACK_WEBHOOK_URL
    POSTMAN_COLLECTION_URL: $POSTMAN_COLLECTION_URL
    POSTMAN_ENVIRONMENT_URL: $POSTMAN_ENVIRONMENT_URL_SANDBOX
    POSTMAN_API_KEY: $POSTMAN_API_KEY
    POSTMAN_ENV: sandbox
  artifacts:
    when: always
    paths:
      - apps/qa/apiTestReport/**
    expire_in: 1 day
  script:
    - cd apps/qa
    - yarn install
    - node runNewman.js
  needs: ["DeploySandboxIgnite"]
  rules: *main_branch_rules

BuildSandboxIgnite:
  extends: .build_ignite
  stage: build-sandbox
  variables:
    APP_ENVIRONMENT: sandbox
    APP_NAME_GIT: Ignite
  rules: *main_branch_rules


GitlabBuildSandboxIgnite:
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    DOCKERFILE: $APP_ENVIRONMENT-ignite-final/Dockerfile.k8s
    APP_NAME_GIT: Ignite
    APP_NAME: ignite
    APP_ENVIRONMENT: sandbox
    ARTIFACT_PATH: $APP_ENVIRONMENT-ignite-final
  rules: *main_branch_rules
  dependencies:
    - BuildSandboxIgnite


DockerSandboxIgnite:
  <<: *imageBuild 
  stage: docker-sandbox
  variables:
    APP_NAME_GIT: Ignite
    APP_NAME: ignite
    APP_ENVIRONMENT: sandbox
  rules: *main_branch_rules
  dependencies:
    - GitlabBuildSandboxIgnite

DeploySandboxIgnite:
  <<: *deploy_k8s
  stage: deploy-api-sandbox
  dependencies: []
  variables:
    APP_NAME_GIT: Ignite
    K8S_CMD: "-f apps/Ignite/k8s/values/environment/sandbox.yaml"
    NAMESPACE: sandbox
    APP_ENVIRONMENT: sandbox
    K8S_DIR: "apps/Ignite/k8s"
    HELM_APP_NAME: ignite
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/ignite"
  rules: *main_branch_rules



DeployProdIgnite:
  <<: *deploy_k8s
  stage: deploy-api-prod
  dependencies: []
  variables:
    APP_NAME_GIT: Ignite
    HELM_APP_NAME: ignite
    K8S_CMD: "-f apps/Ignite/k8s/values/environment/production.yaml"
    APP_ENVIRONMENT: sandbox
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/ignite"
    K8S_DIR: "apps/Ignite/k8s"
    NAMESPACE: production
  rules: *main_branch_rules

########### ignite end

###########-customer-portal-v1
BuildStagingCustomerPortal:
  extends: .build_originate_fe
  stage: build-staging
  image: $APP_NODE_IMAGE
  allow_failure: true
  variables:
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: customer-portal-v1
    APP_NAME: customer-portal
  rules: *development_job_rules



GitlabBuildStagingCustomerPortal:
  <<: *build-gitlab 
  needs: ["BuildStagingCustomerPortal"]
  stage: gitlab-build
  variables:
    APP_ENVIRONMENT: staging
    APP_NAME: customer-portal
    ARTIFACT_PATH: $APP_ENVIRONMENT-customer-portal-final/apps/customer-portal-v1
    DOCKERFILE: $APP_ENVIRONMENT-customer-portal-final/apps/customer-portal-v1/Dockerfile
  rules: *merge_request_success_rules



DockerStagingCustomerPortal: 
  <<: *imageBuild 
  stage: docker-staging
  variables:
    APP_ENVIRONMENT: staging
    APP_NAME: customer-portal
  needs: ["GitlabBuildStagingCustomerPortal"]
  rules: *merge_request_success_rules

DeployStagingCustomerPortal:
  stage: deploy-staging
  <<: *deploy_k8s
  needs:
    - job: DockerStagingCustomerPortal
      artifacts: false
  variables:
    K8S_CMD: ""
    NAMESPACE: staging
    APP_NAME_GIT: customer-portal-v1
    HELM_APP_NAME: dynamic-cp-s
    APP_ENVIRONMENT: staging
    K8S_DIR: "apps/customer-portal-v1/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/customer-portal"
  tags:
    - indicina
  dependencies:
    - DockerStagingCustomerPortal
  rules: *merge_request_success_rules





BuildSandboxCustomerPortal:
  extends: .build_originate_fe
  stage: build-sandbox
  image: $APP_NODE_IMAGE
  allow_failure: true
  variables:
    APP_ENVIRONMENT: sandbox
    APP_NAME_GIT: customer-portal-v1
    APP_NAME: customer-portal
  rules: *main_branch_rules


GitlabBuildSandboxCustomerPortal:
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    APP_ENVIRONMENT: sandbox
    APP_NAME: customer-portal
    APP_NAME_GIT: customer-portal-v1
    ARTIFACT_PATH: $APP_ENVIRONMENT-customer-portal-final/apps/customer-portal-v1
    DOCKERFILE: $APP_ENVIRONMENT-customer-portal-final/apps/customer-portal-v1/Dockerfile
  rules: *main_branch_rules
  dependencies:
    - BuildSandboxCustomerPortal


DockerSandboxCustomerPortal:
  stage: docker-sandbox
  <<: *imageBuild 
  variables:
    APP_ENVIRONMENT: sandbox
    APP_NAME: customer-portal
    APP_NAME_GIT: customer-portal-v1
  rules: *main_branch_rules
  dependencies:
    - GitlabBuildSandboxCustomerPortal

DeploySandboxCustomerPortal:
  stage: deploy-fe-sandbox
  <<: *deploy_k8s
  dependencies: []
  variables:
    APP_NAME_GIT: customer-portal-v1
    K8S_CMD: "-f apps/customer-portal-v1/k8s/values/environment/demo-ignite.yaml"
    NAMESPACE: sandbox
    HELM_APP_NAME: dynamic-cp-d
    APP_ENVIRONMENT: sandbox
    K8S_DIR: "apps/customer-portal-v1/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/customer-portal"
  tags:
    - indicina
  rules: *main_branch_rules




BuildProdCustomerPortal:
  extends: .build_originate_fe
  stage: build-fe-prod
  image: $APP_NODE_IMAGE
  allow_failure: true
  variables:
    APP_ENVIRONMENT: production
    APP_NAME_GIT: customer-portal-v1
    APP_NAME: customer-portal
  rules: *main_branch_rules

GitlabBuildProdCustomerPortal:
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    APP_ENVIRONMENT: production
    ARTIFACT_PATH: $APP_ENVIRONMENT-customer-portal-final/apps/customer-portal-v1
    DOCKERFILE: $APP_ENVIRONMENT-customer-portal-final/apps/customer-portal-v1/Dockerfile
    APP_NAME: customer-portal
    APP_NAME_GIT: customer-portal-v1
  rules: *main_branch_rules
  dependencies:
    - BuildProdCustomerPortal


DockerProdCustomerPortal:
  <<: *imageBuild 
  stage: docker-fe-prod
  variables:
    APP_ENVIRONMENT: production
    APP_NAME: customer-portal
    APP_NAME_GIT: customer-portal-v1
  dependencies:
    - GitlabBuildProdCustomerPortal
  rules: *main_branch_rules



DeployProdCustomerPortal:
  stage: deploy-fe-prod
  <<: *deploy_k8s
  dependencies: []
  variables:
    APP_NAME_GIT: customer-portal-v1
    K8S_CMD: "-f apps/customer-portal-v1/k8s/values/environment/production-ignite.yaml"
    NAMESPACE: frontend
    HELM_APP_NAME: dynamic-cp
    APP_ENVIRONMENT: production
    K8S_DIR: "apps/customer-portal-v1/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/customer-portal"
  tags:
    - indicina
  rules: *main_branch_rules

###########-customer-portal-v1 end


###########-customer-portal-v2

BuildStagingCustomerPortalV2:
  extends: .download_install_artifact
  stage: build-staging
  image: $APP_NODE_IMAGE
  allow_failure: true
  variables:
    APP_NAME_GIT: customer-portal-v2
  script:
   - yarn build --filter=customer-portal-v2
  artifacts:
    paths:
      - ./apps/customer-portal-v2/.next
      - ./apps/customer-portal-v2/docker/
  rules: *development_job_rules



GitlabBuildStagingCustomerPortalV2:
  <<: *build-gitlab 
  needs: ["BuildStagingCustomerPortalV2"]
  stage: gitlab-build
  variables:
    FOLDER_PATH: "apps/customer-portal-v2"
    ARTIFACT_PATH: ".next"
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: customer-portal-v2
    APP_NAME: customer-portal-v2
    DOCKERFILE: "Dockerfile"
  rules: *merge_request_success_rules



DockerStagingCustomerPortalV2: 
  <<: *imageBuild 
  stage: docker-staging
  variables:
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: customer-portal-v2
    APP_NAME: customer-portal-v2
  needs: ["GitlabBuildStagingCustomerPortalV2"]
  rules: *merge_request_success_rules

DeployStagingCustomerPortalV2:
  stage: deploy-staging
  <<: *deploy_k8s
  needs:
    - job: DockerStagingCustomerPortalV2
      artifacts: false
  variables:
    K8S_CMD: "-f apps/customer-portal-v2/k8s/values/environment/staging.yaml"
    NAMESPACE: staging
    APP_NAME_GIT: customer-portal-v2
    HELM_APP_NAME: dynamic-cp-s2
    APP_ENVIRONMENT: staging
    K8S_DIR: "apps/customer-portal-v2/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/customer-portal-v2"
  tags:
    - indicina
  dependencies:
    - DockerStagingCustomerPortalV2
  rules: *merge_request_success_rules





BuildSandboxCustomerPortalV2:
  extends: .download_install_artifact
  stage: build-sandbox
  image: $APP_NODE_IMAGE
  variables:
    APP_NAME_GIT: customer-portal-v2
  script:
    - yarn build --filter=customer-portal-v2
  rules: *main_branch_rules
  artifacts:
    paths:
      - ./apps/customer-portal-v2/.next
    


GitlabBuildSandboxCustomerPortalV2:
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    APP_NAME_GIT: customer-portal-v2
    FOLDER_PATH: "apps/customer-portal-v2"
    APP_ENVIRONMENT: sandbox
    ARTIFACT_PATH: ".next"
    APP_NAME: customer-portal-v2
    DOCKERFILE: "Dockerfile"
  rules: *main_branch_rules
  dependencies:
    - BuildSandboxCustomerPortalV2


DockerSandboxCustomerPortalV2:
  stage: docker-sandbox
  <<: *imageBuild 
  variables:
    APP_NAME_GIT: customer-portal-v2
    APP_ENVIRONMENT: sandbox
    APP_NAME: customer-portal-v2
  rules: *main_branch_rules
  dependencies:
    - GitlabBuildSandboxCustomerPortalV2

DeploySandboxCustomerPortalV2:
  stage: deploy-fe-sandbox
  <<: *deploy_k8s
  dependencies: []
  variables:
    APP_NAME_GIT: customer-portal-v2
    K8S_CMD: "-f apps/customer-portal-v2/k8s/values/environment/sandbox.yaml"
    NAMESPACE: sandbox
    HELM_APP_NAME: dynamic-cp-d2
    APP_ENVIRONMENT: sandbox
    K8S_DIR: "apps/customer-portal-v2/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/customer-portal-v2"
  tags:
    - indicina
  rules: *main_branch_rules




BuildProdCustomerPortalV2:
  extends: .download_install_artifact
  stage: build-fe-prod
  image: $APP_NODE_IMAGE
  variables:
    APP_NAME_GIT: customer-portal-v2
    REACT_APP_BASE_URL: "https://ignite.originate.ng/"
    REACT_APP_IGNITE_WEBSOCKET_URL: "wss://ignite.originate.ng/graphql"
  script:
    - export REACT_APP_IMAGE_TAG=$IMAGE_TAG
    - yarn build --filter=customer-portal-v2
  artifacts:
    paths:
      - ./apps/customer-portal-v2/.next
  rules: *main_branch_rules

GitlabBuildProdCustomerPortalV2:
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    FOLDER_PATH: "apps/customer-portal-v2"
    APP_NAME_GIT: customer-portal-v2
    APP_ENVIRONMENT: prod
    ARTIFACT_PATH: ".next"
    APP_NAME: customer-portal-v2
    DOCKERFILE: "Dockerfile"
  rules: *main_branch_rules
  dependencies:
    - BuildProdCustomerPortalV2


DockerProdCustomerPortalV2:
  <<: *imageBuild 
  stage: docker-fe-prod
  variables:
    APP_NAME_GIT: customer-portal-v2
    APP_ENVIRONMENT: prod
    APP_NAME: customer-portal-v2
  dependencies:
    - GitlabBuildProdCustomerPortalV2
  rules: *main_branch_rules



DeployProdCustomerPortalV2:
  stage: deploy-fe-prod
  <<: *deploy_k8s
  dependencies: []
  variables:
    APP_NAME_GIT: customer-portal-v2
    K8S_CMD: "-f apps/customer-portal-v2/k8s/values/environment/prod.yaml"
    NAMESPACE: frontend
    HELM_APP_NAME: dynamic-cp2
    APP_ENVIRONMENT: prod
    K8S_DIR: "apps/customer-portal-v2/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/customer-portal-v2"
  tags:
    - indicina
  rules: *main_branch_rules

###########-customer-portal-v2 end


###########-collection-mp

BuildStagingCollectionMP:
  extends: .download_install_artifact
  stage: build-staging
  image: $APP_NODE_IMAGE
  allow_failure: true
  variables:
    APP_NAME_GIT: collection-mp
  script:
    - yarn build --filter=collection-mp
  artifacts:
    paths:
      - ./apps/collection-mp/.next
      - ./apps/collection-mp/docker/
  rules: *development_job_rules

GitlabBuildStagingCollectionMP:
  <<: *build-gitlab
  needs: ["BuildStagingCollectionMP"]
  stage: gitlab-build
  variables:
    FOLDER_PATH: "apps/collection-mp"
    ARTIFACT_PATH: ".next"
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: collection-mp
    APP_NAME: collection-mp
    DOCKERFILE: "Dockerfile"
  rules: *merge_request_success_rules

DockerStagingCollectionMP:
  <<: *imageBuild
  stage: docker-staging
  variables:
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: collection-mp
    APP_NAME: collection-mp
  needs: ["GitlabBuildStagingCollectionMP"]
  rules: *merge_request_success_rules

DeployStagingCollectionMP:
  stage: deploy-staging
  <<: *deploy_k8s
  needs:
    - job: DockerStagingCollectionMP
      artifacts: false
  variables:
    K8S_CMD: "-f apps/collection-mp/k8s/values/staging.yaml"
    NAMESPACE: staging
    APP_NAME_GIT: collection-mp
    HELM_APP_NAME: collection-mp
    APP_ENVIRONMENT: staging
    K8S_DIR: "apps/collection-mp/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/collection-mp"
  tags:
    - indicina
  dependencies:
    - DockerStagingCollectionMP
  rules: *merge_request_success_rules

BuildSandboxCollectionMP:
  extends: .download_install_artifact
  stage: build-sandbox
  image: $APP_NODE_IMAGE
  variables:
    APP_NAME_GIT: collection-mp
  script:
    - yarn build --filter=collection-mp
  rules: *main_branch_rules
  artifacts:
    paths:
      - ./apps/collection-mp/.next

GitlabBuildSandboxCollectionMP:
  <<: *build-gitlab
  stage: gitlab-build
  variables:
    APP_NAME_GIT: collection-mp
    FOLDER_PATH: "apps/collection-mp"
    APP_ENVIRONMENT: sandbox
    ARTIFACT_PATH: ".next"
    APP_NAME: collection-mp
    DOCKERFILE: "Dockerfile"
  rules: *main_branch_rules
  dependencies:
    - BuildSandboxCollectionMP

DockerSandboxCollectionMP:
  stage: docker-sandbox
  <<: *imageBuild
  variables:
    APP_NAME_GIT: collection-mp
    APP_ENVIRONMENT: sandbox
    APP_NAME: collection-mp
  rules: *main_branch_rules
  dependencies:
    - GitlabBuildSandboxCollectionMP

DeploySandboxCollectionMP:
  stage: deploy-fe-sandbox
  <<: *deploy_k8s
  dependencies: []
  variables:
    APP_NAME_GIT: collection-mp
    K8S_CMD: "-f apps/collection-mp/k8s/values/sandbox.yaml"
    NAMESPACE: sandbox
    HELM_APP_NAME: collection-mp
    APP_ENVIRONMENT: sandbox
    K8S_DIR: "apps/collection-mp/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/collection-mp"
  tags:
    - indicina
  rules: *main_branch_rules

BuildProdCollectionMP:
  extends: .download_install_artifact
  stage: build-fe-prod
  image: $APP_NODE_IMAGE
  variables:
    APP_NAME_GIT: collection-mp
  script:
    - export REACT_APP_IMAGE_TAG=$IMAGE_TAG
    - yarn build --filter=collection-mp
  artifacts:
    paths:
      - ./apps/collection-mp/.next
  rules: *main_branch_rules

GitlabBuildProdCollectionMP:
  <<: *build-gitlab
  stage: gitlab-build
  variables:
    FOLDER_PATH: "apps/collection-mp"
    APP_NAME_GIT: collection-mp
    APP_ENVIRONMENT: prod
    ARTIFACT_PATH: ".next"
    APP_NAME: collection-mp
    DOCKERFILE: "Dockerfile"
  rules: *main_branch_rules
  dependencies:
    - BuildProdCollectionMP

DockerProdCollectionMP:
  <<: *imageBuild
  stage: docker-fe-prod
  variables:
    APP_NAME_GIT: collection-mp
    APP_ENVIRONMENT: prod
    APP_NAME: collection-mp
  dependencies:
    - GitlabBuildProdCollectionMP
  rules: *main_branch_rules

DeployProdCollectionMP:
  stage: deploy-fe-prod
  <<: *deploy_k8s
  dependencies: []
  variables:
    APP_NAME_GIT: collection-mp
    K8S_CMD: "-f apps/collection-mp/k8s/values/production.yaml"
    NAMESPACE: production
    HELM_APP_NAME: collection-mp
    APP_ENVIRONMENT: prod
    K8S_DIR: "apps/collection-mp/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/collection-mp"
  tags:
    - indicina
  rules: *main_branch_rules

###########-collection-mp end


###########-collection-be

.turbo_build_collection_be: &turbo_build_collection_be
  - npx turbo build --filter=collection-be
  - npx turbo prune --scope=collection-be --docker --out-dir $APP_ENVIRONMENT-collection-be-prune
  - mkdir -p $APP_ENVIRONMENT-collection-be-final
  - cp -R ./$APP_ENVIRONMENT-collection-be-prune/full/packages $APP_ENVIRONMENT-collection-be-final/packages
  - cp -R ./$APP_ENVIRONMENT-collection-be-prune/json/. $APP_ENVIRONMENT-collection-be-final/
  - cp ./$APP_ENVIRONMENT-collection-be-prune/full/turbo.json $APP_ENVIRONMENT-collection-be-final/
  - cp ./$APP_ENVIRONMENT-collection-be-prune/full/apps/collection-be/Dockerfile $APP_ENVIRONMENT-collection-be-final
  - cp -R ./$APP_ENVIRONMENT-collection-be-prune/full/apps/collection-be/docker $APP_ENVIRONMENT-collection-be-final/apps/collection-be/docker
  - cp ./$APP_ENVIRONMENT-collection-be-prune/full/apps/collection-be/tsconfig.json $APP_ENVIRONMENT-collection-be-final/apps/collection-be/tsconfig.json
  - cp -R ./apps/collection-be/dist $APP_ENVIRONMENT-collection-be-final/apps/collection-be/dist
  - cp ./gen.sh $APP_ENVIRONMENT-collection-be-final/
  - |
    for dir in ./packages/*; do
      for folder in dist build; do
        if [ -d "$dir/$folder" ]; then
          mkdir -p "$APP_ENVIRONMENT-collection-be-final/packages/$(basename "$dir")"
          cp -R "$dir/$folder" "$APP_ENVIRONMENT-collection-be-final/packages/$(basename "$dir")/"
        fi
      done
    done
  - cd $APP_ENVIRONMENT-collection-be-final
  - chmod +x gen.sh
  - sh ./gen.sh
  - yarn workspaces focus --production --all

.build_collection_be:
  extends: .download_install_artifact
  image: $APP_NODE_IMAGE
  script: *turbo_build_collection_be
  artifacts:
    paths:
      - $APP_ENVIRONMENT-collection-be-final/

BuildStagingCollectionBE:
  extends: .build_collection_be
  stage: build-staging
  allow_failure: true
  variables:
    APP_ENVIRONMENT: staging
  rules: *development_job_rules

GitlabBuildStagingCollectionBE:
  <<: *build-gitlab 
  stage: gitlab-build
  needs: ["BuildStagingCollectionBE"]
  variables:
    DOCKERFILE: $APP_ENVIRONMENT-collection-be-final/Dockerfile
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: collection-be
    APP_NAME: collection-api
    ARTIFACT_PATH: $APP_ENVIRONMENT-collection-be-final
  rules: *merge_request_success_rules

DockerStagingCollectionBE:
  <<: *imageBuild 
  stage: docker-staging
  variables:
    APP_ENVIRONMENT: staging
    APP_NAME_GIT: collection-be
    APP_NAME: collection-api
  needs: ["GitlabBuildStagingCollectionBE"]
  rules: *merge_request_success_rules

DeployStagingCollectionBE:
  <<: *deploy_k8s
  stage: deploy-staging
  needs:
    - job: DockerStagingCollectionBE
      artifacts: false
  variables:
    APP_ENVIRONMENT: staging
    K8S_CMD: "-f apps/collection-be/k8s/values/staging.yaml"
    NAMESPACE: staging
    HELM_APP_NAME: collection-api
    K8S_DIR: "apps/collection-be/k8s"
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/collection-api"
  rules: *merge_request_success_rules

BuildSandboxCollectionBE:
  extends: .build_collection_be
  stage: build-sandbox
  variables:
    APP_ENVIRONMENT: sandbox
    APP_NAME_GIT: collection-be
  rules: *main_branch_rules

GitlabBuildSandboxCollectionBE:
  <<: *build-gitlab 
  stage: gitlab-build
  variables:
    DOCKERFILE: $APP_ENVIRONMENT-collection-be-final/Dockerfile
    APP_NAME_GIT: collection-be
    APP_NAME: collection-api
    APP_ENVIRONMENT: sandbox
    ARTIFACT_PATH: $APP_ENVIRONMENT-collection-be-final
  rules: *main_branch_rules
  dependencies:
    - BuildSandboxCollectionBE

DockerSandboxCollectionBE:
  <<: *imageBuild 
  stage: docker-sandbox
  variables:
    APP_NAME_GIT: collection-be
    APP_NAME: collection-api
    APP_ENVIRONMENT: sandbox
  rules: *main_branch_rules
  dependencies:
    - GitlabBuildSandboxCollectionBE

DeploySandboxCollectionBE:
  <<: *deploy_k8s
  stage: deploy-api-sandbox
  dependencies: []
  variables:
    APP_NAME_GIT: collection-be
    K8S_CMD: "-f apps/collection-be/k8s/values/sandbox.yaml"
    NAMESPACE: sandbox
    APP_ENVIRONMENT: sandbox
    K8S_DIR: "apps/collection-be/k8s"
    HELM_APP_NAME: collection-api
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/collection-api"
  rules: *main_branch_rules

DeployProdCollectionBE:
  <<: *deploy_k8s
  stage: deploy-api-prod
  dependencies: []
  variables:
    APP_NAME_GIT: collection-be
    HELM_APP_NAME: collection-api
    K8S_CMD: "-f apps/collection-be/k8s/values/production.yaml"
    APP_ENVIRONMENT: sandbox
    IMAGE_REPO: "europe-west1-docker.pkg.dev/daring-keep-433720-r2/indicina/collection-api"
    K8S_DIR: "apps/collection-be/k8s"
    NAMESPACE: production
  rules: *main_branch_rules

###########-collection-be end




