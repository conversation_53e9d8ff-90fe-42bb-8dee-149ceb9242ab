import { DataSource } from 'apollo-datasource';
import { addDays } from 'date-fns';
import csv<PERSON><PERSON><PERSON><PERSON> from 'csvtojson/v2';
import { capitalize } from 'lodash';
import currency from 'currency.js';
import {
  ACCOUNT_TYPES,
  APPLICATION_HISTORY_ACTION,
  APPLICATION_STATUSES,
  APPLICATION_STEPS,
  APPLICATION_STEP_VALUES,
  ATTRIBUTES,
  DURATION_TYPES,
  ENTITIES,
  EVENTS,
  GENERIC_STATUSES,
  isTest,
  LOAN_TYPES,
  POLICY_STATUSES,
  PORTFOLIO_METADATA_KEYS,
  PORTFOLIO_STATUSES,
  REPAYMENT_SERVICES,
  REVIEW_FEEDBACK_TYPE,
  WEBHOOKS_EVENTS,
  DOCUMENT_UPLOAD_TYPE,
  INDICINA_CDN_URL,
  GCS_BUCKET,
} from '~/config';
import { redisClient } from '~/redis';
import { errorLogger, infoLogger } from '~/logger';
import { igniteEventEmitters } from '~/lib';
import { formatAmount, getLoanDurationFromApplication } from '~/lib/mail';
import { portfolioCreation } from '~/components/Portfolio/methods/events/generateWebhookEventData';
import { getInterestRate, hasTrueProperty } from '~/lib/utils';
import { publishWebhookEvent } from '~/events/publishers/webhook/webhookEvent.publisher';
import { publishDisburseLoanEvent } from '~/events/publishers/webhook/disburseLoan.publisher';
import { publishBankStatementUploadedEvent } from '~/events/publishers/application';
import {
  CARD_REFERENCE_STATUS,
  CARD_TRANSACTION_META_TYPES,
  POLICY_PROCESSING_FEE_DEBIT_METHOD,
  SCORECARD_APPROVAL_TYPES,
} from '~/config/app';
import generateWebhookEventData from '../../methods/events/generateWebhookEventData';
import ApplicationEventListener from '../../methods/events/ApplicationEventListener';
import * as Utils from '../../utils';

const cuid = require('cuid');

const { transformLoanDuration } = Utils;

const createUniqueNumber = (text) => {
  const dateString = new Date()
    .toISOString()
    .replace(new RegExp('\\D', ['g']), '')
    .substring(0, 12);

  const rand4 = Math.floor(Math.random() * (9999 - 1111) + 1111);

  return `${text}-${dateString}-${rand4}`;
};

const selectBestResult = (compareResult, existingResult) => {
  let finalResult = existingResult || compareResult;

  if (existingResult && existingResult.isDeferred && !compareResult.isDeferred) finalResult = compareResult;

  return finalResult;
};

const getApplicationStatusIsNotMutable = status => status !== APPLICATION_STATUSES.pending.name && status !== APPLICATION_STATUSES.underReview.name;

const { Application } = EVENTS;
const { Application: entityName } = ENTITIES;

export default class ApplicationAPI extends DataSource {
  constructor({ methods }) {
    super();
    this.methods = methods;
    this.utils = Utils;

    if (!isTest) {
      ApplicationEventListener();
    }
  }

  /**
   * This is a function that gets called by ApolloServer when being setup.
   * This function gets called with the datasource config including things
   * like caches and context. We'll assign this.context to the request context
   * here, so we can know about the user making requests
   */
  initialize(config) {
    this.context = config.context;
  }

  async getApplicationById(applicationId) {
    return this.context.prisma.application.findUnique({
      where: { id: applicationId },
      include: this.utils.APPLICATION_FIELDS,
    });
  }

  async getApplicationPolicyDetails(applicationId) {
    if (!applicationId) return null;
    const application = await this.context.prisma.application.findUnique({
      where: { id: applicationId },
      select: {
        policyDetails: true,
      },
    });
    return application && application.policyDetails;
  }

  async getLoanAmountFromPolicy({ rates, policy, applicationPolicyDetails, amount, loanDuration }) {
    const {
      dataSources: { RepaymentAPI },
    } = this.context;

    let _rates = rates || [{}];
    let processingFee;
    let fullAmount = amount;
    let chargesAmount = 0;
    let repaymentBreakdown = [];

    if (policy) {
      const interestRate = getInterestRate({ applicationPolicyDetails, policy, amount });
      _rates = [interestRate];

      if (policy.taxRate) _rates.push(policy.taxRate);
      ({ processingFee } = policy);

      ({
        totalExpectedPayment: fullAmount,
        totalInterestPortion: chargesAmount,
        repaymentBreakdown,
      } = await RepaymentAPI.getRepaymentBreakdown({
        applicationPolicyDetails,
        policyId: policy.id,
        principalAmount: amount,
        duration: loanDuration,
      }));
    }

    // Apply rates to amount
    return {
      ...this.methods.applyLoanRates({
        amount,
        rates: _rates,
        applyDiscount: false,
        processingFee,
      }),
      fullAmount,
      chargesAmount,
      repaymentBreakdown,
    };
  }

  async hasUploadedPdfBankStatement(applicationId) {
    const bankStatement = await this.context.prisma.bankStatementFile.findFirst({
      where: { application: { id: applicationId } },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return !!bankStatement;
  }

  async getUploadedBankStatementByApplicationId(applicationId) {
    const bankStatement = await this.context.prisma.bankStatementFile.findFirst({
      where: {
        application: { id: applicationId },
        file: {
          url: {
            endsWith: '.pdf',
          },
        },
      },
      include: {
        file: true,
        bank: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (bankStatement) {
      bankStatement.bankStatementPdf = await this.context.dataSources.GcsAPI.getObject(
        bankStatement.file.bucket,
        bankStatement.file.key,
      );
    }

    return bankStatement != null && bankStatement.file != null && bankStatement.file.key.split('/')[1] === 'mbs'
      ? null
      : bankStatement;
  }

  async createLoanApplication({
    customerAccountUser,
    clientId,
    amount,
    loanDuration: _loanDuration,
    durationType = DURATION_TYPES.daily.name,
    dayOfRepayment,
    metadata = {},
    policy,
    loanCategoryId,
    source,
  }) {
    const {
      dataSources: { LoanCategoryAPI },
      prisma,
    } = this.context;

    // check if accountUser is a customer of the supplied clientId
    if (customerAccountUser.account.parentAccount.id !== clientId) {
      throw new Error('customer account not found under specified clientId');
    }

    if (_loanDuration < 1) throw new Error('Loan duration cannot be less than 1');

    const durationMeta = transformLoanDuration({
      duration: _loanDuration,
      durationType,
    });
    const { duration: loanDuration } = durationMeta;
    const dateOfRepayment = addDays(new Date(), loanDuration);

    const {
      baseAmount,
      fullAmount,
      chargesAmount,
      taxAmount,
      processingFee,
      // eslint-disable-next-line no-unused-vars
      discountAmount,
    } = await this.getLoanAmountFromPolicy({ amount, loanDuration });

    const create = []; // { key: 'durationMeta', value: JSON.stringify(durationMeta) }
    const metaKeys = Object.keys(metadata);
    metaKeys.forEach((key) => {
      create.push({ key, value: JSON.stringify(metadata[key]) });
    });

    let isValidLoanCategory = false;

    if (loanCategoryId != null) {
      const loanCategory = await LoanCategoryAPI.validateLoanCategory({
        loanCategoryId,
        clientId,
      });
      isValidLoanCategory = loanCategory.id === loanCategoryId;
    }

    const application = await prisma.application.create({
      data: {
        account: { connect: { id: customerAccountUser.account.id } },
        status: { connect: { name: APPLICATION_STATUSES.pending.name } },
        policy: policy ? { connect: { id: policy.id } } : undefined,
        loanCategory: isValidLoanCategory ? { connect: { id: loanCategoryId } } : undefined,
        amount,
        baseAmount,
        fullAmount,
        taxAmount,
        chargesAmount,
        loanDuration,
        dateOfRepayment,
        dayOfRepayment,
        meta: { create },
        metadata: {
          durationMeta,
          ...metadata,
        },
        processingFee,
        applicationNumber: createUniqueNumber('APP'),
        source,
      },
      include: this.utils.APPLICATION_FIELDS,
    });

    igniteEventEmitters({
      singletonEmit: {
        event: Application.created.emit,
        payload: { id: application.id, entityName, data: application, event: Application.created.key },
      },
    });

    publishWebhookEvent({
      data: generateWebhookEventData({ application }),
      status: GENERIC_STATUSES.success,
      message: WEBHOOKS_EVENTS.application.creation.message,
      eventType: WEBHOOKS_EVENTS.application.creation.name,
      accountId: clientId,
    });

    return application;
  }

  async updateApplicationPolicy({ clientId, applicationId, policyId, accountUser, comment, sendUserNotification }) {
    const {
      prisma,
      dataSources: { AccountAPI, ApplicationStepsAPI, LoanCategoryAPI, PolicyAPI },
    } = this.context;

    const applications = await prisma.application.findMany({
      where: {
        id: applicationId,
        account: {
          parentAccount: { id: clientId },
        },
      },
      include: {
        status: true,
        policy: {
          select: { id: true },
        },
        account: {
          select: {
            id: true,
            parentAccount: { select: { id: true } },
          },
        },
        bankAccount: { select: { id: true } },
        policyDetails: true,
        collectionMethod: true,
      },
    });

    const policies = await prisma.policy.findMany({
      where: {
        id: policyId,
        account: { id: clientId },
      },
      include: PolicyAPI.utils.POLICY_FIELDS,
    });

    if (policies.length === 0) throw new Error('invalid policyId specified');
    if (applications.length === 0) throw new Error('invalid applicationId specified');
    const policy = policies[0];
    const _application = applications[0];

    if (policy.status !== POLICY_STATUSES.active) throw new Error('cannot set inactive/disabled policy');
    if (
      _application.status.name === APPLICATION_STATUSES.approved.name
      || _application.status.name === APPLICATION_STATUSES.denied.name
      || _application.status.name === APPLICATION_STATUSES.abandoned.name
    ) {
      throw new Error(`cannot change policy for already ${_application.status.name} loan`);
    }
    if (_application.policy && _application.policy.id === policyId) throw new Error('cannot set new policy to old one');

    let accountAttributes;

    const attributesWhere = {
      name: {
        in: [
          ATTRIBUTES.noCardImplementation,
          ATTRIBUTES.requiresExternalDisbursement,
          ATTRIBUTES.allowCardSkipDuringApplication,
        ],
      },
    };

    const loanCategory = await LoanCategoryAPI.getLoanCategoryByPolicyId({ policyId });

    if (loanCategory?.id) {
      accountAttributes = await prisma.loanCategoryAttribute.findMany({
        where: {
          attribute: attributesWhere,
          loanCategory: { id: loanCategory.id },
        },
        include: {
          attribute: true,
        },
      });
    } else {
      accountAttributes = await prisma.accountAttribute.findMany({
        where: {
          attribute: attributesWhere,
          account: { id: clientId },
        },
        include: {
          attribute: true,
        },
      });
    }

    const { value: noCardImplementation } = accountAttributes.find(({ attribute }) => attribute.name === ATTRIBUTES.noCardImplementation) || {};
    const { value: requiresExternalDisbursement } = accountAttributes.find(({ attribute }) => attribute.name === ATTRIBUTES.requiresExternalDisbursement) || {};
    const { value: allowCardSkipDuringApplication } = accountAttributes.find(({ attribute }) => attribute.name === ATTRIBUTES.allowCardSkipDuringApplication) || {};

    const { collectionMethod } = _application;
    const skippedCardCollection = collectionMethod && collectionMethod.name !== REPAYMENT_SERVICES.card;

    if (noCardImplementation !== 'true' && !skippedCardCollection) {
      const addedCard = await prisma.accountCard.findFirst({
        where: {
          account: { id: _application.account.id },
        },
      });
      if (!addedCard && !allowCardSkipDuringApplication) throw new Error('This application does not have a bank card attached to it');
    }

    if (requiresExternalDisbursement !== 'true') {
      const { bankAccount } = _application || {};
      if (!bankAccount) throw new Error('This application does not have a bank account attached to it');
    }

    // Apply rates to amount
    const { baseAmount, fullAmount, chargesAmount, taxAmount, processingFee } = await this.getLoanAmountFromPolicy({
      policy,
      amount: _application.amount,
      loanDuration: _application.loanDuration,
    });

    let policyDetails;

    const { loanType } = policy;
    if (loanType && loanType.name === LOAN_TYPES.graduated.name) {
      const glCycle = policy.graduatedLoanCycles.find(({ amount: _amount }) => _amount === _application.amount);
      if (!glCycle) throw new Error('Unexpected Error: Invalid amount on graduated loan');
      policyDetails = {
        interestRateCalcBy: 'PERCENTAGE',
        interestRateValue: glCycle.interestRate,
      };
    } else {
      policyDetails = {
        interestRateCalcBy: policy.interestRate.calcBy,
        interestRateValue: policy.interestRate.value,
      };
    }

    await prisma.$transaction([
      prisma.applicationHistory.create({
        data: {
          application: { connect: { id: applicationId } },
          actionBy: accountUser ? { connect: { id: accountUser.id } } : undefined,
          comment,
          oldPolicy: _application.policy ? { connect: { id: _application.policy.id } } : undefined,
          newPolicy: { connect: { id: policyId } },
          metadata: {
            oldData: {
              baseAmount: _application.baseAmount,
              fullAmount: _application.fullAmount,
              chargesAmount: _application.chargesAmount,
              taxAmount: _application.taxAmount,
              processingFee: _application.processingFee,
            },
          },
        },
      }),
      prisma.application.update({
        data: {
          baseAmount,
          fullAmount,
          chargesAmount,
          taxAmount,
          processingFee,
          policyDetails: {
            upsert: {
              create: policyDetails,
              update: policyDetails,
            },
          },
          policy: { connect: { id: policyId } },
          loanCategory: loanCategory?.id ? { connect: { id: loanCategory.id } } : undefined,
        },
        where: {
          id: applicationId,
        },
      }),
    ]);

    // Send user application policy updated notice
    const clientAccountUser = await AccountAPI.getAccountUserById({
      id: _application.account.parentAccount.id,
      asClient: true,
    });
    const customerAccountUser = await AccountAPI.getAccountUserById({
      id: _application.account.id,
      clientId: clientAccountUser.account.id,
    });

    if (sendUserNotification) {
      try {
        const lateFeeCalcBy = policy.lateFeeCalculation.rate && policy.lateFeeCalculation.rate.calcBy === 'PERCENTAGE' ? '%' : 'NGN';
        const lateFeeValue = policy.lateFeeCalculation.rate && policy.lateFeeCalculation.rate.value;
        // Send user notification email
        this.sendApplicationPolicyUpdatedNotification({
          loanAmount: baseAmount,
          repaymentAmount: fullAmount,
          clientAccountUser,
          customerAccountUser,
          interestRate: policy.interestRate.value,
          lateFee: `${lateFeeValue} ${lateFeeCalcBy}`,
        });
      } catch (e) {
        // intentionally left blank
      }
    }

    if (policy.shouldRefetchModelResults) {
      try {
        // Refetch model manager results
        const application = await this.getApplicationById(applicationId);
        ApplicationStepsAPI.completeModelManagerRequest({
          application,
          clientId,
          accountUser: customerAccountUser,
        });
      } catch (e) {
        // intentionally left blank
      }
    }

    return { success: true };
  }

  async updateApplicationLoanDuration({
    applicationId,
    policy,
    accountUser,
    loanDuration: _loanDuration,
    durationType: _durationType,
    metadata: _existingMeta,
    oldApplicationData = {},
  }) {
    const {
      dataSources: { AccountAPI },
      prisma,
    } = this.context;

    if (_loanDuration < 1) throw new Error('Loan duration cannot be less than 1');
    if (!_existingMeta) throw new Error('Existing metadata must be provided to make this update');

    let metadata = _existingMeta;
    let loanDuration = _loanDuration;
    let durationType = _durationType;

    // Ensure policy is applicable to specified duration
    const {
      minLoanDuration: policyMinLoanDuration,
      maxLoanDuration: policyMaxLoanDuration,
      durationType: { name: policyDurationType },
    } = policy;
    const loanDurationMeta = transformLoanDuration({
      duration: _loanDuration,
      durationType: _durationType,
    });

    const { duration: minLoanDurationDays } = transformLoanDuration({
      duration: policyMinLoanDuration,
      durationType: policyDurationType,
    });

    const { duration: maxLoanDurationDays, durationTypeDisplayName } = transformLoanDuration({
      duration: policyMaxLoanDuration,
      durationType: policyDurationType,
    });

    if (loanDurationMeta.duration < minLoanDurationDays || loanDurationMeta.duration > maxLoanDurationDays) {
      throw new Error(
        `Loan duration must be between ${policyMinLoanDuration} and ${policyMaxLoanDuration} ${durationTypeDisplayName} inclusive`,
      );
    }

    if (oldApplicationData.loanDuration && oldApplicationData.loanDuration !== loanDuration) {
      loanDuration = loanDurationMeta.duration;
      durationType = loanDurationMeta.type;
      metadata = {
        ...metadata,
        durationMeta: loanDurationMeta,
      };
    }

    let dateOfRepayment;
    if (durationType !== DURATION_TYPES.oneOff.name) {
      dateOfRepayment = addDays(new Date(), loanDuration);
    }

    const variables = {
      loanDuration,
      durationType,
      dateOfRepayment,
      metadata,
      comment: 'Updated loan application duration',
      oldStatus: oldApplicationData.status.name,
      newStatus: oldApplicationData.status.name,
      historyMetadata: {
        oldData: oldApplicationData,
        newData: {
          loanDuration,
          status: oldApplicationData.status.name,
        },
      },
      actionBy: accountUser ? { connect: { id: accountUser.id } } : undefined,
      action: APPLICATION_HISTORY_ACTION.durationUpdateAction,
    };

    const [updateApplication] = await prisma.$transaction([
      prisma.application.update({
        data: {
          status: { connect: { name: variables.newStatus } },
          loanDuration: variables.loanDuration,
          dateOfRepayment: variables.dateOfRepayment,
          metadata: variables.metadata,
        },
        where: { id: applicationId },
        include: this.utils.APPLICATION_FIELDS,
      }),
      prisma.applicationHistory.create({
        data: {
          comment: variables.comment,
          metadata: variables.historyMetadata,
          application: { connect: { id: applicationId } },
          oldStatus: { connect: { name: variables.oldStatus } },
          newStatus: { connect: { name: variables.newStatus } },
          actionBy: variables.actionBy,
          action: variables.action,
        },
      }),
    ]);

    if (updateApplication) {
      const clientAccountUser = await AccountAPI.getAccountUserById({
        id: updateApplication.account.parentAccount.id,
        asClient: true,
      });

      const customerAccountUser = await AccountAPI.getAccountUserById({
        id: updateApplication.account.id,
        clientId: clientAccountUser.account.id,
      });

      this.sendApplicationLoanUpdatedNotification({ clientAccountUser, customerAccountUser });
    }

    return updateApplication;
  }

  async sendApplicationLoanUpdatedNotification({ clientAccountUser, customerAccountUser }) {
    const {
      methods: { getApplicationLoanDurationUpdatedNotification },
      context: {
        dataSources: { ClientAccountAPI },
      },
    } = this;

    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientAccountUser.account.id,
    });

    return getApplicationLoanDurationUpdatedNotification({
      customerAccountUser,
      clientOptions,
    }).send();
  }

  async modifyInterestRate({ accountUser, application, interestRate, calcBy }) {
    const {
      prisma,
      dataSources: { ClientAccountAPI, AccountAPI },
    } = this.context;

    const interestPolicyDetails = {
      interestRateCalcBy: calcBy,
      interestRateValue: interestRate,
    };

    const { chargesAmount, fullAmount } = await this.getLoanAmountFromPolicy({
      policy: application.policy,
      amount: application.baseAmount,
      loanDuration: application.loanDuration,
      applicationPolicyDetails: interestPolicyDetails,
    });

    const updatedApplication = await prisma.application.update({
      where: {
        id: application.id,
      },
      data: {
        chargesAmount,
        fullAmount,
        policyDetails: {
          upsert: {
            create: {
              ...interestPolicyDetails,
            },
            update: {
              ...interestPolicyDetails,
            },
          },
        },
        history: {
          create: {
            actionBy: { connect: { id: accountUser.id } },
            action: APPLICATION_HISTORY_ACTION.interestRateModification,
            oldStatus: { connect: { name: application.status.name } },
            newStatus: { connect: { name: application.status.name } },
            comment: `Updated application interest rate to ${interestRate}`,
          },
        },
      },
      include: this.utils.APPLICATION_FIELDS,
    });

    if (updatedApplication) {
      const { getApplicationInterestRateModifiedEmail } = this.methods;

      const clientOptions = await ClientAccountAPI.getEmailOptions({
        accountId: accountUser.account.id,
      });
      const customerAccountUser = await AccountAPI.getAccountUserById({
        id: updatedApplication.account.id,
        clientId: accountUser.account.id,
      });

      getApplicationInterestRateModifiedEmail({
        customerAccountUser,
        clientOptions,
      }).send();
    }

    return updatedApplication;
  }

  async denyApplication({ application: app, comment, metadata = {}, accountUser }) {
    const {
      prisma,
      dataSources: { AccountAPI, ClientAccountAPI, LoanCategoryAPI, MessageAPI },
    } = this.context;
    const application = await this.getApplicationById(app.id);
    const { status, account } = application;

    if (status.name === APPLICATION_STATUSES.approved.name) {
      throw new Error('cannot deny an already approved application');
    }

    if (status.name === APPLICATION_STATUSES.denied.name) {
      throw new Error('application is already denied');
    }

    const { id: loanCategoryId } = application.policy != null
      ? await LoanCategoryAPI.getLoanCategoryByPolicyId({ policyId: application.policy.id })
      : {};
    const { data: { allowAction } = {} } = (await ClientAccountAPI.getAttributeByName({
      accountId: account.parentAccount.id,
      attributeName: ATTRIBUTES.abandonApplication,
      loanCategoryId,
    })) || {};

    if (status.name === APPLICATION_STATUSES.abandoned.name && !allowAction) {
      throw new Error('cannot deny an already abandoned application');
    }

    if (application.portfolio) {
      if (application.portfolio.status.name !== PORTFOLIO_STATUSES.onHold.name) {
        throw new Error('You cannot reject this application');
      }
      await prisma.portfolio.update({
        data: {
          status: { connect: { name: PORTFOLIO_STATUSES.closed.name } },
        },
        where: { id: application.portfolio.id },
      });
      await prisma.portfolioHistory.create({
        data: {
          comment,
          portfolio: { connect: { id: application.portfolio.id } },
          oldStatus: { connect: { name: PORTFOLIO_STATUSES.onHold.name } },
          newStatus: { connect: { name: PORTFOLIO_STATUSES.closed.name } },
          actionBy: accountUser ? { connect: { id: accountUser.id } } : undefined,
        },
      });
    }

    const variables = {
      applicationId: application.id,
      newStatus: APPLICATION_STATUSES.denied.name,
      oldStatus: status.name,
      comment,
      metadata: { ...(application.metadata || {}), ...metadata },
      action: APPLICATION_HISTORY_ACTION.statusUpdateAction,
    };

    if (accountUser) {
      variables.actionBy = { connect: { id: accountUser.id } };
    }

    if (application.approvalWorkflow && application.approvalWorkflow.nextStep) {
      variables.oldAWStep = { connect: { id: application.approvalWorkflow.nextStep.id } };
      await prisma.application.update({
        data: {
          approvalWorkflow: {
            update: {
              nextStep: { disconnect: true },
            },
          },
        },
        where: {
          id: application.id,
        },
      });
    }

    const [updateApplication] = await prisma.$transaction([
      prisma.application.update({
        data: {
          status: { connect: { name: variables.newStatus } },
          loanDuration: variables.loanDuration,
          dateOfRepayment: variables.dateOfRepayment,
          metadata: variables.metadata,
        },
        where: { id: variables.applicationId },
        include: this.utils.APPLICATION_FIELDS,
      }),
      prisma.applicationHistory.create({
        data: {
          comment: variables.comment,
          metadata: variables.metadata,
          application: { connect: { id: variables.applicationId } },
          oldStatus: { connect: { name: variables.oldStatus } },
          newStatus: { connect: { name: variables.newStatus } },
          actionBy: variables.actionBy,
          action: variables.action,
          oldAWStep: variables.oldAWStep,
        },
      }),
    ]);

    // Send user application denied notice
    const clientAccountUser = await AccountAPI.getAccountUserById({
      id: application.account.parentAccount.id,
      asClient: true,
    });
    const customerAccountUser = await AccountAPI.getAccountUserById({
      id: application.account.id,
      clientId: clientAccountUser.account.id,
    });
    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientAccountUser.account.id,
    });

    this.sendApplicationDeniedNotification({
      clientAccountUser,
      customerAccountUser,
      application,
      rejectReason: comment,
      clientOptions,
    });
    this.sendClientApplicationDeniedEmail({
      clientAccountUser,
      customerAccountUser,
      application,
      comment,
      status: updateApplication.status.name,
      clientOptions,
    });

    const { slug } = clientAccountUser.account;

    const notificationPreference = clientOptions?.customerNotificationPreference;

    if (notificationPreference && notificationPreference?.applicationDenied?.sms === true) {
      MessageAPI.sendMessage({
        to: customerAccountUser.user.phone,
        message: `We regret to inform you that your application has been denied. Check your dashboard for the reason. ${capitalize(slug)}`,
        slug,
        clientId: clientAccountUser.account.id,
      });
    }

    return {
      ...application,
      status: updateApplication.status,
      approvalWorkflow: updateApplication.approvalWorkflow,
    };
  }

  async deferApplication({ application, comment, metadata, accountUser, doStatusCheck = true }) {
    const {
      prisma,
      dataSources: { AccountAPI, ApprovalWorkflowAPI, ReviewFeedbackAPI },
    } = this.context;
    const { status, policy } = application;

    if (status.name === APPLICATION_STATUSES.underReview.name && policy) {
      return application;
    }

    if (status.name === APPLICATION_STATUSES.approved.name) {
      throw new Error('cannot manually review an already approved application');
    }

    if (doStatusCheck && status.name === APPLICATION_STATUSES.denied.name) {
      throw new Error('application is already denied');
    }

    const clientAccountUser = await AccountAPI.getAccountUserById({
      id: application.account.parentAccount.id,
      asClient: true,
    });

    // get approval workflow
    let approvalWorkflow = policy && policy.approvalWorkflow;

    if (!approvalWorkflow) {
      // get client approval workflow
      const [approvalWorkflowAttrib] = await prisma.accountAttribute.findMany({
        where: {
          attribute: { name: ATTRIBUTES.approvalWorkflow },
          account: { id: clientAccountUser.account.id },
        },
      });
      if (approvalWorkflowAttrib && approvalWorkflowAttrib.value) {
        approvalWorkflow = await prisma.approvalWorkflow.findUnique({
          where: { id: approvalWorkflowAttrib.value },
          include: {
            steps: {
              include: {
                roles: {
                  select: { id: true },
                },
                permissions: {
                  select: { id: true },
                },
              },
            },
            client: {
              select: {
                id: true,
              },
            },
          },
        });
      }
    }

    let approvalWorkflowUpdate = null;
    let firstStep;

    // When deferring, If there is an approval workflow, initialize application workflow step
    if (approvalWorkflow) {
      firstStep = approvalWorkflow.steps.find(({ orderNo }) => orderNo === 1);
      approvalWorkflowUpdate = {
        create: {
          workflow: { connect: { id: approvalWorkflow.id } },
          nextStep: { connect: { id: firstStep.id } },
        },
      };
    }

    const variables = {
      applicationId: application.id,
      newStatus: APPLICATION_STATUSES.underReview.name,
      oldStatus: status.name,
      comment,
      metadata,
      approvalWorkflowUpdate,
      action: APPLICATION_HISTORY_ACTION.statusUpdateAction,
      actionBy: accountUser ? { connect: { id: accountUser.id } } : undefined,
    };

    const [updateApplication] = await prisma.$transaction([
      prisma.application.update({
        data: {
          status: { connect: { name: variables.newStatus } },
          approvalWorkflow: variables.approvalWorkflowUpdate,
        },
        where: { id: variables.applicationId },
        include: this.utils.APPLICATION_FIELDS,
      }),
      prisma.applicationHistory.create({
        data: {
          comment: variables.comment,
          metadata: variables.metadata,
          application: { connect: { id: variables.applicationId } },
          oldStatus: { connect: { name: variables.oldStatus } },
          newStatus: { connect: { name: variables.newStatus } },
          actionBy: variables.actionBy,
          action: variables.action,
          newAWStep: firstStep ? { connect: { id: firstStep.id } } : undefined,
        },
      }),
    ]);

    if (updateApplication.approvalWorkflow) {
      await ApprovalWorkflowAPI.autoAssignNextStep(updateApplication);
    }

    if (application.metadata?.affordabilityPayload) {
      const payload = application.metadata.affordabilityPayload;
      await ReviewFeedbackAPI.updateLoanApplicationDetails({
        ...payload,
        application,
      }).catch((error) => {
        errorLogger('Error while modifying application amount based on affordability', {
          errMsg: error.message,
          payload,
        });
        throw new Error('Error while modifying application amount based on affordability, please try again later');
      });
    }

    const customerAccountUser = await AccountAPI.getAccountUserById({
      id: application.account.id,
      clientId: clientAccountUser.account.id,
    });
    this.sendClientReviewApplicationEmail({
      clientAccountUser,
      customerAccountUser,
      application,
      status: updateApplication.status.name,
    });
    this.sendUserReviewApplicationNotification({
      clientAccountUser,
      customerAccountUser,
      application,
    });

    return {
      ...application,
      status: updateApplication.status,
    };
  }

  async approveApplicationAndCreatePortfolio({ applicationId, accountUser, metadata, requiresManualDisbursal }) {
    const {
      prisma,
      dataSources: {
        AccountAPI,
        ClientAccountAPI,
        CollectionAPI,
        PortfolioMetaAPI,
        OfferLetterAPI,
        ReviewFeedbackAPI,
        LoanCategoryAPI,
        MessageAPI,
      },
    } = this.context;
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      include: this.utils.APPLICATION_FIELDS,
    });
    const {
      id,
      status,
      account,
      policy,
      amount,
      baseAmount,
      fullAmount,
      taxAmount,
      chargesAmount,
      loanDuration,
      dayOfRepayment,
      processingFee,
    } = application;

    const { name: clientName } = application.account.parentAccount;
    const { id: loanCategoryId } = application.policy != null
      ? await LoanCategoryAPI.getLoanCategoryByPolicyId({ policyId: application.policy.id })
      : {};
    const { options = {} } = (await ClientAccountAPI.getMerchantConfigByName(clientName, loanCategoryId)) || {};
    const {
      requiresEventTrigger = false,
      requiresExternalDisbursement = false,
      requiresRemitaMandate,
      requiresNibssCollection,
    } = options;

    const clientId = account.parentAccount.id;
    const { data: { allowAction } = {} } = (await ClientAccountAPI.getAttributeByName({
      accountId: clientId,
      attributeName: ATTRIBUTES.abandonApplication,
      loanCategoryId,
    })) || {};

    if (!application.policy) {
      throw new Error('cannot approve an application with no policy assigned');
    }

    if (status.name === APPLICATION_STATUSES.abandoned.name && !allowAction) {
      throw new Error('cannot approve an already abandoned application');
    }

    if (status.name === APPLICATION_STATUSES.denied.name) {
      throw new Error('cannot approve an already denied application');
    }

    // when approving a loan that is awaiting feedback, system should complete the feedback process
    if (status.name === APPLICATION_STATUSES.awaitingFeedback.name) {
      await ReviewFeedbackAPI.completeReviewFeedbacks({ applicationId, accountUser, appStatus: status.name });
    }

    let nibssMandateId;

    if (status.name !== APPLICATION_STATUSES.approved.name) {
      if (requiresNibssCollection) {
        const preApprovalCheck = await CollectionAPI.doPreApprovalCheck({ applicationId });
        nibssMandateId = preApprovalCheck?.nibssMandateId;
        if (!preApprovalCheck.activated) {
          await prisma.application.update({
            data: {
              status: { connect: { name: APPLICATION_STATUSES.underReview.name } },
              metadata: {
                nibssMandateId,
                ...(application.metadata && Object.keys(application.metadata).length ? application.metadata : {}),
              },
              history: {
                create: {
                  comment: `Attempted Approval: ${preApprovalCheck.errReason}`,
                  oldStatus: { connect: { name: status.name } },
                  newStatus: { connect: { name: APPLICATION_STATUSES.underReview.name } },
                  actionBy: accountUser ? { connect: { id: accountUser.id } } : undefined,
                  action: APPLICATION_HISTORY_ACTION.statusUpdateAction,
                },
              },
            },
            where: { id: applicationId },
          });
          if (nibssMandateId && application.portfolio) {
            await prisma.portfolio.update({
              data: {
                nibssMandateId,
              },
              where: {
                id: application.portfolio.id,
              },
            });
          }
          throw new Error(preApprovalCheck.errReason);
        }
      }
      const hasGeneratedOfferLetter = application.reviewFeedbacks.some(
        review => review.reviewType.name === REVIEW_FEEDBACK_TYPE.offerLetterRequest.name,
      );
      if (!hasGeneratedOfferLetter && application.policy && application.policy.offerLetterEnabled === true) {
        try {
          const offerLetterResult = await OfferLetterAPI.createAndSendOfferLetter({ applicationId }, accountUser);
          return {
            ...offerLetterResult,
            portfolio: null,
            persistAwfStep: true,
          };
        } catch (e) {
          errorLogger('Unable to generate offer letter', {
            errorMessage: e.message,
            errorStacktrace: e.stack,
          });
          throw new Error('An error occurred while trying to generate offer letter');
        }
      }

      if (application.policy.processingFeeDebitMethod === POLICY_PROCESSING_FEE_DEBIT_METHOD.PreApproval) {
        await this.chargeProcessingFeeBeforeApproval({ application });
      }

      const variables = {
        applicationId: id,
        accountId: account.id,
        policy: policy ? { connect: { id: policy.id } } : undefined,
        newStatus: APPLICATION_STATUSES.approved.name,
        oldStatus: status.name,
        portfolioStatus: requiresManualDisbursal
          ? PORTFOLIO_STATUSES.pendingManualDisbursement.name
          : PORTFOLIO_STATUSES.pendingDisbursement.name,
        portfolioNumber: createUniqueNumber('LOAN'),
        amount,
        baseAmount,
        fullAmount,
        chargesAmount,
        taxAmount,
        loanDuration,
        dayOfRepayment,
        metadata,
        action: APPLICATION_HISTORY_ACTION.statusUpdateAction,
        processingFee,
      };

      if (accountUser) {
        variables.actionBy = { connect: { id: accountUser.id } };
      }

      if (status.name === APPLICATION_STATUSES.pending.name) {
        const updateApplicationStatVariables = {
          applicationId: application.id,
          newStatus: APPLICATION_STATUSES.underReview.name,
          oldStatus: status.name,
          actionBy: variables.actionBy,
          action: APPLICATION_HISTORY_ACTION.statusUpdateAction,
        };

        const [initUpdateStatus] = await prisma.$transaction([
          prisma.application.update({
            data: {
              status: { connect: { name: updateApplicationStatVariables.newStatus } },
            },
            where: { id: updateApplicationStatVariables.applicationId },
            include: this.utils.APPLICATION_FIELDS,
          }),
          prisma.applicationHistory.create({
            data: {
              application: { connect: { id: updateApplicationStatVariables.applicationId } },
              oldStatus: { connect: { name: updateApplicationStatVariables.oldStatus } },
              newStatus: { connect: { name: updateApplicationStatVariables.newStatus } },
              actionBy: updateApplicationStatVariables.actionBy,
              action: updateApplicationStatVariables.action,
            },
          }),
        ]);

        if (initUpdateStatus) {
          variables.oldStatus = initUpdateStatus.status.name;
        }
      }

      // eslint-disable-next-line no-unused-vars
      const [updateApplication, createHistory, createPortfolio] = await prisma.$transaction([
        prisma.application.update({
          data: {
            status: { connect: { name: variables.newStatus } },
          },
          where: { id: variables.applicationId },
          select: {
            id: true,
            status: true,
          },
        }),
        prisma.applicationHistory.create({
          data: {
            application: { connect: { id: variables.applicationId } },
            oldStatus: { connect: { name: variables.oldStatus } },
            newStatus: { connect: { name: variables.newStatus } },
            metadata: variables.metadata,
            actionBy: variables.actionBy,
            action: variables.action,
            comment: 'Loan Approved',
          },
        }),
        application.portfolio
          ? prisma.portfolio.findFirst({
            where: {
              id: application.portfolio.id,
            },
            include: {
              account: {
                select: {
                  id: true,
                  name: true,
                  customIdentifier: true,
                  parentAccount: {
                    select: { id: true, name: true },
                  },
                  accountUsers: {
                    select: { user: true },
                  },
                },
              },
              status: true,
              policy: {
                select: { id: true, name: true },
              },
              clientTransactionType: true,
              application: {
                select: {
                  id: true,
                  applicationNumber: true,
                  status: true,
                  loanCategory: {
                    select: { id: true, name: true },
                  },
                },
              },
            },
          })
          : prisma.portfolio.create({
            data: {
              nibssMandateId,
              account: { connect: { id: variables.accountId } },
              policy: variables.policy,
              application: { connect: { id: variables.applicationId } },
              amount: variables.amount,
              status: { connect: { name: variables.portfolioStatus } },
              fullAmount: variables.fullAmount,
              baseAmount: variables.baseAmount,
              taxAmount: variables.taxAmount,
              chargesAmount: variables.chargesAmount,
              loanDuration: variables.loanDuration,
              dateOfRepayment: variables.dateOfRepayment,
              dayOfRepayment: variables.dayOfRepayment,
              portfolioNumber: variables.portfolioNumber,
              processingFee: variables.processingFee,
              loanStatus: { connect: { name: variables.portfolioStatus } },
            },
            include: {
              account: {
                select: {
                  id: true,
                  name: true,
                  customIdentifier: true,
                  parentAccount: {
                    select: { id: true, name: true },
                  },
                  accountUsers: {
                    select: { user: true },
                  },
                },
              },
              status: true,
              policy: {
                select: { id: true, name: true },
              },
              clientTransactionType: true,
              application: {
                select: {
                  id: true,
                  applicationNumber: true,
                  status: true,
                  loanCategory: {
                    select: { id: true, name: true },
                  },
                },
              },
            },
          }),
      ]);

      if (application.metadata && application.metadata.durationMeta) {
        PortfolioMetaAPI.upsertMetadata({
          portfolioId: createPortfolio.id,
          key: PORTFOLIO_METADATA_KEYS.durationMeta,
          value: JSON.stringify(application.metadata.durationMeta),
        });
      }

      const clientAccountUser = await AccountAPI.getAccountUserById({
        id: clientId,
        asClient: true,
      });
      const customerAccountUser = await AccountAPI.getAccountUserById({
        id: application.account.id,
        clientId,
      });

      const [betaMeta] = await prisma.userMetadata.findMany({
        where: { user: { id: customerAccountUser.user.id }, name: ATTRIBUTES.betaMetaKey },
      });

      igniteEventEmitters({
        singletonEmit: {
          event: Application.approved.emit,
          payload: {
            id: application.id,
            entityName,
            data: { ...application, accountId: betaMeta != null ? betaMeta.value : undefined },
            event: Application.approved.key,
          },
        },
      });
      publishWebhookEvent({
        data: generateWebhookEventData({ application }),
        status: GENERIC_STATUSES.success,
        message: WEBHOOKS_EVENTS.application.approval.message,
        eventType: WEBHOOKS_EVENTS.application.approval.name,
        accountId: clientId,
      });

      const clientOptions = await ClientAccountAPI.getEmailOptions({
        accountId: clientAccountUser.account.id,
      });

      this.sendApplicationApprovedNotification({
        clientAccountUser,
        customerAccountUser,
        application,
        requiresMandate: requiresRemitaMandate || application.collectionMethod?.name === REPAYMENT_SERVICES.remita,
        clientOptions,
      });
      this.sendClientApplicationApprovedEmail({
        clientAccountUser,
        customerAccountUser,
        application,
        status: updateApplication.status.name,
        clientOptions,
      });

      const { slug } = clientAccountUser.account;

      const notificationPreference = clientOptions?.customerNotificationPreference;

      if (notificationPreference && notificationPreference?.applicationApproved?.sms === true) {
        MessageAPI.sendMessage({
          to: customerAccountUser.user.phone,
          message: `Congrats! Your loan of ${
            variables.amount
          } is approved. Check your dashboard to confirm if you need to activate mandate for funds disbursement. ${capitalize(slug)}`,
          slug,
          clientId,
        });
      }

      infoLogger(`MERCHANT ${clientAccountUser.account.name} REQUIRES EXTERNAL SERVICE : `, {
        requiresEventTrigger,
      });

      if (!requiresManualDisbursal && !requiresEventTrigger && !requiresExternalDisbursement) {
        publishDisburseLoanEvent({
          id: createPortfolio.id,
          accountBankId: application.bankAccount.id,
        });
      }

      publishWebhookEvent({
        data: portfolioCreation({ portfolio: createPortfolio }),
        status: GENERIC_STATUSES.success,
        message: WEBHOOKS_EVENTS.portfolio.creation.message,
        eventType: WEBHOOKS_EVENTS.portfolio.creation.name,
        accountId: createPortfolio.account.parentAccount.id,
      });

      return {
        success: true,
        application: {
          ...application,
          status: updateApplication.status,
        },
        portfolio: createPortfolio,
      };
    }

    // application has already been approved

    // find portfolio for approved application
    // TODO: update porfolio fragment
    const portfolio = await prisma.application.findUnique({ where: { id: application.id } }).portfolio();

    return {
      application,
      portfolio,
    };
  }

  async setRequiredSteps({ id, steps: _steps = [] }) {
    const { prisma } = this.context;
    const reducedSteps = _steps.map(el => el.value || el);
    const steps = [...new Set(reducedSteps)];

    if (steps.length === 0) throw new Error('At least one step should be specified');
    const data = [];
    steps.forEach(
      step => APPLICATION_STEP_VALUES.indexOf(step) !== -1
        && data.push({
          value: step,
          position: steps.indexOf(step) + 1,
        }),
    );

    await prisma.$transaction([
      prisma.application_requiredSteps.deleteMany({
        where: {
          nodeId: id,
        },
      }),
      prisma.application.update({
        where: { id },
        data: {
          requiredSteps: {
            createMany: {
              data,
            },
          },
        },
      }),
    ]);
  }

  async setCompletedSteps({ id, steps: _steps = [] }) {
    const operationLockKey = `setCompletedSteps-${id}`;

    try {
      await redisClient.acquireLock(operationLockKey, 30);
      const { prisma } = this.context;
      const application = await prisma.application.findUnique({ where: { id }, select: { completedSteps: true } });
      const existingSteps = application.completedSteps || [];
      const reducedSteps = [...existingSteps, ..._steps].map(el => el.value || el);
      const steps = [...new Set(reducedSteps)];

      if (steps.length === 0) throw new Error('At least one step should be specified');

      const data = [];
      steps.forEach(
        step => APPLICATION_STEP_VALUES.indexOf(step) !== -1
          && data.push({
            value: step,
            position: steps.indexOf(step) + 1,
          }),
      );

      await prisma.$transaction([
        prisma.application_completedSteps.deleteMany({
          where: {
            nodeId: id,
          },
        }),
        prisma.application.update({
          where: { id },
          data: {
            completedSteps: {
              createMany: {
                data,
              },
            },
          },
        }),
      ]);
    } catch (error) {
      errorLogger('Error setting completed steps', { msg: error.message });
      throw error;
    } finally {
      await redisClient.releaseLock(operationLockKey);
    }
  }

  async sendClientReviewApplicationEmail({ clientAccountUser, customerAccountUser, application, status }) {
    const {
      methods: { getReviewApplicationEmail },
      context: {
        dataSources: { ClientAccountAPI, UserAPI },
      },
    } = this;
    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientAccountUser.account.id,
    });
    const { value: monthlySalary } = await UserAPI.getUserMetaDataByName({
      userId: customerAccountUser.user.id,
      metaName: 'netIncome',
    });
    return getReviewApplicationEmail({
      client: clientAccountUser.user,
      customerAccountUser,
      application,
      loanDuration: getLoanDurationFromApplication(application),
      clientOptions,
      monthlySalary,
      status,
    }).send();
  }

  async sendUserReviewApplicationNotification({ clientAccountUser, customerAccountUser, application }) {
    const {
      methods: { getReviewApplicationNotificationEmail },
      context: {
        dataSources: { ClientAccountAPI, MessageAPI },
      },
    } = this;
    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientAccountUser.account.id,
    });

    const notificationPreference = clientOptions?.customerNotificationPreference;

    if (notificationPreference && notificationPreference?.applicationReview?.sms === true) {
      MessageAPI.sendMessage({
        slug: clientOptions.slug,
        to: customerAccountUser.user.phone,
        message: `Hello ${customerAccountUser.user.firstName},\nyour application is completed and is now under review. ${clientOptions.slug} Team`,
        clientId: clientAccountUser.account.id,
      });
    }

    if (notificationPreference && notificationPreference?.applicationReview?.email === false) return;

    getReviewApplicationNotificationEmail({
      customerAccountUser,
      clientOptions,
      loanAmount: application.baseAmount,
    }).send();
  }

  async sendApplicationApprovedNotification({ customerAccountUser, application, requiresMandate, clientOptions }) {
    const {
      methods: { getApplicationApprovedNotification },
    } = this;

    const notificationPreference = clientOptions?.customerNotificationPreference;

    if (notificationPreference && notificationPreference?.applicationApproved?.email === false) return;

    getApplicationApprovedNotification({
      customerAccountUser,
      clientOptions,
      loanAmount: application.baseAmount,
      requiresMandate,
    }).send();
  }

  async sendClientApplicationApprovedEmail({
    clientAccountUser,
    customerAccountUser,
    application,
    status,
    clientOptions,
  }) {
    const {
      methods: { getClientApplicationApprovedEmail },
      context: {
        dataSources: { UserAPI },
      },
    } = this;
    const { value: monthlySalary } = await UserAPI.getUserMetaDataByName({
      userId: customerAccountUser.user.id,
      metaName: 'netIncome',
    });

    return getClientApplicationApprovedEmail({
      customerAccountUser,
      clientAccountUser,
      clientOptions,
      loanAmount: application.baseAmount,
      applicationId: application.applicationNumber,
      applicationDate: application.createdAt,
      loanDuration: getLoanDurationFromApplication(application),
      monthlySalary,
      status,
    }).send();
  }

  async sendApplicationDeniedNotification({ customerAccountUser, application, rejectReason, clientOptions }) {
    const {
      methods: { getApplicationDeniedNotification },
    } = this;

    const notificationPreference = clientOptions?.customerNotificationPreference;

    if (notificationPreference && notificationPreference?.applicationDenied?.email === false) return;

    getApplicationDeniedNotification({
      customerAccountUser,
      clientOptions,
      loanAmount: application.baseAmount,
      rejectReason,
    }).send();
  }

  async sendClientApplicationDeniedEmail({
    clientAccountUser,
    customerAccountUser,
    application,
    comment,
    status,
    clientOptions,
  }) {
    const {
      methods: { getClientApplicationDeniedEmail },
      context: {
        dataSources: { UserAPI },
      },
    } = this;
    const { value: monthlySalary } = await UserAPI.getUserMetaDataByName({
      userId: customerAccountUser.user.id,
      metaName: 'netIncome',
    });

    return getClientApplicationDeniedEmail({
      customerAccountUser,
      clientAccountUser,
      clientOptions,
      loanAmount: application.baseAmount,
      applicationId: application.applicationNumber,
      applicationDate: application.createdAt,
      loanDuration: getLoanDurationFromApplication(application),
      comment,
      status,
      monthlySalary,
    }).send();
  }

  async sendApplicationPolicyUpdatedNotification({
    clientAccountUser,
    customerAccountUser,
    loanAmount,
    repaymentAmount,
    interestRate,
    lateFee,
  }) {
    const {
      methods: { getApplicationPolicyUpdatedNotification },
      context: {
        dataSources: { ClientAccountAPI },
      },
    } = this;
    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientAccountUser.account.id,
    });
    return getApplicationPolicyUpdatedNotification({
      customerAccountUser,
      clientOptions,
      loanAmount,
      repaymentAmount,
      interestRate,
      lateFee,
    }).send();
  }

  async completeApplication({ application: _application, accountUser, offerId, sendPolicyUpdateNotification = false }) {
    const operationLockTime = 60; // 1 min
    const operationLockKey = `${accountUser.account.id}-${_application.id}-completeApplication`;

    await redisClient.acquireLock(operationLockKey, operationLockTime);

    try {
      let application = _application;
      const {
        dataSources: { UserAPI, CreditScoreAPI, RuleAPI, OfferAPI, ClientAccountAPI },
        prisma,
      } = this.context;

      const errors = [];
      const rejectLoanErrors = [];
      const warnings = [];
      const deferLoanWarnings = [];
      const overrideLoanWarnings = [];
      const overrideLoanErrors = [];

      const defaultResponse = {
        creditScore: 0,
        application,
        suggestedApplication: null,
        approved: false,
        message: null,
        portfolio: null,
        requiredSteps: [],
        completedSteps: [],
      };

      const { id: clientId, name: clientName } = application.account.parentAccount;

      let loanCategoryId;
      if (application.loanCategory != null) {
        loanCategoryId = application.loanCategory.id;
      }

      let selectedOffer;
      if (offerId) {
        selectedOffer = await OfferAPI.getOfferById(offerId);
        if (!selectedOffer) throw new Error('Provided offer does not exist');
        // set application duration to the max duration of the policy attached
        application.loanDuration = selectedOffer.policy.maxLoanDuration;
        await prisma.application.update({
          data: {
            loanDuration: application.loanDuration,
          },
          where: { id: application.id },
        });
      }

      const merchantConfig = await ClientAccountAPI.getMerchantConfigByName(clientName, loanCategoryId);

      const {
        requiresCrcReport: crcReportIsRequired,
        requiresCrcMicroReport: crcMicroReportIsRequired,
        requiresCreditScore: creditScoreIsRequired,
        requiresDecide: decideIsRequired,
        requiresLatestCrcReport,
      } = merchantConfig.options;

      const { user, bvn } = await UserAPI.getFullUserDetails({
        userId: accountUser.user.id,
      });

      const applicationData = {
        user,
        bvn,
        account: accountUser.account,
        amount: application.baseAmount,
        loanDuration: application.loanDuration,
        creditScore: null,
        application,
        accountBank: application.bankAccount,
      };

      if (application.metadata) {
        applicationData.durationMeta = application.metadata.durationMeta;
        applicationData.isBankSupported = application.metadata?.bankAccount?.isSupported;
      }

      const applicationPolicy = await prisma.policy.findFirst({
        where: {
          loanCategoryId,
        },
        select: {
          requiresScoreCard: true,
        },
      });

      // TODO: change source of creditScoreIsRequired variable
      if (
        decideIsRequired
        || creditScoreIsRequired
        || crcReportIsRequired
        || crcMicroReportIsRequired
        || hasTrueProperty(requiresLatestCrcReport)
        || applicationPolicy?.requiresScoreCard
      ) applicationData.creditScore = await CreditScoreAPI.getCreditScoreByapplicationId(application.id);

      // check general rules
      await RuleAPI.checkGeneralRules({
        applicationData,
        errors,
        rejectLoanErrors,
        warnings,
        deferLoanWarnings,
        overrideLoanWarnings,
        overrideLoanErrors,
      });

      if (!selectedOffer) {
        const { offers } = await OfferAPI.getOfferByApplicationId({ applicationId: application.id });
        // check offers
        if (offers && offers.length > 0) {
          // TODO: Select policy based on priority / isDefault
          [selectedOffer] = offers;
        }

        if (!offers && !application.metadata.rulesetResult) {
          errors.push({
            code: 'LOAN_OFFERS_NOT_GENERATED',
            message: 'Loan offers have not been generated for application',
          });
        }
      }

      if (!selectedOffer) {
        if (applicationData.creditScore?.scoreCardResult?.result?.pass === false) {
          rejectLoanErrors.push({
            code: 'ACCEPTANCE_CRITERIA_NOT_MET',
            message: 'Did not meet acceptance criteria',
          });
        } else {
          rejectLoanErrors.push({
            code: 'NO_MATCHING_LOAN_PRODUCTS',
            message: 'No Loan Offer',
          });
        }
      } else {
        const { policy, isDeferred } = selectedOffer;
        const policyId = policy.id;
        if (!application.policy || policyId !== application.policy.id) {
          await this.updateApplicationPolicy({
            clientId,
            applicationId: application.id,
            policyId,
            comment: 'Set application loan product based on offers',
            sendUserNotification: sendPolicyUpdateNotification,
          });
        }
        application = await this.getApplicationById(application.id);
        if (isDeferred) {
          deferLoanWarnings.push({
            code: 'RULESET_MANUAL_REVIEW_ACTION',
            message: 'Requires manual review according to matching rules criteria',
          });
        }
      }

      const allErrors = [...rejectLoanErrors, ...overrideLoanErrors, ...errors];
      const allWarnings = [...deferLoanWarnings, ...overrideLoanWarnings, ...warnings];

      const applicationStatusIsNotMutable = getApplicationStatusIsNotMutable(application.status.name);

      const steps = [...new Set([...application.completedSteps, APPLICATION_STEPS.completeApplication.name])];
      await this.setCompletedSteps({
        id: application.id,
        steps,
      });
      await prisma.application.update({
        where: { id: application.id },
        data: {
          submittedAt: new Date().toISOString(),
        },
      });
      application.completedSteps = steps;

      // should remain in pending state
      if (overrideLoanErrors.length > 0) {
        const messages = overrideLoanErrors.map(({ message }) => message).join(',');
        return {
          ...defaultResponse,
          application,
          message: messages,
          errors,
          warnings: allWarnings,
        };
      }

      // should override errors and move to manual review
      if (overrideLoanWarnings.length > 0) {
        const messages = overrideLoanWarnings.map(({ message }) => message).join(',');

        if (applicationStatusIsNotMutable) {
          return {
            ...defaultResponse,
            application,
            message: messages,
            errors: allErrors,
            warnings: overrideLoanWarnings,
          };
        }

        // defer application
        const { status } = await this.deferApplication({
          application,
          comment: messages,
          metadata: {
            warnings: allWarnings,
            overrideLoanWarnings,
          },
        });

        return {
          ...defaultResponse,
          application: {
            ...application,
            status,
          },
          message: messages,
          errors,
          warnings: allWarnings,
        };
      }

      // should deny application
      if (rejectLoanErrors.length > 0) {
        const messages = rejectLoanErrors.map(({ message }) => message).join(',');

        if (applicationStatusIsNotMutable) {
          return {
            ...defaultResponse,
            application,
            message: messages,
            errors: allErrors,
            warnings: deferLoanWarnings,
          };
        }
        // deny application
        const { status } = await this.denyApplication({
          application,
          comment: messages,
          metadata: {
            errors,
            rejectLoanErrors,
            warnings,
            deferLoanWarnings,
          },
        });
        return {
          ...defaultResponse,
          application: {
            ...application,
            status,
          },
          message: messages,
          errors: allErrors,
          warnings: deferLoanWarnings,
        };
      }

      // should do nothing but has less priority than rejectLoanErrors
      if (errors.length > 0) {
        const messages = errors.map(({ message }) => message).join(',');
        return {
          ...defaultResponse,
          application,
          message: messages,
          errors,
          warnings: allWarnings,
        };
      }

      // should move to manual review
      if (deferLoanWarnings.length > 0) {
        const messages = deferLoanWarnings.map(({ message }) => message).join(', ');

        if (applicationStatusIsNotMutable) {
          return {
            ...defaultResponse,
            application,
            message: messages,
            errors: allErrors,
            warnings: deferLoanWarnings,
          };
        }

        // defer application
        const { status } = await this.deferApplication({
          application,
          comment: messages,
          metadata: {
            warnings,
            deferLoanWarnings,
          },
        });

        return {
          ...defaultResponse,
          application: {
            ...application,
            status,
          },
          message: messages,
          errors,
          warnings: allWarnings,
        };
      }

      // if (warnings.length > 0) {
      //   const messages = warnings.map(({ message }) => message).join(',');
      //   return {
      //     ...defaultResponse,
      //     application,
      //     message: messages,
      //     errors: allErrors,
      //     warnings: allWarnings,
      //   };
      // }

      if (applicationStatusIsNotMutable) {
        return {
          ...defaultResponse,
          application,
          message: null,
          errors,
          warnings,
        };
      }

      if (allErrors.length === 0 && deferLoanWarnings.length === 0) {
        // Approve loan and create portfolio from application
        const { application: approvedApplication, portfolio } = await this.approveApplicationAndCreatePortfolio({
          applicationId: application.id,
        });

        return {
          creditScore: 0,
          application: approvedApplication,
          suggestedApplication: null,
          approved: true,
          message: null,
          portfolio,
          errors,
          warnings,
          requiredSteps: [],
          completedSteps: [],
        };
      }
      throw new Error('An unexpected error occurred while processing loan');
    } catch (error) {
      throw error;
    } finally {
      await redisClient.releaseLock(operationLockKey);
    }
  }

  async abandonApplication({ application, comment, metadata, accountUser }) {
    const { prisma } = this.context;
    const { id, status } = application;

    if (status.name !== APPLICATION_STATUSES.pending.name) {
      throw new Error(`cannot abandon an application in ${status.name} status`);
    }

    const variables = {
      applicationId: id,
      newStatus: APPLICATION_STATUSES.abandoned.name,
      oldStatus: status.name,
      comment,
      metadata,
      action: APPLICATION_HISTORY_ACTION.statusUpdateAction,
    };

    if (accountUser) {
      variables.actionBy = { connect: { id: accountUser.id } };
    }

    const [updateApplication] = await prisma.$transaction([
      prisma.application.update({
        data: {
          status: { connect: { name: variables.newStatus } },
        },
        where: { id: variables.applicationId },
        include: this.utils.APPLICATION_FIELDS,
      }),
      prisma.applicationHistory.create({
        data: {
          comment: variables.comment,
          metadata: variables.historyMetadata,
          application: { connect: { id: variables.applicationId } },
          oldStatus: { connect: { name: variables.oldStatus } },
          newStatus: { connect: { name: variables.newStatus } },
          actionBy: variables.actionBy,
          action: variables.action,
        },
      }),
    ]);

    return {
      ...application,
      status: updateApplication.status,
    };
  }

  // generate the offers a customer is entitled to
  async generateApplicationOffers({ application, accountUser }) {
    const operationLockTime = 60; // 1 min
    const operationLockKey = `${accountUser.account.id}-${application.id}-generateApplicationOffers`;

    await redisClient.acquireLock(operationLockKey, operationLockTime);
    const {
      dataSources: { RuleAPI, UserAPI, CreditScoreAPI, OfferAPI, ClientAccountAPI, PolicyAPI, RepaymentAPI },
      prisma,
    } = this.context;

    try {
      const { name: clientName } = application.account.parentAccount;

      let loanCategoryId;
      if (application.loanCategory != null) {
        loanCategoryId = application.loanCategory.id;
      }

      const merchantConfig = await ClientAccountAPI.getMerchantConfigByName(clientName, loanCategoryId);

      const {
        requiresCrcReport: crcReportIsRequired,
        requiresCrcMicroReport: crcMicroReportIsRequired,
        requiresCreditScore: creditScoreIsRequired,
        requiresCreditBureauReport: creditBureauReportIsRequired,
        requiresCreditRegistryReport: creditRegistryReportIsRequired,
        requiresDecide: decideIsRequired,
        requiresDecidePdfAnalysis,
        requiresLatestCrcReport,
      } = merchantConfig.options;

      const { user, bvn, nin } = await UserAPI.getFullUserDetails({
        userId: accountUser.user.id,
      });

      const { baseAmount: amount, loanDuration } = application;

      const blacklistDetails = await UserAPI.getBlacklistDetails({
        userId: user.id,
      }).catch((error) => {
        errorLogger(`[generateApplicationOffers] Error fetching user blacklist details - ${error.message}`, {
          error,
        });

        return null;
      });

      const applicationData = {
        user,
        bvn,
        nin,
        blacklistDetails,
        account: accountUser.account,
        amount,
        loanDuration,
        creditScore: null,
        application,
        accountBank: application.bankAccount,
      };

      if (application.metadata) {
        applicationData.durationMeta = application.metadata.durationMeta;
        applicationData.isBankSupported = application.metadata?.bankAccount?.isSupported;
      }

      if (
        decideIsRequired
        || creditScoreIsRequired
        || crcReportIsRequired
        || crcMicroReportIsRequired
        || creditBureauReportIsRequired
        || creditRegistryReportIsRequired
        || hasTrueProperty(requiresLatestCrcReport)
      ) applicationData.creditScore = await CreditScoreAPI.getCreditScoreByapplicationId(application.id);

      const policy = await prisma.policy.findFirst({
        where: {
          loanCategoryId,
        },
        include: PolicyAPI.utils.POLICY_FIELDS,
      });
      const applicablePolicies = {};
      let scoreCardResult;
      let noOfMissingSteps = 0;
      application.pendingMeta.forEach((step) => {
        if (!step?.isResolved && !step?.isSkipped) noOfMissingSteps += 1;
      });

      if (!policy.requiresScoreCard) {
        throw new Error('Policy does not have a score card configured, please contact administrator');
      }

      let affordabilitySuggestion = null;
      const existingMetadata = application.metadata || {};

      if (noOfMissingSteps === 0) {
        const decideOkay = !(requiresDecidePdfAnalysis || decideIsRequired)
          || applicationData.creditScore?.decideResult?.bankStatementAnalysis;
        if (decideOkay) {
          scoreCardResult = await RuleAPI.runScoreCard({ applicationData, loanCategoryId });
          policy.scoreCardConfig = {
            approvalConfig: {
              onPass: policy?.scoreCardConfig?.approvalConfig?.onPass || SCORECARD_APPROVAL_TYPES.Approve,
              onFail: policy?.scoreCardConfig?.approvalConfig?.onFail || SCORECARD_APPROVAL_TYPES.Decline,
            },
            affordabilityConfig: {
              onAccept: policy?.scoreCardConfig?.affordabilityConfig?.onAccept || SCORECARD_APPROVAL_TYPES.ManualReview,
              onReject: policy?.scoreCardConfig?.affordabilityConfig?.onReject || SCORECARD_APPROVAL_TYPES.Decline,
            },
          };
          const isPassDeferred = policy?.scoreCardConfig?.approvalConfig.onPass === SCORECARD_APPROVAL_TYPES.ManualReview;
          const isFailedDeffered = policy?.scoreCardConfig?.approvalConfig.onFail === SCORECARD_APPROVAL_TYPES.ManualReview;

          if ((isPassDeferred || isFailedDeffered) && scoreCardResult?.affordability) {
            affordabilitySuggestion = scoreCardResult?.affordability?.breakdown?.find(
              item => item.tenor === applicationData?.durationMeta?.baseDuration,
            );
            if (affordabilitySuggestion?.value) {
              const newAmoount = currency(affordabilitySuggestion?.value).value;
              const payload = {
                amount: newAmoount,
                loanDuration: applicationData?.durationMeta?.baseDuration,
                durationType: applicationData?.durationMeta?.type,
                customComment: `Updated application amount from N${formatAmount(applicationData.amount)} to N${formatAmount(newAmoount)} based on scorecard affordability`,
              };
              existingMetadata.affordabilityPayload = payload;
            }
          }

          const newApplicationData = await RuleAPI.getApplicationDataFromPolicy({ policy, applicationData });

          if (scoreCardResult.result?.pass === true) {
            applicablePolicies[policy.id] = {
              policyId: policy.id,
              priority: policy.priority,
              isDefault: policy.isDefault,
              data: newApplicationData._data,
              isDeferred: isPassDeferred,
            };
          } else if (scoreCardResult.result?.pass === false && isFailedDeffered) {
            applicablePolicies[policy.id] = {
              policyId: policy.id,
              priority: policy.priority,
              isDefault: policy.isDefault,
              data: newApplicationData._data,
              isDeferred: isFailedDeffered,
            };
          }
        }
      }

      // const applicationStatusIsNotMutable = getApplicationStatusIsNotMutable(application.status.name);

      await OfferAPI.deleteOldOffer({ applicationId: application.id });
      const applicablePoliciesArray = Object.values(applicablePolicies);

      const applicablePoliciesArrayResponse = await Promise.all(
        applicablePoliciesArray.map(offerData => OfferAPI.createOffer({
          applicationId: application.id,
          ...offerData,
        })),
      );

      const updatedApplication = await prisma.application.update({
        data: {
          metadata: {
            ...existingMetadata,
            rulesetResult: {
              createdAt: new Date().toISOString(),
            },
          },
          creditScore: scoreCardResult
            ? {
              upsert: {
                create: {
                  scoreCardResult,
                },
                update: {
                  scoreCardResult,
                },
              },
            }
            : undefined,
        },
        where: { id: application.id },
        include: this.utils.APPLICATION_FIELDS,
      });

      infoLogger('Application updated');

      const breakdown = await RepaymentAPI.getRepaymentBreakdown({
        policyId: policy.id,
        principalAmount: application.baseAmount,
        duration: application.loanDuration,
      });
      const durationMeta = application?.metadata?.durationMeta;

      return {
        application: updatedApplication,
        offers: applicablePoliciesArrayResponse,
        suggestedOffer: applicablePoliciesArrayResponse[0],
        potentialOffer: {
          id: cuid(),
          loanTenor: durationMeta ? `${durationMeta?.baseDuration} ${durationMeta?.durationTypeDisplayName}` : null,
          repaymentType: breakdown?.repaymentFrequency,
          baseAmount: breakdown?.principalAmount,
          fullAmount: breakdown?.totalExpectedPayment,
          interestRate: breakdown?.totalInterest,
          repaymentBreakdown: breakdown?.repaymentBreakdown,
          policy,
        },
      };
    } catch (error) {
      errorLogger('Error while generating offers', { applicationId: application.id, errMsg: error.message, error });
      await prisma.applicationHistory.create({
        data: {
          application: { connect: { id: application.id } },
          comment: 'Error while generating offer',
        },
      });
      throw error;
    } finally {
      await redisClient.releaseLock(operationLockKey);
    }
  }

  async generateCustomApplicationOffers({ application, accountUser }) {
    const operationLockTime = 60; // 1 min
    const operationLockKey = `${accountUser.account.id}-${application.id}-generateCustomApplicationOffers`;

    await redisClient.acquireLock(operationLockKey, operationLockTime);

    try {
      const {
        dataSources: { ClientRuleAPI, RuleAPI, UserAPI, CreditScoreAPI, OfferAPI, ClientAccountAPI },
        prisma,
      } = this.context;

      const { id: clientId, name: clientName } = application.account.parentAccount;

      let loanCategoryId;
      if (application.loanCategory != null) {
        loanCategoryId = application.loanCategory.id;
      }

      const merchantConfig = await ClientAccountAPI.getMerchantConfigByName(clientName, loanCategoryId);

      const {
        requiresCrcReport: crcReportIsRequired,
        requiresCrcMicroReport: crcMicroReportIsRequired,
        requiresCreditScore: creditScoreIsRequired,
        requiresCreditBureauReport: creditBureauReportIsRequired,
        requiresCreditRegistryReport: creditRegistryReportIsRequired,
        requiresDecide: decideIsRequired,
        requiresLatestCrcReport,
      } = merchantConfig.options;

      const { user, bvn } = await UserAPI.getFullUserDetails({
        userId: accountUser.user.id,
      });

      const { baseAmount: amount, loanDuration } = application;

      const blacklistDetails = await UserAPI.getBlacklistDetails({
        userId: user.id,
      }).catch((error) => {
        errorLogger(`[generateApplicationOffers] Error fetching user blacklist details - ${error.message}`, {
          error,
        });

        return null;
      });

      const applicationData = {
        user,
        bvn,
        blacklistDetails,
        account: accountUser.account,
        amount,
        loanDuration,
        creditScore: null,
        application,
        accountBank: application.bankAccount,
      };

      if (application.metadata) {
        applicationData.durationMeta = application.metadata.durationMeta;
        applicationData.isBankSupported = application.metadata?.bankAccount?.isSupported;
      }

      if (
        decideIsRequired
        || creditScoreIsRequired
        || crcReportIsRequired
        || crcMicroReportIsRequired
        || creditBureauReportIsRequired
        || creditRegistryReportIsRequired
        || hasTrueProperty(requiresLatestCrcReport)
      ) applicationData.creditScore = await CreditScoreAPI.getCreditScoreByapplicationId(application.id);

      const { accountRuleSets: baseRuleSets } = await ClientRuleAPI.getCustomApplicableClientRuleSets({
        clientId,
        amount,
      });

      const applicablePolicies = {};
      let checkRuleSetResults = [];

      if (baseRuleSets.length > 0) {
        checkRuleSetResults = await Promise.all(
          baseRuleSets.map(brs => RuleAPI.getCheckRulesetResult({
            accountRuleSet: brs,
            applicationData,
          })),
        );

        checkRuleSetResults.forEach(({ policyResults }) => {
          policyResults.forEach((policyResult) => {
            const { policyId, isDeclined, isEvaluated } = policyResult;
            if (!isDeclined || !isEvaluated) {
              applicablePolicies[policyId] = selectBestResult(policyResult, applicablePolicies[policyId]);
            }
          });
        });
      }
      let scoreCardResult;
      let noOfMissingSteps = 0;
      application.pendingMeta.forEach((step) => {
        if (!step?.isResolved && !step?.isSkipped) noOfMissingSteps += 1;
      });
      if (noOfMissingSteps === 0) {
        scoreCardResult = await RuleAPI.runScoreCard({
          applicationData,
          loanCategoryId: application.loanCategory != null ? application.loanCategory.id : undefined,
        });
      }

      await OfferAPI.deleteOldOffer({ applicationId: application.id });
      const applicablePoliciesArray = Object.values(applicablePolicies);

      const applicablePoliciesArrayResponse = await applicablePoliciesArray.map(async offerData => OfferAPI.createOffer({
        applicationId: application.id,
        ...offerData,
      }));

      const existingMetadata = application.metadata || {};
      const updatedApplication = await prisma.application.update({
        data: {
          metadata: {
            ...existingMetadata,
            rulesetResult: {
              checkRuleSetResults,
              createdAt: new Date().toISOString(),
            },
          },
          creditScore: {
            scoreCardResult: scoreCardResult || {},
          },
        },
        where: { id: application.id },
        include: this.utils.APPLICATION_FIELDS,
      });

      return {
        application: updatedApplication,
        offers: applicablePoliciesArrayResponse,
        suggestedOffer: applicablePoliciesArrayResponse[0],
      };
    } catch (error) {
      throw error;
    } finally {
      await redisClient.releaseLock(operationLockKey);
    }
  }

  async saveApplicationMetadata({ applicationId, data }, accountUser) {
    const { prisma } = this.context;

    const metaData = data;
    const filter = { id: applicationId };

    if (accountUser) {
      filter.account = { id: accountUser.account.id };
      if (accountUser.account.accountType.name === ACCOUNT_TYPES.Client.name) {
        filter.account = { parentAccount: { id: accountUser.account.id } };
      }
    }

    const exists = await prisma.application.findFirst({ where: filter });

    if (!exists) return new Error('Invalid application');
    if (metaData.length === 0) return new Error('No meta data provided!');

    const create = metaData.map(({ key, value, documentId }) => ({
      key,
      value,
      document: documentId ? { connect: { id: documentId } } : undefined,
    }));

    const application = await prisma.application.update({
      data: {
        meta: { create },
      },
      where: { id: applicationId },
      include: this.utils.APPLICATION_FIELDS,
    });

    return {
      application,
      status: true,
    };
  }

  async addComment({ applicationId, comment, accountUser }) {
    const { prisma } = this.context;

    const application = await prisma.application.findFirst({
      where: {
        OR: [
          {
            id: applicationId,
          },
          { applicationNumber: applicationId },
        ],
      },
      include: this.utils.APPLICATION_FIELDS,
    });

    const connectStatus = { connect: { name: application.status.name } };

    const updatedApplication = prisma.application.update({
      data: {
        history: {
          create: {
            comment,
            oldStatus: connectStatus,
            newStatus: connectStatus,
            actionBy: accountUser?.id ? { connect: { id: accountUser.id } } : undefined,
          },
        },
      },
      where: {
        id: application.id,
      },
      include: this.utils.APPLICATION_FIELDS,
    });

    return {
      application: updatedApplication,
    };
  }

  async deleteApplicationDocument({ key, bucket, applicationId, accountUser }) {
    const { prisma } = this.context;
    const [application] = await prisma.application.findMany({
      where: {
        id: applicationId,
        account: {
          parentAccount: {
            id: accountUser.account.id,
          },
        },
      },
      include: {
        meta: true,
        status: true,
        account: {
          select: {
            accountUsers: {
              select: {
                user: { select: { id: true } },
              },
            },
          },
        },
      },
    });

    if (!application) throw new Error('Application not found');

    const userId = application.account.accountUsers[0].user.id;

    const [document] = await prisma.supportingDocument.findMany({
      where: {
        user: { id: userId },
        file: {
          key,
          bucket,
        },
      },
    });

    if (!document) throw new Error('Document not found');
    await prisma.supportingDocument.delete({ where: { id: document.id } });
    const meta = application.meta.find(m => m.key === key);
    if (meta) {
      await prisma.applicationMetadata.delete({ where: { id: meta.id } });
    }

    await prisma.applicationHistory.create({
      data: {
        actionBy: { connect: { id: accountUser.id } },
        action: APPLICATION_HISTORY_ACTION.documentUpdate,
        oldStatus: { connect: { name: application.status.name } },
        newStatus: { connect: { name: application.status.name } },
        comment: `Deleted supporting document with key - ${key}`,
        application: { connect: { id: applicationId } },
      },
    });

    return prisma.application.findUnique({ where: { id: applicationId }, include: this.utils.APPLICATION_FIELDS });
  }

  async deleteCustomerDocument({ key, bucket, applicationId, accountUser }) {
    const { prisma } = this.context;
    const [application] = await prisma.application.findMany({
      where: {
        id: applicationId,
        account: {
          id: accountUser.account.id,
        },
      },
      include: {
        meta: true,
        status: true,
        account: {
          select: {
            accountUsers: {
              select: {
                user: { select: { id: true } },
              },
            },
          },
        },
      },
    });

    if (!application) throw new Error('Application not found');

    const userId = application.account.accountUsers[0].user.id;

    const [document] = await prisma.supportingDocument.findMany({
      where: {
        user: { id: userId },
        file: {
          key,
          bucket,
        },
      },
    });

    if (!document) throw new Error('Document not found');
    await prisma.supportingDocument.delete({ where: { id: document.id } });
    const meta = application.meta.find(m => m.key === key);
    if (meta) {
      await prisma.applicationMetadata.delete({ where: { id: meta.id } });
    }

    await prisma.applicationHistory.create({
      data: {
        actionBy: { connect: { id: accountUser.id } },
        action: APPLICATION_HISTORY_ACTION.documentUpdate,
        oldStatus: { connect: { name: application.status.name } },
        newStatus: { connect: { name: application.status.name } },
        comment: `Customer deleted supporting document with key - ${key}`,
        application: { connect: { id: applicationId } },
      },
    });

    return prisma.application.findUnique({ where: { id: applicationId }, include: this.utils.APPLICATION_FIELDS });
  }

  async sendApplicationAssignedNotification({ clientId, clientAccountUser, application }) {
    const {
      methods: { getApplicationAssignedNotification },
      context: {
        dataSources: { ClientAccountAPI },
      },
    } = this;
    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientId,
    });

    return getApplicationAssignedNotification({
      clientAccountUser,
      clientOptions,
      loanProductName: application.policy.name,
      applicationId: application.applicationNumber,
    }).send();
  }

  async sendApplicationUnassignedNotification({ clientId, clientAccountUser, application }) {
    const {
      methods: { getApplicationUnassignedNotification },
      context: {
        dataSources: { ClientAccountAPI },
      },
    } = this;
    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientId,
    });

    return getApplicationUnassignedNotification({
      clientAccountUser,
      clientOptions,
      loanProductName: application.policy.name,
      applicationId: application.applicationNumber,
    }).send();
  }

  async assignApplicationToMerchantUser({ applicationId, assignTo, accountUser }) {
    const { prisma } = this.context;

    const clientId = accountUser.accountId;
    const application = await this.getApplicationById(applicationId);
    try {
      const assignToAccountUser = await prisma.accountUser.findFirst({
        where: { id: assignTo, accountId: clientId },
        include: {
          user: {
            select: {
              email: true,
            },
          },
        },
      });

      let message;

      if (!assignToAccountUser) {
        message = 'Invalid merchant account user. Get a valid merchant account user to assign the application to.';
        errorLogger(message);
        return {
          success: false,
          message,
        };
      }

      const applicationExists = (await prisma.application.count({
        where: {
          id: applicationId,
          account: {
            parentAccountId: clientId,
          },
        },
      })) !== 0;

      if (!applicationExists) {
        message = 'Invalid Application. Application does not belong to this merchant.';
        errorLogger(message);
        return {
          success: false,
          message,
        };
      }

      const assignedApplication = await prisma.application.update({
        where: {
          id: applicationId,
        },
        data: {
          assignedTo: { connect: { id: assignTo } },
          history: {
            create: {
              comment: `Application assigned to user with email ${assignToAccountUser.user.email}`,
              actionBy: { connect: { id: accountUser.id } },
            },
          },
        },
      });

      if (assignedApplication) {
        this.sendApplicationAssignedNotification({
          application,
          clientId,
          clientAccountUser: assignToAccountUser,
        }).catch((error) => {
          errorLogger('Error while sending application assigned notification', { msg: error.message, error });
        });
      }

      if (application.assignedTo) {
        this.sendApplicationUnassignedNotification({
          application,
          clientId,
          clientAccountUser: application.assignedTo,
        }).catch((error) => {
          errorLogger('Error while sending application unassigned notification', { msg: error.message, error });
        });
      }

      return {
        success: true,
        application,
      };
    } catch (error) {
      errorLogger('Error while assigning application to merchant account user', { msg: error.message, error });
      throw new Error('Error occurred while assigning application to a user');
    }
  }

  async unassignApplication({ applicationId, accountUser }) {
    const { prisma } = this.context;
    const clientId = accountUser.accountId;
    try {
      const application = await prisma.application.findFirst({
        where: {
          id: applicationId,
          account: {
            parentAccountId: clientId,
          },
        },
        include: this.utils.APPLICATION_FIELDS,
      });

      let message;

      if (!application) {
        message = 'Invalid Application. Application does not belong to this merchant.';
        errorLogger(message);
        return {
          success: false,
          message,
        };
      }

      if (!application.assignedTo) throw new Error('Application is currently unassigned');

      const unassignedApplication = await prisma.application.update({
        where: {
          id: applicationId,
        },
        data: {
          assignedTo: { disconnect: true },
          history: {
            create: {
              comment: `Application unassigned from user with email ${application.assignedTo.user.email}`,
              actionBy: { connect: { id: accountUser.id } },
            },
          },
        },
      });

      if (unassignedApplication) {
        this.sendApplicationUnassignedNotification({
          application,
          clientId,
          clientAccountUser: application.assignedTo,
        }).catch((error) => {
          errorLogger('Error while sending application unassigned notification', { msg: error.message, error });
        });
      }

      return {
        success: true,
        application,
      };
    } catch (error) {
      errorLogger('Error while unassigning application', { msg: error.message, error });
      throw new Error('Error occurred while unassigning application');
    }
  }

  setApplicationBankAccount = async ({
    applicationId,
    bankAccountId,
    newBankAccount: { bankId, accountName, accountNumber } = {},
    accountUser,
  } = {}) => {
    const application = await this.getApplicationById(applicationId);
    const {
      prisma,
      dataSources: { BankAPI },
    } = this.context;

    if (!bankAccountId && !bankId && !accountName && !accountNumber) {
      return {
        success: false,
        message: 'Invalid input. Kindly supply existing `bankAccountId` or new bank account input fields.',
      };
    }

    try {
      const { account } = (await prisma.application.findFirst({
        where: {
          id: applicationId,
          account: { parentAccount: { id: accountUser.account.id } },
        },
        select: { account: { select: { id: true } } },
      })) || {};

      if (!account || Object.keys(account).length === 0) {
        errorLogger(`Error setting application bank account - Application with id '${applicationId}' does not exist`);

        return {
          success: false,
          message: 'Application does not exist.',
        };
      }

      let bankAccount;

      if (bankAccountId) {
        bankAccount = await prisma.accountBank.findFirst({
          where: { id: bankAccountId },
          include: {
            bank: true,
            okraRecord: true,
          },
        });

        if (!bankAccount) {
          errorLogger(
            `Error setting application bank account - Bank account with id '${bankAccountId}' does not exist.`,
          );

          return {
            success: false,
            message: 'Bank account does not exist.',
          };
        }
      } else {
        const bankExists = (await prisma.bank.count({ where: { id: bankId } })) > 0;

        if (!bankExists) {
          return {
            success: false,
            message: `Bank with id '${bankId}' does not exist`,
          };
        }

        bankAccount = await BankAPI.addAccountBank({
          bankId,
          accountId: account.id,
          accountNumber,
          accountName,
        });
      }

      await BankAPI.updateDefaultAccountBank(account.id, bankAccount.id);

      bankAccount.isDefault = true;

      await prisma.application.update({
        where: {
          id: applicationId,
        },
        data: {
          bankAccount: { connect: { id: bankAccount.id } },
          history: {
            create: {
              comment: 'Application bank account has been updated',
              actionBy: { connect: { id: accountUser.id } },
            },
          },
        },
      });

      return {
        success: true,
        applicationId,
        bankAccount,
        applicationNumber: application.applicationNumber,
      };
    } catch (error) {
      errorLogger(`Error while updating application bank account for applicationId - ${applicationId}`, { error });
      throw error;
    }
  };

  async reopenApplication({ application, accountUser }) {
    const {
      prisma,
      dataSources: { ReviewFeedbackAPI },
    } = this.context;
    const { id, status, approvalWorkflow, history = [] } = application;

    if (status.name !== APPLICATION_STATUSES.denied.name) {
      throw new Error(`Cannot reopen an application in ${status.name} status`);
    }

    let deniedAppHistory;
    let updateStepHistory;

    for (let i = 0; i < history.length; i++) {
      const h = history[i];
      if (h.newStatus?.name === APPLICATION_STATUSES.denied.name) {
        deniedAppHistory = h;
      }
      if (h.newAWStep) {
        updateStepHistory = h;
      }

      if (deniedAppHistory && updateStepHistory) break;
    }

    const newStatus = deniedAppHistory?.oldStatus?.name || APPLICATION_STATUSES.pending.name;
    const newAWStepId = deniedAppHistory?.oldAWStep?.id || updateStepHistory?.newAWStep?.id;

    const variables = {
      newStatus,
      applicationId: id,
      oldStatus: status.name,
      comment: 'Application Reopened',
      action: APPLICATION_HISTORY_ACTION.reopenApplication,
    };

    let approvalWorkflowUpdate;
    if (approvalWorkflow && newAWStepId) {
      approvalWorkflowUpdate = {
        update: {
          nextStep: { connect: { id: newAWStepId } },
        },
      };
    }

    await ReviewFeedbackAPI.completeReviewFeedbacks({
      applicationId: variables.applicationId,
      accountUser,
      appStatus: APPLICATION_STATUSES.underReview.name,
    });

    const updateApplication = await prisma.application.update({
      where: { id: variables.applicationId },
      data: {
        status: { connect: { name: variables.newStatus } },
        history: {
          create: {
            comment: variables.comment,
            oldStatus: { connect: { name: variables.oldStatus } },
            newStatus: { connect: { name: variables.newStatus } },
            action: variables.action,
            actionBy: accountUser.id ? { connect: { id: accountUser.id } } : undefined,
          },
        },
        approvalWorkflow: approvalWorkflowUpdate,
      },
      include: this.utils.APPLICATION_FIELDS,
    });

    return updateApplication;
  }

  async sendPendingApplicationReminder({ clientId, customerId }) {
    const {
      methods: { getPendingApplicationReminderEmail },
      context: {
        dataSources: { ClientAccountAPI, AccountAPI, MessageAPI },
      },
    } = this;

    const customerAccountUser = await AccountAPI.getAccountUserById({
      id: customerId,
      clientId,
    });

    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientId,
    });

    const notificationPreference = clientOptions?.customerNotificationPreference;

    if (notificationPreference && notificationPreference?.incompleteApplicationReminder?.sms === true) {
      await MessageAPI.sendIncompleteApplicationReminder({
        customerAccountUser,
        clientSlug: clientOptions.slug,
      }).catch((error) => {
        errorLogger('Error while sending incomplete application sms reminder', {
          msg: error.message,
          customerId,
        });
      });
    }

    if (!notificationPreference || notificationPreference?.incompleteApplicationReminder?.email === false) {
      infoLogger('Email notification is disabled for this client');
      return true;
    }

    infoLogger('Sending pending application reminder email', { customerId });

    return getPendingApplicationReminderEmail({
      customerAccountUser,
      clientOptions,
    }).send();
  }

  async bulkUpdateFromCsv({ file, accountUser }) {
    try {
      const { createReadStream, mimetype } = await file;

      if (mimetype !== 'text/csv') {
        throw new Error('Invalid file type. Only csv files are allowed');
      }

      const stream = createReadStream();
      const data = await csvToJson().fromStream(stream);

      const updateApplications = data.map(record => this.updateApplicationDocumentAndRequirements({ record, accountUser }));

      const result = await Promise.allSettled(updateApplications);
      const errors = result
        .filter(p => p.status === 'rejected')
        .map((f) => {
          const [applicationNumber, message] = f.reason.message.split('—') || [];
          return {
            applicationNumber,
            message,
          };
        });

      return {
        success: !errors.length,
        errors,
      };
    } catch (error) {
      errorLogger('Error while bulk updating applications from csv', { msg: error.message, error });
      throw new Error('Error occurred while bulk updating applications from csv');
    }
  }

  async updateApplicationDocumentAndRequirements({ record, accountUser: merchantUser }) {
    const {
      prisma,
      dataSources: { GcsAPI },
    } = this.context;
    const {
      applicationNumber,
      creditBureauReportUploadKey,
      bankStatementUploadKey,
      bankStatementPassword,
      bankStatementBankCode,
      requiresDecidePdfAnalysis,
    } = record;

    try {
      const application = await prisma.application.findFirst({
        where: {
          applicationNumber,
        },
        select: {
          id: true,
          bankAccount: {
            select: {
              bank: {
                select: {
                  id: true,
                  code: true,
                },
              },
            },
          },
          status: {
            select: { name: true },
          },
          account: {
            select: {
              id: true,
              accountUsers: {
                select: {
                  id: true,
                  user: {
                    select: {
                      id: true,
                    },
                  },
                },
              },
            },
          },
        },
      });
      if (!application) throw new Error('Application not found');

      let bank;
      let bankCode;
      let existingBankStatement;
      const metadata = [];
      if (bankStatementUploadKey) {
        bankCode = application.bankAccount?.bank?.code || bankStatementBankCode;
        if (!bankCode) throw new Error('Statement bank code is required');

        bank = await prisma.bank.findFirst({ where: { code: bankCode } });
        if (!bank) throw new Error('Bank not found');

        existingBankStatement = await prisma.bankStatementFile.findFirst({
          where: {
            application: {
              id: application.id,
            },
          },
        });
      }

      const {
        id: applicationId,
        status: { name: status },
        account: { id: accountId, accountUsers },
      } = application;
      const [
        {
          id: accountUserId,
          user: { id: userId },
        },
      ] = accountUsers;
      const {
        account: { id: clientId, slug },
      } = merchantUser;

      const bankStatementKey = bankStatementUploadKey
        ? `${GcsAPI.getFolderName({ subFolder: 'customer-bank-statement/upload', slug })}${bankStatementUploadKey}`
        : undefined;
      const creditBureauReportKey = creditBureauReportUploadKey
        ? `${GcsAPI.getFolderName({ slug })}${creditBureauReportUploadKey}`
        : undefined;

      const response = bankStatementKey || creditBureauReportKey
        ? await prisma.$transaction([
          ...(creditBureauReportKey
            ? [
              prisma.supportingDocument.create({
                data: {
                  file: {
                    create: {
                      key: creditBureauReportKey,
                      url: `${INDICINA_CDN_URL}/${creditBureauReportKey}`,
                      bucket: GCS_BUCKET,
                    },
                  },
                  documentName: `CREDIT REPORT ${applicationNumber}`,
                  uploadType: DOCUMENT_UPLOAD_TYPE.client,
                  user: { connect: { id: userId } },
                  accountUser: {
                    connect: {
                      id: accountUserId,
                    },
                  },
                },
              }),
            ]
            : []),
          ...(bankStatementKey
            ? [
              prisma.bankStatementFile.create({
                data: {
                  file: {
                    create: {
                      key: bankStatementKey,
                      url: `${INDICINA_CDN_URL}/${bankStatementKey}`,
                      bucket: GCS_BUCKET,
                    },
                  },
                  password: bankStatementPassword,
                  application: {
                    connect: {
                      id: applicationId,
                    },
                  },
                  account: {
                    connect: {
                      id: accountId,
                    },
                  },
                  bank: {
                    connect: {
                      id: bank.id,
                    },
                  },
                },
              }),
            ]
            : []),
        ])
        : [];

      if (creditBureauReportKey && response.length) {
        const [uploadedCreditBureauDocument] = response;
        metadata.push({
          key: creditBureauReportKey,
          value: '',
          document: { connect: { id: uploadedCreditBureauDocument.id } },
        });
      }

      if (existingBankStatement) {
        await prisma.bankStatementFile
          .delete({
            where: { id: existingBankStatement.id },
          })
          .catch(() => {});
      }

      if (requiresDecidePdfAnalysis?.toLowerCase() === 'true' && bankStatementUploadKey) {
        publishBankStatementUploadedEvent({
          clientId,
          applicationId,
          skipRequirementCheck: true,
        });
      }

      await prisma.application
        .update({
          data: {
            meta: metadata.length ? { create: metadata } : undefined,
            history: {
              create: {
                oldStatus: { connect: { name: status } },
                newStatus: { connect: { name: status } },
                comment: 'Application documents and requirements updated',
                actionBy: { connect: { id: merchantUser.id } },
              },
            },
          },
          where: { id: applicationId },
        })
        .catch((error) => {
          errorLogger('Error saving application meta and history', { error, msg: error.message });
        });
    } catch (error) {
      errorLogger('Error while updating single application from csv', { msg: error.message, error, record });
      throw new Error(`${applicationNumber}—${error.message}`);
    }
  }

  async updateApprovalWorkflow({ applicationId, newWorkflowId, newWorkflowStepId, accountUser, comment: _comment }) {
    const {
      prisma,
      dataSources: { ApprovalWorkflowAPI },
    } = this.context;
    const clientId = accountUser.accountId;

    infoLogger('updateApprovalWorkflow', { applicationId, newWorkflowId, newWorkflowStepId });

    const application = await prisma.application.findFirst({
      where: {
        id: applicationId,
        account: {
          parentAccountId: clientId,
        },
      },
      select: {
        id: true,
        status: true,
        portfolio: {
          select: {
            id: true,
            status: true,
          },
        },
        approvalWorkflow: {
          select: {
            id: true,
            nextStep: {
              include: {
                roles: true,
              },
            },
            workflow: {
              include: {
                steps: {
                  include: {
                    roles: true,
                  },
                },
              },
            },
          },
        },
      },
    });
    infoLogger('application', application);
    const allowedStatus = [APPLICATION_STATUSES.underReview.name, APPLICATION_STATUSES.approved.name];
    if (!application) {
      errorLogger('Application not found', { applicationId, clientId });
      throw new Error('Invalid Application');
    }
    if (!allowedStatus.includes(application?.status.name)) {
      errorLogger('Application status not allowed');
      throw new Error('Application is currently immutable');
    }

    const allowedPortfolioStatus = [PORTFOLIO_STATUSES.onHold.name, PORTFOLIO_STATUSES.pendingManualDisbursement.name];
    if (application?.portfolio && !allowedPortfolioStatus.includes(application.portfolio?.status?.name)) {
      errorLogger('Application portfolio status not allowed');
      throw new Error('Application is currently immutable');
    }

    const updateData = {
      workflow: application.approvalWorkflow.workflow,
      nextStep: application.approvalWorkflow.nextStep,
    };
    infoLogger('initial updateData', updateData);
    if (newWorkflowId) {
      const workflow = await prisma.approvalWorkflow.findFirst({
        where: {
          id: newWorkflowId,
        },
        select: {
          id: true,
          clientId: true,
          steps: {
            select: {
              id: true,
              orderNo: true,
              approvalType: true,
              approvalWorkflowId: true,
            },
            orderBy: {
              orderNo: 'asc',
            },
          },
          title: true,
        },
      });

      if (!workflow || (workflow.clientId && workflow.clientId !== clientId)) {
        errorLogger('invalid workflow id', { newWorkflowId, clientId });
        throw new Error('Invalid workflow selected');
      }
      updateData.workflow = workflow;
      [updateData.nextStep] = workflow.steps;
    }
    if (newWorkflowStepId) {
      const workflowStep = await prisma.approvalWorkflowStep.findFirst({
        where: {
          id: newWorkflowStepId,
          approvalWorkflowId: updateData.workflow.id,
        },
        include: {
          roles: {
            select: {
              name: true,
            },
          },
        },
      });
      if (!workflowStep) {
        errorLogger('invalid workflow step', { newWorkflowStepId, workflowId: updateData.workflow.id });
        throw new Error('Invalid workflow step selected');
      }
      updateData.nextStep = workflowStep;
    }
    const { approvalWorkflow } = application;

    infoLogger('UpdateApprovalWorkflow Data', { updateData });

    if (
      approvalWorkflow.workflow.id === updateData.workflow.id
      && approvalWorkflow.nextStep.id === updateData.nextStep.id
    ) {
      return {
        success: true,
        applicationId,
      };
    }

    const workflowUpdated = newWorkflowId !== approvalWorkflow.workflow?.id
      ? `workflow updated from ${approvalWorkflow.workflow?.title} to ${updateData.workflow?.title}`
      : '';
    const fromRole = approvalWorkflow.nextStep.roles?.map(role => role?.name).join(', ');
    const toRole = updateData.nextStep?.roles?.map(role => role?.name).join(', ');
    const workflowStepUpdated = newWorkflowStepId
      ? `workflow step updated from ${approvalWorkflow.nextStep?.approvalType} (Step ${
          approvalWorkflow.nextStep?.orderNo
      }: ${fromRole}) to ${updateData.nextStep?.approvalType} (Step ${updateData.nextStep?.orderNo}: ${toRole})`
      : '';

    const and = workflowUpdated && workflowStepUpdated ? ' and ' : '';

    const comment = _comment || `Application ${workflowUpdated}${and}${workflowStepUpdated}`;

    infoLogger('UpdateApprovalWorkflow and comment Data', { updateData, comment });

    let response;
    if (
      updateData.workflow.id === approvalWorkflow.workflow.id
      && updateData.nextStep.orderNo < approvalWorkflow.nextStep.orderNo
    ) {
      infoLogger('Reverting workflow', { updateData });
      response = await ApprovalWorkflowAPI.revertApprovalWorkflowStep({
        applicationId,
        comment,
        accountUser,
        customUpdate: updateData,
      });
    } else {
      infoLogger('Advancing workflow', { updateData });
      response = await ApprovalWorkflowAPI.advanceApprovalWorkflowStep({
        applicationId,
        comment,
        accountUser,
        customUpdate: updateData,
      });
    }

    infoLogger('Update ApprovalWorkflow Response', { response });

    return {
      applicationId,
      success: true,
    };
  }

  async activeCustomerApplication({ accountId }) {
    return this.context.prisma.application.findFirst({
      where: {
        account: {
          id: accountId,
        },
        OR: [
          {
            status: {
              name: {
                notIn: [
                  APPLICATION_STATUSES.approved.name,
                  APPLICATION_STATUSES.denied.name,
                  APPLICATION_STATUSES.abandoned.name,
                ],
              },
            },
          },
          {
            status: { name: APPLICATION_STATUSES.approved.name },
            portfolio: { status: { name: { not: PORTFOLIO_STATUSES.closed.name } } },
          },
        ],
      },
    });
  }

  async chargeProcessingFeeBeforeApproval({ application }) {
    const {
      prisma,
      dataSources: { CardAPI, AccountAPI },
    } = this.context;
    const {
      account: { id: accountId },
      processingFee,
      id: applicationId,
      status: { id: statusId },
      policy: { processingFeeName },
    } = application;

    if (!processingFee) return { success: true };

    const hasDebitedProcessingFeeBeforeApproval = await prisma.applicationHistory.findFirst({
      where: {
        comment: {
          contains: `Successfully debited ${processingFeeName ?? 'processing fee'} of amount N${formatAmount(processingFee)}`,
        },
        application: {
          id: applicationId,
        },
      },
    });

    if (hasDebitedProcessingFeeBeforeApproval) {
      return {
        success: true,
      };
    }

    try {
      const pendingDebit = await prisma.cardTransactionReference.findFirst({
        where: {
          status: CARD_REFERENCE_STATUS.pending.name,
          account: { id: accountId },
          AND: [
            { metadata: { string_contains: applicationId } },
            { metadata: { string_contains: CARD_TRANSACTION_META_TYPES.processingFeeCharge } },
          ],
        },
      });

      if (pendingDebit) {
        throw new Error(`Pending transaction with ref ${pendingDebit.reference} detected, retry later`);
      }

      const accountUser = await AccountAPI.getAccountUserById({
        clientId: application.account.parentAccountId,
        id: application.account.id,
      });

      const { success, error } = await CardAPI.chargeCardOnFile({
        amount: processingFee,
        accountUser,
        type: CARD_TRANSACTION_META_TYPES.processingFeeCharge,
        metadata: {
          applicationId,
        },
      });

      if (!success) throw new Error(`${error?.message || `Unable to debit ${processingFeeName ?? 'processing fee'}`}`);

      return await prisma.applicationHistory
        .create({
          data: {
            application: { connect: { id: applicationId } },
            oldStatus: { connect: { id: statusId } },
            newStatus: { connect: { id: statusId } },
            comment: `Successfully initiated ${processingFeeName ?? 'processing fee'} of amount N${formatAmount(processingFee)}`,
          },
        })
        .catch((e) => {
          errorLogger('ERROR OCCURED WHILST CREATING APPLICATION HISTORY AFTER INITIATING PROCESSING FEE DEBIT', {
            error: e,
            errMsg: e.message,
            applicationId,
          });
          return true;
        });
    } catch (error) {
      errorLogger('ERROR OCCURED WHILST DEBITING PROCESSING FEE BEFORE APPROVAL - ', { error, errMsg: error.message });
      throw error;
    }
  }
}
