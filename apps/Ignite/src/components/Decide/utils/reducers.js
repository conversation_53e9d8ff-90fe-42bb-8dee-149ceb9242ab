export default {
  reduceScoreCardResponse: scorecard => ({
    id: scorecard.id,
    name: scorecard.name,
    status: scorecard.status,
    createdAt: scorecard.createdAt,
    ruleset: scorecard.ruleset,
  }),
  reduceScoreCardExecuteResponse: result => ({
    scoreCardId: result.scorecardId,
    analysisId: result.rules.id,
    result: result.rules?.outcome,
    resultId: result.id,
    affordability: result?.affordability,
  }),
};
