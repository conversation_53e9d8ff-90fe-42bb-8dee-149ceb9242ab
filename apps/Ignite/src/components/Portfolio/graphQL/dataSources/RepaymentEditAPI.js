/* eslint-disable class-methods-use-this,no-unused-expressions */
import crypto from 'crypto';
import { DataSource } from 'apollo-datasource';
import { isAfter, isBefore, isValid, format } from 'date-fns';
import { errorLogger } from '~/logger';
import {
  PORTFOLIO_STATUSES,
  REPAYMENT_STATUSES,
  LOAN_STATUSES,
  DEBIT_MANDATE_TYPES,
  EDIT_REPAYMENT_STATUSES,
  DEBIT_INSTRUCTION_STATUSES,
} from '~/config';
import { redisClient } from '~/redis';
import {
  publishRepaymentScheduleUpdatedEvent,
  publishUpdateCollectionRepaymentEvent,
} from '~/events/publishers/repayment';
import { COLLECT_STATUS } from '~/components/Lidya/utils';
import { formatAmount } from '~/lib';
import { PORTFOLIO_FIELDS, REPAYMENT_FIELDS } from '../../utils';

export default class RepaymentEditAPI extends DataSource {
  initialize(config) {
    this.context = config.context;
  }

  constructor({ methods }) {
    super();
    this.methods = methods;
  }

  async editRepayment({ repayments, portfolioId, accountUser, isDryRun }) {
    const { prisma } = this.context;

    const portfolio = await prisma.portfolio.findUnique({ where: { id: portfolioId }, include: PORTFOLIO_FIELDS });

    if (portfolio.status.name === PORTFOLIO_STATUSES.closed.name) {
      throw new Error('Cannot edit repayments for closed loans');
    }

    const operationLockTime = 60; // 1 min
    const operationLockKey = `${portfolio.account.id}-${portfolio.id}-portfolio`;
    !isDryRun && (await redisClient.acquireLock(operationLockKey, operationLockTime));

    const cacheKey = crypto
      .createHash('sha256')
      .update(`EDIT_REPAYMENT_${portfolioId}_${accountUser.account.id}_${JSON.stringify(repayments)}`)
      .digest('hex');
    let existingTemplateData = await redisClient.getAsync(cacheKey);

    const actionBy = accountUser.id;

    if (!isDryRun && existingTemplateData) {
      existingTemplateData = JSON.parse(existingTemplateData);
      await this.upsertRepaymentsAndCreateHistory({ templateData: existingTemplateData, portfolioId });
      await this.updateRepaymentPortfolio({ portfolioId, actionBy });

      await redisClient.releaseLock(operationLockKey);
      return {
        status: EDIT_REPAYMENT_STATUSES.success.name,
        repaymentData: existingTemplateData,
      };
    }

    const validation = [];
    const templateData = [];

    let principalAmount = 0;
    const { outstandingPrincipal } = this.getPrincipalAmounts({ portfolio });

    await Promise.all(
      repayments.map(async ({ newPrincipalAmount, newLateFee, newDueDate, newInterestAmount, repaymentId }, index) => {
        const errorMessage = this.validateInput({ newPrincipalAmount, newLateFee, newDueDate, newInterestAmount });

        if (errorMessage !== '') {
          validation.push({ repaymentId, errorMessage, index });
          return;
        }

        const isNewRepayment = repaymentId == null;

        // is new repayment no need to generate history data
        // if existing repayment check if any amount has been paid
        // if amount paid isn't 0 or null update with history
        // else if amount paid is null just go ahead and cancel all other repayments and generate new repayments

        // get repayment

        let repayment = null;
        if (!isNewRepayment) {
          const existingRepaymentInfo = await this.getRepayment(repaymentId);
          const { message: repaymentMsg } = existingRepaymentInfo;

          if (repaymentMsg) {
            validation.push({ repaymentId, errorMessage: repaymentMsg, index });
            return;
          }

          ({ repayment } = existingRepaymentInfo);
          // repayment status check function
          const { isValidStatus, message: repaymentStatusMsg } = this.validateRepaymentStatus(repayment.status);

          if (!isValidStatus) {
            validation.push({ repaymentId, errorMessage: repaymentStatusMsg, index });
            return;
          }
        }

        const repaymentUpdateData = {};
        // update this function to change existing repayment statuses
        repaymentUpdateData.isNewRepaymentRecord = isNewRepayment || repayment.amountPaid == null;
        repaymentUpdateData.repayment = this.getUpdatedRepaymentValues({
          newPrincipalAmount,
          newLateFee,
          newDueDate,
          newInterestAmount,
          repayment,
        });

        repaymentUpdateData.repayment.actionBy = actionBy;
        templateData.push(repaymentUpdateData);

        principalAmount += newPrincipalAmount;
      }),
    );

    if (principalAmount > outstandingPrincipal) {
      throw new Error('New total principal cannot be greater than existing principal');
    }

    if (isDryRun) {
      // eslint-disable-next-line no-unused-expressions
      validation.length === 0 && (await redisClient.setAsync(cacheKey, JSON.stringify(templateData)));

      return {
        status: EDIT_REPAYMENT_STATUSES.pending.name,
        validation,
        repaymentData: templateData,
      };
    }

    if (validation.length > 0) {
      return {
        status: EDIT_REPAYMENT_STATUSES.failed.name,
        validation,
      };
    }

    await this.upsertRepaymentsAndCreateHistory({ templateData, portfolioId });
    await this.updateRepaymentPortfolio({ portfolioId, actionBy });

    await redisClient.releaseLock(operationLockKey);
    return {
      status: EDIT_REPAYMENT_STATUSES.success.name,
      repaymentData: templateData,
    };
  }

  async upsertRepaymentsAndCreateHistory({ templateData, portfolioId }) {
    templateData.forEach(async (template) => {
      const { repayment: repaymentData, isNewRepaymentRecord } = template;
      const {
        id,
        principal,
        lateFee,
        interest,
        dueDate,
        outstandingPayment,
        actionBy,
        status: newStatus,
      } = repaymentData;
      const { status, message } = await this.updateRepaymentAndCreateHistory({
        repaymentId: id,
        newRepaymentData: { principal, lateFee, dueDate, interest, status: newStatus, outstandingPayment, actionBy },
        isNewRepaymentRecord,
        portfolioId,
      });

      repaymentData.status = status;
      repaymentData.message = message;
    });
  }

  validateInput({ newPrincipalAmount, newLateFee, newDueDate, newInterestAmount }) {
    let errorMessage = '';

    if (newPrincipalAmount < 0) errorMessage += 'Invalid principal amount\n';
    if (newLateFee < 0) errorMessage += 'Invalid late fee\n';
    if (newInterestAmount < 0) errorMessage += 'Invalid late fee\n';
    if (!isValid(new Date(newDueDate))) errorMessage += 'Invalid due date';

    return errorMessage;
  }

  async getRepayment(id) {
    try {
      const repayment = await this.context.prisma.repayment.findUnique({
        where: { id },
        include: {
          status: true,
          portfolio: {
            selecr: { id: true },
          },
        },
      });

      return { repayment };
    } catch (e) {
      errorLogger(`Error fetching repayment - id: ${id}`, { stack: e });
      return { message: `Error fetching repayment - id: ${id}` };
    }
  }

  validateRepaymentStatus(status) {
    if (status.name !== REPAYMENT_STATUSES.pending.name) return { isValidStatus: false, message: 'Only pending repayments can be edited' };
    return { isValidStatus: true };
  }

  // if repayment == null just return a new object
  getUpdatedRepaymentValues({ newPrincipalAmount, newLateFee, newDueDate, newInterestAmount, repayment }) {
    const sumRepaymentValues = newPrincipalAmount + newLateFee + newInterestAmount;
    const isExistingRepayment = repayment != null;

    if (isExistingRepayment && sumRepaymentValues < repayment.amountPaid) {
      throw new Error('Repayment has been partly paid off. New total cannot be less than paid amount');
    }

    let status = { connect: { name: REPAYMENT_STATUSES.pending.name } };
    if (isExistingRepayment) {
      status = sumRepaymentValues === repayment.amountPaid
        ? { connect: { name: REPAYMENT_STATUSES.paid.name } }
        : { connect: { name: repayment.status.name } };
    }

    return {
      id: isExistingRepayment ? repayment.id : null,
      principal: newPrincipalAmount,
      lateFee: newLateFee,
      interest: newInterestAmount,
      dueDate: newDueDate,
      outstandingPayment: isExistingRepayment ? sumRepaymentValues - repayment.amountPaid : sumRepaymentValues,
      status,
    };
  }

  async updateRepaymentAndCreateHistory({ repaymentId, newRepaymentData, isNewRepaymentRecord, portfolioId }) {
    const { prisma } = this.context;
    const { principal, lateFee, interest, dueDate, outstandingPayment, status, actionBy } = newRepaymentData;
    let repayment = null;
    try {
      if (isNewRepaymentRecord) {
        if (repaymentId != null) {
          await prisma.repayment.update({
            where: { id: repaymentId },
            data: {
              status: { connect: { name: REPAYMENT_STATUSES.cancelled.name } },
              actionBy: { connect: { id: actionBy } },
              cancellationDate: new Date().toISOString(),
            },
          });
        }

        repayment = await prisma.repayment.create({
          data: {
            principalPortion: principal,
            principalBalance: principal,
            dueDate,
            outstandingPayment,
            interestPortion: interest,
            status,
            portfolio: { connect: { id: portfolioId } },
          },
        });
      } else {
        repayment = await prisma.repayment.update({
          where: { id: repaymentId },
          data: {
            principalBalance: principal,
            principalPortion: principal,
            lateFee,
            interestPortion: interest,
            dueDate,
            outstandingPayment,
            status,
          },
        });
      }
    } catch (e) {
      errorLogger(`An error occurred whilst updating repayment: id - ${repaymentId}`, { stack: e, newRepaymentData });
      throw e;
    }

    return {
      status: 'success',
      message: 'repayment successfully updated and history successfully created',
      repayment,
    };
  }

  // should the status of the repayment be updated if the edit cancels out the outstanding amounts?
  // update portfolio amounts and history too
  async updateRepaymentPortfolio({ portfolioId, actionBy }) {
    const { prisma } = this.context;
    let portfolio = null;

    try {
      portfolio = await prisma.portfolio.findUnique({ where: { id: portfolioId }, include: PORTFOLIO_FIELDS });
    } catch (e) {
      errorLogger(`An error occurred whilst attempting to get repayment portfolio: id - ${portfolioId}`, { stack: e });
      throw e;
    }

    const fullAmount = portfolio.repayments.reduce(
      (r1, r2) => r1.principalPortion + r1.interestPortion + r2.principalPortion + r2.interestPortion,
    );
    const lateRepaymentFee = portfolio.repayments.reduce((r1, r2) => r1.lateFee + r2.lateFee);
    const isNotFullyPaid = portfolio.repayments.some(r => r.status.name === REPAYMENT_STATUSES.pending.name);

    const fullAmountUpdate = fullAmount && fullAmount > 0 ? fullAmount : undefined;
    const lateRepaymentFeeUpdate = lateRepaymentFee && lateRepaymentFee > 0 ? lateRepaymentFee : undefined;

    let updatedPortfolio = null;
    try {
      updatedPortfolio = await prisma.portfolio.update({
        where: { id: portfolioId },
        data: {
          fullAmount: fullAmountUpdate,
          lateRepaymentFee: lateRepaymentFeeUpdate,
          status: {
            connect: { name: isNotFullyPaid ? portfolio.status.name : PORTFOLIO_STATUSES.closed.name },
          },
        },
        select: { status: true },
      });
    } catch (e) {
      errorLogger(`An error occurred whilst attempting to update portfolio: id - ${portfolioId}`, { stack: e });
      throw e;
    }

    await prisma.portfolioHistory.create({
      data: {
        actionBy: { connect: { id: actionBy } },
        portfolio: { connect: { id: portfolioId } },
        oldStatus: { connect: { name: portfolio.status.name } },
        newStatus: { connect: { name: updatedPortfolio.status.name } },
        formerFullAmount: portfolio.fullAmount,
        currentFullAmount: fullAmountUpdate,
        formerLateRepaymentFee: portfolio.lateRepaymentFee,
        currentLateRepaymentFee: lateRepaymentFeeUpdate,
        comment: 'Updated repayment schedule',
      },
    });
  }

  getPrincipalAmounts({ portfolio }) {
    let totalPrincipalCollected = 0;
    portfolio.transactions.forEach(transaction => transaction.metadata.forEach((meta) => {
      if (meta.key === 'totalPrincipalCollected') {
        totalPrincipalCollected += meta.value;
      }
    }));

    return {
      totalPrincipalCollected,
      outstandingPrincipal: portfolio.baseAmount - totalPrincipalCollected,
    };
  }

  async editRepaymentSchedule({ schedule = [], portfolioId, accountUser }) {
    const {
      prisma,
      dataSources: { RepaymentAPI },
    } = this.context;

    const portfolio = await prisma.portfolio.findUnique({
      where: { id: portfolioId },
      include: {
        status: true,
        debitMandate: {
          select: {
            id: true,
            endDate: true,
            amount: true,
            debitMandateInstructions: { select: { id: true, status: true, amount: true } },
          },
        },
        debitMandateMeta: {
          select: {
            id: true,
            status: true,
            type: true,
            lidyaCollect: {
              select: {
                id: true,
                collectStatus: true,
              },
            },
          },
        },
      },
    });
    if (!portfolio) throw new Error('Portfolio not found');

    const {
      status: { name: oldStatus },
      debitMandate,
      debitMandateMeta,
    } = portfolio;

    const lidyaMandate = debitMandateMeta && debitMandateMeta.find(dm => dm.type.name === DEBIT_MANDATE_TYPES.lidya.name);

    const pendingRepayments = await prisma.repayment.findMany({
      where: {
        portfolio: { id: portfolioId },
        status: { name: REPAYMENT_STATUSES.pending.name },
        mandateInstruction: null,
      },
      include: {
        lidyaInstallment: true,
      },
    });

    const isOngoingLidyaMandate = lidyaMandate?.lidyaCollect?.collectStatus === COLLECT_STATUS.ongoing.name;
    const syncedLidyaInstallments = pendingRepayments.every(pr => pr.lidyaInstallment);
    if (isOngoingLidyaMandate && !syncedLidyaInstallments) throw new Error('Repayments cannot be edited at this time, please try again later');

    if (pendingRepayments.length < 1) throw new Error('There are no pending repayments');

    let totalPendingPrincipal = 0;
    let totalAmountPaid = 0;
    const newPendingPrincipal = schedule.reduce((a, b) => a + b.newPrincipalAmount, 0);

    if (newPendingPrincipal < 1) throw new Error('Total principal cannot be less than N1');

    let earliestDueDate = pendingRepayments[0].dueDate;
    pendingRepayments.forEach((r) => {
      if (isBefore(new Date(r.dueDate), new Date(earliestDueDate))) {
        earliestDueDate = r.dueDate;
      }
      totalPendingPrincipal += r.principalPortion;
      totalAmountPaid += r.amountPaid;
    });

    if (newPendingPrincipal.toFixed(2) > totalPendingPrincipal.toFixed(2)) {
      throw new Error(
        `New total principal of N${formatAmount(
          newPendingPrincipal,
        )} must be equal to the existing total principal of N${formatAmount(totalPendingPrincipal)}`,
      );
    }

    let invalidDueDate = false;
    const repaymentBreakdown = schedule
      .map(({ newInterestAmount, newPrincipalAmount, newDueDate, newLateFee }) => {
        const dueDate = new Date(newDueDate);
        if (isBefore(dueDate, new Date(earliestDueDate))) {
          invalidDueDate = true;
        }
        return {
          interestPortion: newInterestAmount,
          principalPortion: newPrincipalAmount,
          totalPayment: newPrincipalAmount + newInterestAmount + (newLateFee || 0),
          dueDate: dueDate.toISOString(),
          lateFee: newLateFee,
        };
      })
      .sort((a, b) => (a.dueDate < b.dueDate ? -1 : 1));

    if (invalidDueDate) {
      throw new Error(
        `Invalid date: Due dates must be after the earliest pending repayment due date of ${format(new Date(earliestDueDate), 'DD/MM/YYYY')}`,
      );
    }

    if (debitMandate) {
      const totalPaymentSum = repaymentBreakdown.reduce((acc, curr) => acc + curr.totalPayment, 0);
      const paidInstructionSum = debitMandate.debitMandateInstructions
          ?.filter(dbi => dbi.status?.name === DEBIT_INSTRUCTION_STATUSES.debited.name)
          ?.reduce((acc, curr) => acc + curr.amount, 0) || 0;
      const outstandingPayment = debitMandate.amount - paidInstructionSum;
      if (totalPaymentSum > outstandingPayment) {
        throw new Error(
          `Total payment of N${formatAmount(
            totalPaymentSum,
          )} cannot be greater than maximum mandate amount of N${formatAmount(outstandingPayment)}`,
        );
      }
      if (repaymentBreakdown.some(rb => isAfter(new Date(rb.dueDate), new Date(debitMandate.endDate)))) {
        throw new Error('Invalid date: Due date must be before mandate end date');
      }
    }

    await Promise.all(
      pendingRepayments.map(pR => prisma.repayment.update({
        where: { id: pR.id },
        data: {
          status: { connect: { name: REPAYMENT_STATUSES.cancelled.name } },
        },
      })),
    );

    await RepaymentAPI.createRepaymentData({ repaymentBreakdown, portfolioId });

    const updatedRepayments = await prisma.repayment.findMany({
      where: {
        portfolio: { id: portfolioId },
        status: { name: { in: [REPAYMENT_STATUSES.pending.name, REPAYMENT_STATUSES.paid.name] } },
      },
      include: REPAYMENT_FIELDS,
    });

    const updatedPendingRepayments = updatedRepayments.filter(
      ({ status }) => status.name === REPAYMENT_STATUSES.pending.name,
    );

    await prisma.portfolio.update({
      data: {
        fullAmount: updatedRepayments.reduce((a, b) => a + Number(b.totalPayment), 0),
        chargesAmount: updatedRepayments.reduce((a, b) => a + Number(b.interestPortion), 0),
        lateRepaymentFee: updatedRepayments.reduce((a, b) => a + Number(b.lateFee), 0),
        status: {
          connect: {
            name: PORTFOLIO_STATUSES.disbursed.name,
          },
        },
        loanStatus: {
          connect: {
            name: LOAN_STATUSES.performing.name,
          },
        },
        history: {
          create: {
            oldStatus: { connect: { name: oldStatus } },
            newStatus: { connect: { name: oldStatus } },
            actionBy: { connect: { id: accountUser.id } },
            comment: 'Repayment schedule updated',
          },
        },
      },
      where: { id: portfolioId },
    });

    if (totalAmountPaid > 0) await RepaymentAPI.updateRepayments(updatedPendingRepayments, totalAmountPaid, null, {}, portfolioId);

    const reducedUpdatedRepaymentPayload = updatedPendingRepayments.map(({ id, totalPayment, dueDate }) => ({
      id,
      totalPayment,
      dueDate,
    }));
    publishRepaymentScheduleUpdatedEvent({
      portfolioId,
      schedule: reducedUpdatedRepaymentPayload,
      oldSchedule: pendingRepayments,
      isOngoingLidyaMandate,
    });
    publishUpdateCollectionRepaymentEvent({ portfolioId });

    return {
      status: 'SUCCESS',
    };
  }

  async repaymentScheduleUpdateNotification({ portfolioId }) {
    const {
      prisma,
      dataSources: { AccountAPI, ClientAccountAPI },
    } = this.context;

    const portfolio = await prisma.portfolio.findUnique({
      where: { id: portfolioId },
      include: {
        account: {
          select: {
            id: true,
            parentAccount: {
              select: { id: true },
            },
          },
        },
      },
    });

    if (!portfolio) throw new Error('Portfolio not found');

    const {
      account: {
        id: customerId,
        parentAccount: { id: clientId },
      },
    } = portfolio;
    const customerAccountUser = await AccountAPI.getAccountUserById({
      id: customerId,
      clientId,
    });
    const clientOptions = await ClientAccountAPI.getEmailOptions({
      accountId: clientId,
    });

    const notificationPreference = clientOptions?.customerNotificationPreference;

    if (notificationPreference && notificationPreference?.loanRepayment?.email === false) return;

    this.methods
      .getCustomerRepaymentEditEmail({
        clientOptions,
        customerAccountUser,
      })
      .send();
  }
}
