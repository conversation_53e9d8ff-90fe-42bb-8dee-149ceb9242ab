import { SERVICES, TRANSACTION_TYPES } from '~/config';

export default (transaction) => {
  const paymentMethod = {
    name: null,
    iconUrl: null,
  };

  const { actionBy, toEntity, fromEntity, type } = transaction;

  if (toEntity?.service?.name === SERVICES.bank.name && type?.name === TRANSACTION_TYPES.disbursement.name) {
    paymentMethod.name = 'Bank Transfer';
  }
  if (fromEntity?.service?.name === SERVICES.card.name && type?.name === TRANSACTION_TYPES.repayment.name) {
    paymentMethod.name = 'Debit Card';
  }
  if (
    fromEntity?.service?.name === SERVICES.paystackBankTransfer.name
    && type?.name === TRANSACTION_TYPES.repayment.name
  ) {
    paymentMethod.name = 'Bank Transfer';
  }
  if (fromEntity?.service?.name === SERVICES.bank.name && type?.name === TRANSACTION_TYPES.repayment.name) {
    paymentMethod.name = 'Direct Debit';
  }
  if (toEntity?.service?.name === SERVICES.cash.name || fromEntity?.service?.name === SERVICES.cash.name) {
    paymentMethod.name = actionBy && `${actionBy.firstName} ${actionBy.lastName}`;
  }
  return paymentMethod;
};
