/* eslint-disable no-await-in-loop */
import redis from 'redis';
import { promisify } from 'util';
import { REDIS_HOST, REDIS_PORT, REDIS_PASS, REDIS_DB, isTest } from '~/config';
import { infoLogger } from '~/logger';

let _redis = redis;

if (isTest && process.env.JEST_ENV !== 'e2e') {
  // eslint-disable-next-line import/no-extraneous-dependencies, global-require
  _redis = require('redis-mock');
}

const client = _redis.createClient(REDIS_PORT, REDIS_HOST);
if (REDIS_PASS) {
  client.auth(REDIS_PASS);
}

client.select(REDIS_DB);

const getAsync = promisify(client.get).bind(client);
const setAsync = promisify(client.set).bind(client);
const delAsync = promisify(client.del).bind(client);
const keysAsync = promisify(client.keys).bind(client);
const scanAsync = promisify(client.scan).bind(client);
const closeInstance = promisify(client.quit).bind(client);
const endConnection = promisify(client.end).bind(client);
const existsAsync = promisify(client.exists).bind(client);
const flushDatabase = promisify(client.flushdb).bind(client);
const hgetAsync = promisify(client.hget).bind(client);
const hsetAsync = promisify(client.hset).bind(client);
const hdelAsync = promisify(client.hdel).bind(client);
const expireAsync = promisify(client.expire).bind(client);

const sleep = m => new Promise(r => setTimeout(r, m));

const acquireLock = async (lockId, TIMEOUT) => {
  if (!client.connected) {
    throw new Error('Redis not connected');
  }

  infoLogger(`Acquiring lock for ${lockId} with timeout ${TIMEOUT} seconds`);

  let acquired = false;
  while (!acquired) {
    const ret = await setAsync(lockId, '1', 'EX', TIMEOUT, 'NX');
    if (ret === 'OK') {
      acquired = true;
      infoLogger(`Lock acquired for ${lockId}`);
      return;
    }

    await sleep(1000);
  }
};

// del is a promisified version of client.del
const releaseLock = async (lockId) => {
  await delAsync(lockId);
};

const delMultiAsync = async (pattern) => {
  async function getKeys(cursor = 0, result = []) {
    const [newCursor, keys] = await scanAsync(cursor, 'MATCH', pattern, 'COUNT', '10');
    const cursorInt = parseInt(newCursor, 10);
    keys.forEach((key) => {
      result.push(key);
    });

    if (cursorInt === 0) {
      return result;
    }
    return getKeys(cursorInt, result);
  }

  const allKeys = await getKeys(0);
  const promises = allKeys.map(async key => delAsync(key));
  await Promise.all(promises);
  return true;
};

const redisClient = {
  ...client,
  getAsync,
  setAsync,
  delAsync,
  delMultiAsync,
  keysAsync,
  acquireLock,
  releaseLock,
  closeInstance,
  endConnection,
  existsAsync,
  flushDatabase,
  hgetAsync,
  hsetAsync,
  hdelAsync,
  expireAsync,
};

export { redisClient };
