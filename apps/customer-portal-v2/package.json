{"name": "customer-portal-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 1200", "build": "next build", "start": "next start", "lint": "next lint", "codegen": "graphql-codegen --config codegen.ts", "codegen-watch": "yarn graphql-codegen --watch"}, "dependencies": {"@apollo/client": "^3.11.3", "@apollo/experimental-nextjs-app-support": "^0.11.2", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@indicina1/originate-form-builder": "^2.3.1", "@uselessdev/datepicker": "^2.8.1", "chakra-react-select": "^4.9.1", "chroma-js": "^3.1.2", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "framer-motion": "^11.3.21", "graphql": "^16.9.0", "idle-timer-manager": "^0.0.5", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.424.0", "next": "^14.2.25", "next-auth": "^4.24.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.52.2", "react-number-format": "^4.9.4", "react-number-format-5": "npm:react-number-format@5.0.0", "react-paystack": "^6.0.0", "react-select": "^5.8.0", "sharp": "^0.33.5", "zod": "^3.24.2"}, "devDependencies": {"@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.3.3", "@parcel/watcher": "^2.4.1", "@types/chroma-js": "^3", "@types/js-cookie": "^3", "@types/lodash": "^4", "@types/node": "^20", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "typescript": "^5.7.2"}}