import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import logger from "@/lib/logger";
import { CardInfo } from "@/src/app/(protected)/(cpV2)/_components";
import { appRedirection, getCpV2ClientInfo } from "@/src/app/actions";
import {
  CREATE_APPLICATION_TRACE,
  SKIP_CARD_COLLECTION,
} from "@/src/graphql/mutation";
import {
  GENERATE_PAYSTACK_CARD_REFERENCE,
  GET_PAYSTACK_CARD_REFERENCE,
} from "@/src/graphql/query";
import {
  Application,
  CardReferenceStatusInput,
  CreateApplicationTraceInput,
  GenerateAddCardReferenceInput,
  SkipCardCollectionInput,
  Viewer,
} from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Add Debit Card",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        email
      }
      account {
        id
        channel {
          type
        }
        cards {
          id
        }
      }
    }
  }
`;

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      customApplicationForm
      loanDuration
      loanCategory {
        id
        products {
          applicationForm
          id
        }
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
      }
      
      bankAccount {
        id
        bank {
          id
          name
        }
      }
    }
  }
`;

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId && !input?.comment) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

const generateByPayStackRefAction = async ({
  accessToken,
  input,
}: {
  input: GenerateAddCardReferenceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.clientId) {
      return;
    }

    const response = await graphqlRequest(
      GENERATE_PAYSTACK_CARD_REFERENCE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return { data: response?.data?.getAddCardReference, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const runSkipCardCollectionAction = async ({
  accessToken,
  input,
}: {
  input: SkipCardCollectionInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const response = await graphqlRequest(
      SKIP_CARD_COLLECTION,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return { data: response?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const getByPayStackRefAction = async ({
  accessToken,
  input,
}: {
  input: CardReferenceStatusInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.reference) {
      return;
    }

    const response = await graphqlRequest(
      GET_PAYSTACK_CARD_REFERENCE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return { data: response?.data?.getCardReferenceStatus, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

interface PageProps {
  params: { id: string };
}

const CardPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);

  const applicationNumber = params?.id;

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const application: Application = applicationQuery?.data?.application;
  const applicationId = application?.id;

  const clientInfo = await getCpV2ClientInfo({ basePath: null });
  const clientId = clientInfo?.clientId!;
  const hasCard = !!viewerData?.account?.cards?.length;

  const createAppTrace = async ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => {
    "use server";
    return createTrace({
      input: {
        page,
        comment,
        applicationId,
        isDebug,
        metadata,
      },
      accessToken: accessToken!,
    });
  };

  const generateByPayStackRef = async ({
    metadata,
  }: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => {
    "use server";
    return generateByPayStackRefAction({
      input: {
        clientId,
        metadata,
      },
      accessToken,
    });
  };

  const runSkipCardCollection = async () => {
    "use server";
    return runSkipCardCollectionAction({
      input: {
        applicationId,
      },
      accessToken,
    });
  };

  const getByPayStackRef = async ({
    reference,
    loanDuration,
  }: {
    reference: CardReferenceStatusInput["reference"];
    loanDuration: CardReferenceStatusInput["loanDuration"];
  }) => {
    "use server";
    return getByPayStackRefAction({
      input: {
        reference,
        loanDuration,
      },
      accessToken,
    });
  };

  const result = await appRedirection({ basePath: null });

  if (result !== `/application/${applicationNumber}/add-card`) {
    redirect(result!);
  }

  return (
    <CardInfo
      viewer={viewerData}
      application={application}
      applicationNumber={applicationNumber}
      createAppTrace={createAppTrace}
      generateByPayStackRef={generateByPayStackRef}
      runSkipCardCollection={runSkipCardCollection}
      getByPayStackRef={getByPayStackRef}
    />
  );
};
export default CardPage;
