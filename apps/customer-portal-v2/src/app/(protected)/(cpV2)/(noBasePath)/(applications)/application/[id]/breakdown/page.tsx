import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import logger from "@/lib/logger";
import { ConfirmApplication } from "@/src/app/(protected)/(cpV2)/_components";
import { appRedirection } from "@/src/app/actions";
import {
  COMPLETE_APPLICATION,
  CREATE_APPLICATION_TRACE,
} from "@/src/graphql/mutation";
import { APPLICATION_STEPS } from "@/utils/constants";
import {
  Application,
  ApplicationStageInput,
  CompleteApplicationInput,
  CreateApplicationTraceInput,
  PotentialOffer,
} from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Confirm Loan Application",
};

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      dateOfRepayment
      completedSteps
    }
  }
`;

const GENERATE_APPLICATION_OFFERS = `
  mutation GenerateApplicationOffers($input: ApplicationStageInput!) {
    generateApplicationOffers(input: $input) { 
      potentialOffer {
        id
        baseAmount
        fullAmount
        interestRate
        loanTenor
        policy {
          id
          processingFee {
            calcBy
            name
            type
            value
          }
          interestRate {
            value
          }
          processingFeeDebitMethod
        }
        repaymentBreakdown {
          principalBalance
          expectedPayment
          interestPortion
          principalPortion
          endingBalance
          dueDate
        }
        repaymentType
      }
    }
  }
`;

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

const completeApplicationAction = async ({
  input,
  accessToken,
}: {
  input: CompleteApplicationInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const completeApp = await graphqlRequest(
      COMPLETE_APPLICATION,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (completeApp?.errors?.length) {
      return {
        error: completeApp?.errors[0]?.message,
        data: null,
      };
    }

    if (completeApp?.data) {
      const CookieStore = cookies();
      CookieStore.delete("skippedCardAddition");
      return {
        data: completeApp?.data,
        error: null,
      };
    }
  } catch (error: any) {
    return {
      error: error?.message,
      data: null,
    };
  }
};

const getOffers = async ({
  input,
  accessToken,
}: {
  input: ApplicationStageInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const offers = await graphqlRequest(
      GENERATE_APPLICATION_OFFERS,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (offers?.errors?.length) {
      return {
        error: offers?.errors[0]?.message,
        data: null,
      };
    }

    if (offers?.data) {
      return {
        data: offers?.data,
        error: null,
      };
    }
  } catch (error: any) {
    return {
      error: error?.message,
      data: null,
    };
  }
};

interface PageProps {
  params: { id: string };
}

const BreakDownPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const applicationNumber = params?.id;
  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const application: Application = applicationQuery?.data?.application;
  const applicationId = application?.id;

  const createAppTrace = async ({
    page,
    comment,
    isDebug,
    metadata,
  }: {
    page: CreateApplicationTraceInput["page"];
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
  }) => {
    "use server";
    return createTrace({
      input: {
        applicationId,
        page,
        comment,
        isDebug,
        metadata,
      },
      accessToken,
    });
  };

  const completeApplication = async () => {
    "use server";
    return completeApplicationAction({
      input: {
        applicationId,
      },
      accessToken,
    });
  };

  const generateApplicationOffers = async () => {
    "use server";
    return getOffers({
      input: {
        applicationId,
      },
      accessToken,
    });
  };

  const result = await generateApplicationOffers();
  const data = result?.data;
  const error: string = result?.error;

  const potentialOffer: PotentialOffer =
    data?.generateApplicationOffers?.potentialOffer;

  const redirectResult = await appRedirection({ basePath: null });

  if (redirectResult !== `/application/${applicationNumber}/breakdown`) {
    redirect(redirectResult!);
  }
  return (
    <ConfirmApplication
      application={application}
      potentialOffer={potentialOffer}
      error={error}
      createAppTrace={createAppTrace}
      completeApplication={completeApplication}
    />
  );
};
export default BreakDownPage;
