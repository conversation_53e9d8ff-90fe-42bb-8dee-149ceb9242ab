import { Box } from "@chakra-ui/react";
import { ReactNode } from "react";
import React from "react";
import { Metadata } from "next";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Loan Appplication Pages",
};

type Props = {
  children: ReactNode;
  params: { id: string };
};
const WidgetAppLayout = async ({ children, params }: Props) => {
  return <Box>{children}</Box>;
};

export default WidgetAppLayout;
