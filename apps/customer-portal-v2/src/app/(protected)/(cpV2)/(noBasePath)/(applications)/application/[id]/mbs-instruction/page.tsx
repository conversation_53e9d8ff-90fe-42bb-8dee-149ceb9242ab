import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import logger from "@/lib/logger";
import { MbsInstructions } from "@/src/app/(protected)/(cpV2)/_components";
import { appRedirection } from "@/src/app/actions";
import {
  CREATE_APPLICATION_TRACE,
  EXTERNAL_BANK_STATEMENT_REQUEST,
} from "@/src/graphql/mutation";
import { APPLICATION_STEPS } from "@/utils/constants";
import { getAttributrFromProduct } from "@/utils/index";
import {
  Application,
  CompleteExternalBankStatementRequestInput,
  CreateApplicationTraceInput,
  Viewer,
} from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "MBS Instructions",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      account {
        bankAccounts {
          id
          bank {
            id
            name
            code
          }
          accountName
          accountNumber
          status
          isDefault
        }
      }
    }
  }
`;
const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      completedSteps
      bankAccount {
        accountName
        accountNumber
        bank {
          code
          id
          name
        }
        id
        isDefault
        status
      }
      requiredSteps
      loanCategory {
        id
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
      } 
    }
  }
`;

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

const externalBankStatementRequestAction = async ({
  accessToken,
  input,
}: {
  input: CompleteExternalBankStatementRequestInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const res = await graphqlRequest(
      EXTERNAL_BANK_STATEMENT_REQUEST,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return {
        error: res?.errors?.[0]?.message,
        data: null,
        success: false,
        redirect: false,
      };
    }

    if (res?.data?.completeExternalBankStatementRequest?.success) {
      return { data: res?.data, error: null, success: true, redirect: true };
    }
  } catch (error: any) {
    return {
      error: error?.message,
      data: null,
      success: false,
      redirect: false,
    };
  }
};

interface PageProps {
  params: { id: string };
}

const MbsInstructionPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const applicationNumber = params?.id;
  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const application: Application = applicationQuery?.data?.application;
  const applicationId = application?.id;
  const loanCategory = application?.loanCategory;

  const namesSet = new Set(["requiresBankStatementUpload"]);
  const loanCategoryAttributes = loanCategory?.loanCategoryAttributes!;

  const applicationAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const requiresBankStatementUpload: boolean =
    applicationAttributes?.requiresBankStatementUpload;

  const createAppTrace = async ({
    page,
    comment,
    isDebug,
    metadata,
  }: {
    page: CreateApplicationTraceInput["page"];
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
  }) => {
    "use server";
    return createTrace({
      input: {
        applicationId,
        page,
        comment,
        isDebug,
        metadata,
      },
      accessToken,
    });
  };

  const externalBankStatementRequest = async ({
    password,
    skipStep,
    ticketNum,
  }: {
    password?: CompleteExternalBankStatementRequestInput["password"];
    skipStep?: CompleteExternalBankStatementRequestInput["skipStep"];
    ticketNum?: CompleteExternalBankStatementRequestInput["ticketNum"];
  }) => {
    "use server";
    return externalBankStatementRequestAction({
      input: {
        applicationId,
        password,
        skipStep,
        ticketNum,
      },
      accessToken,
    });
  };

  const result = await appRedirection({ basePath: null });

  if (result !== `/application/${applicationNumber}/mbs-instruction`) {
    redirect(result!);
  }

  return (
    <MbsInstructions
      application={application}
      viewer={viewerData}
      applicationNumber={applicationNumber}
      externalBankStatementRequest={externalBankStatementRequest}
      createAppTrace={createAppTrace}
      requiresBankStatementUpload={requiresBankStatementUpload}
    />
  );
};

export default MbsInstructionPage;
