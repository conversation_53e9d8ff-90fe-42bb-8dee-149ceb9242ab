import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import logger from "@/lib/logger";
import { Mbs } from "@/src/app/(protected)/(cpV2)/_components";
import { appRedirection } from "@/src/app/actions";
import {
  COMPLETE_BANK_STATEMENT_REQUEST,
  CREATE_APPLICATION_TRACE,
  INITIATE_BANK_STATEMENT_REQUEST,
} from "@/src/graphql/mutation";
import { getAttributrFromProduct } from "@/utils/index";
import {
  Application,
  CompleteBanksStatementInput,
  CreateApplicationTraceInput,
  InitiateBanksStatementInput,
  Viewer,
} from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "MBS",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      account {
        bankAccounts {
          id
          bank {
            id
            name
            code
          }
          accountName
          accountNumber
          status
          isDefault
        }
      }
    }
  }
`;
const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      bankAccount {
        accountName
        accountNumber
        bank {
          code
          id
          name
        }
        id
        isDefault
        status
      }
      requiredSteps
      completedSteps
      credit {
        ticketNo
      }
      loanCategory {
        id
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
      } 
    }
  }
`;

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

const triggerOtpAction = async ({
  accessToken,
  input,
}: {
  input: InitiateBanksStatementInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.accountBankId && !input?.applicationId) {
      return;
    }
    const res = await graphqlRequest(
      INITIATE_BANK_STATEMENT_REQUEST,
      "POST",
      {
        input,
      },
      accessToken
    );
    if (res?.errors?.length) {
      return { success: false, error: res?.errors?.[0]?.message };
    }

    if (res?.data) {
      return { success: true, error: null };
    }
    return { success: false, error: null };
  } catch (error: any) {
    return { success: false, error: error?.message };
  }
};

const completeBankStatementRequestAction = async ({
  accessToken,
  input,
}: {
  input: CompleteBanksStatementInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }
    const res = await graphqlRequest(
      COMPLETE_BANK_STATEMENT_REQUEST,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return {
        error: res?.errors?.[0]?.message,
        data: null,
        success: false,
        redirect: false,
      };
    }

    if (res?.data?.completeBankStatementRequest?.success) {
      return { data: res?.data, error: null, success: true, redirect: true };
    }
  } catch (error: any) {
    return {
      error: error?.message,
      data: null,
      success: false,
      redirect: false,
    };
  }
};

interface PageProps {
  params: { id: string };
}

const MbsPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const applicationNumber = params?.id;
  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const application: Application = applicationQuery?.data?.application;
  const applicationId = application?.id;
  const accountBankId = application?.bankAccount?.bank?.id;
  const loanCategory = application?.loanCategory;

  const namesSet = new Set(["requiresBankStatementUpload"]);
  const loanCategoryAttributes = loanCategory?.loanCategoryAttributes!;

  const applicationAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const requiresBankStatementUpload: boolean =
    applicationAttributes?.requiresBankStatementUpload;
  const createAppTrace = async ({
    page,
    comment,
    isDebug,
    metadata,
  }: {
    page: CreateApplicationTraceInput["page"];
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
  }) => {
    "use server";
    return createTrace({
      input: {
        applicationId,
        page,
        comment,
        isDebug,
        metadata,
      },
      accessToken,
    });
  };

  const completeBankStatementRequest = async ({
    password,
    skipStep,
    ticketNum,
  }: {
    password?: CompleteBanksStatementInput["password"];
    skipStep?: CompleteBanksStatementInput["skipStep"];
    ticketNum?: CompleteBanksStatementInput["ticketNum"];
  }) => {
    "use server";
    return completeBankStatementRequestAction({
      input: {
        applicationId,
        password,
        skipStep,
        ticketNum,
      },
      accessToken,
    });
  };

  const triggerOtp = async ({
    isRetried,
  }: {
    isRetried?: InitiateBanksStatementInput["isRetried"];
  }) => {
    "use server";
    return triggerOtpAction({
      accessToken,
      input: {
        accountBankId,
        applicationId,
        isRetried,
      },
    });
  };

  const result = await appRedirection({ basePath: null });

  if (result !== `/application/${applicationNumber}/mbs`) {
    redirect(result!);
  }

  return (
    <Mbs
      application={application}
      viewer={viewerData}
      applicationNumber={applicationNumber}
      createAppTrace={createAppTrace}
      triggerOtp={triggerOtp}
      completeBankStatementRequest={completeBankStatementRequest}
      requiresBankStatementUpload={requiresBankStatementUpload}
    />
  );
};

export default MbsPage;
