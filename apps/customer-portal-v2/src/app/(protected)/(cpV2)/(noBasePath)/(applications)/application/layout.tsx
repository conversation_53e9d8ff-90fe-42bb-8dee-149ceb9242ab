import { Center, Container } from "@chakra-ui/react";
import { ReactNode } from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export default async function ApplicationLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <Center
      bg='#f2f2f2'
      w='full'
      h='full'
    >
      <Container
        maxW='max-content'
        mx={{ base: 0, md: "auto" }}
        px={0}
        overflow='hidden'
        my={4}
      >
        {children}
      </Container>
    </Center>
  );
}
