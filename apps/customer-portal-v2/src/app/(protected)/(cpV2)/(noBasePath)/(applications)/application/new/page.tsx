import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import {
  appRedirection,
  getCpV2ClientInfo,
  getLoanInitiationData,
  signUpRedirection,
} from "@/src/app/actions";
import { GET_LOAN_CATEGORIES } from "@/src/graphql/query";
import { LoanInitiationProvider } from "@/src/services";
import { LoanCategory, Viewer } from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";
import { LoanInitiation } from "../../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "New Loan Application",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        kycInformation
        isEmailConfirmed
        bvnStatus {
          bvn
        }
      }
      account {
        applications {
          nodes {
            status {
              name
            }
          }
        }
        cards {
          id
          expiryDate
          email
          bankName
          isDefault
          maskedPan
          status
          type
        }
      }
    }
  }
`;

const LoanInitiationPage = async () => {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const clientInfo = await getCpV2ClientInfo({ basePath: null });
  const clientId = clientInfo?.clientId!;

  const loanCategoriesQuery = await graphqlRequest(
    GET_LOAN_CATEGORIES,
    "POST",
    {},
    accessToken
  );

  const loanCategoriesData: LoanCategory[] =
    loanCategoriesQuery?.data?.getLoanCategories?.categories;

  const loanInitiationData = async ({
    amount,
    selectedCategoryId,
  }: {
    selectedCategoryId?: string;
    amount?: number;
  }) => {
    "use server";
    const {
      applicableTenor,
      eligibleAmount,
      errors: initiationError,
    } = await getLoanInitiationData({
      clientId,
      amount,
      selectedCategoryId,
      token: accessToken,
    });

    return {
      applicableTenor,
      eligibleAmount,
      initiationError,
    };
  };

  const result = await appRedirection({ basePath: null });
  const redirectResult = await signUpRedirection({
    basePath: null,
  });

  const applicationCount =
    viewerData?.account?.applications?.nodes?.length || 0;

  const pendingApplication = viewerData?.account?.applications?.nodes?.find(
    (app: any) => app?.status?.name === "PENDING"
  );

  if (applicationCount === 0 && redirectResult !== "/application/new") {
    redirect(redirectResult);
  }

  if (pendingApplication && result !== "/application/new") {
    redirect(result!);
  }

  return (
    <LoanInitiationProvider
      cards={viewerData?.account?.cards}
      token={accessToken}
    >
      <LoanInitiation
        data={loanCategoriesData}
        error={loanCategoriesQuery?.data?.errors?.[0]}
        loanInitiationData={loanInitiationData}
      />
    </LoanInitiationProvider>
  );
};
export default LoanInitiationPage;
