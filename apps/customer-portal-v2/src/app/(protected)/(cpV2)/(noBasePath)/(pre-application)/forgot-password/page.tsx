import React from "react";
import { graphqlRequest } from "@/lib/graphql-client";
import { getCpV2ClientInfo, loginRedirection } from "@/src/app/actions";
import { TriggerPasswordResetInput } from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";
import { ForgotPasswordPage } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Forgot Password",
};

const TRIGGER_PASSWORD_RESET_MUTATION = `
  mutation TriggerPasswordResetMutation($input: TriggerPasswordResetInput!) {
    triggerPasswordReset(input: $input) {
      ok
    }
  }
`;

const forgotPasswordAction = async ({
  input,
}: {
  input: TriggerPasswordResetInput;
}) => {
  "use server";
  try {
    const result = await graphqlRequest(
      TRIGGER_PASSWORD_RESET_MUTATION,
      "POST",
      {
        input,
      }
    );

    if (result?.errors?.length) {
      throw new Error(result?.errors[0]?.message);
    }

    return { data: result?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const ForgotPassword = async () => {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken!;

  if (accessToken) {
    return await loginRedirection({ basePath: null });
  }

  const clientInfo = await getCpV2ClientInfo({ basePath: null });
  const clientId = clientInfo?.clientId!;

  const forgotPassword = async ({
    email,
  }: {
    email: TriggerPasswordResetInput["email"];
  }) => {
    "use server";

    return forgotPasswordAction({
      input: {
        email,
        clientId,
      },
    });
  };

  return <ForgotPasswordPage forgotPassword={forgotPassword} />;
};

export default ForgotPassword;
