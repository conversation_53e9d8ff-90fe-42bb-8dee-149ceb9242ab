import { ReactNode } from "react";
import { Container, Flex } from "@chakra-ui/react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

interface LayoutProps {
  children: ReactNode;
}

export default async function UnAuthLayout({ children }: LayoutProps) {
  return (
    <Flex
      bg='customPrimary.500'
      w='full'
      h='full'
      justifyContent='center'
      alignItems='center'
    >
      <Container
        maxW='max-content'
        mx={{ base: 0, md: "auto" }}
        px={0}
        overflow='hidden'
      >
        {children}
      </Container>
    </Flex>
  );
}
