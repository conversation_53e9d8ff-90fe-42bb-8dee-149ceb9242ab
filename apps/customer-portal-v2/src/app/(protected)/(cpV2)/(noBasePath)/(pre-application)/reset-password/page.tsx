import React from "react";
import { graphqlRequest } from "@/lib/graphql-client";
import { ResetPasswordInput } from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";
import { loginRedirection } from "@/src/app/actions";
import { ResetPasswordPage } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Reset Password",
};

const RESET_PASSWORD_MUTATION = `
  mutation ResetPasswordMutation($input: ResetPasswordInput!) {
    resetPassword(input: $input) {
      ok
    }
  }
`;

const resetPasswordAction = async ({
  input,
}: {
  input: ResetPasswordInput;
}) => {
  "use server";
  try {
    const result = await graphqlRequest(RESET_PASSWORD_MUTATION, "POST", {
      input,
    });

    if (result?.errors?.length) {
      throw new Error(result?.errors[0]?.message);
    }

    return { data: result?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

type PageProps = {
  params: {
    resetToken: string;
  };
  searchParams: {
    resetToken: string;
  };
};

const ResetPassword = async ({ params, searchParams }: PageProps) => {
  const session = await getServerSession(authOptions);

  const resetToken = searchParams?.resetToken;

  const accessToken = session?.accessToken!;

  if (accessToken) {
    return await loginRedirection({ basePath: null });
  }

  const resetPassword = async ({
    password,
  }: {
    password: ResetPasswordInput["password"];
  }) => {
    "use server";

    return resetPasswordAction({
      input: {
        password,
        resetToken,
      },
    });
  };
  return <ResetPasswordPage resetPassword={resetPassword} />;
};

export default ResetPassword;
