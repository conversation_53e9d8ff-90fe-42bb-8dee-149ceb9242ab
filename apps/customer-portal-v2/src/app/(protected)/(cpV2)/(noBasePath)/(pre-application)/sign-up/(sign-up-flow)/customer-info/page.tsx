import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { CustomerInfo } from "@/src/app/(protected)/(cpV2)/_components";
import { signUpRedirection } from "@/src/app/actions";
import { Viewer } from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Customer Information",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        id
        firstName
        lastName
        kycInformation
        isEmailConfirmed
        bvnStatus {
          bvn
        }
      }
    }
  }
`;

const CustomerInfoPage = async () => {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const user = viewerData?.me;
  const redirectResult = await signUpRedirection({
    basePath: null,
  });

  if (redirectResult !== `/sign-up/customer-info`) {
    redirect(redirectResult);
  }

  return (
    <CustomerInfo
      user={user}
      token={accessToken}
    />
  );
};
export default CustomerInfoPage;
