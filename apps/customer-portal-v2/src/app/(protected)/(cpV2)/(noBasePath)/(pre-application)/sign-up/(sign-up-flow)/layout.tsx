import { Container } from "@chakra-ui/react";
import React, { ReactNode } from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

const SignUpLayout = async ({ children }: { children: ReactNode }) => {
  return (
    <Container
      maxW='max-content'
      px={{ base: 0, md: "auto" }}
      mx={{ base: 0, md: "auto" }}
      my={{
        base: 16,
        md: 8,
      }}
      overflow='hidden'
      boxShadow='2xl'
      borderRadius='lg'
      bg='white'
    >
      {children}
    </Container>
  );
};

export default SignUpLayout;
