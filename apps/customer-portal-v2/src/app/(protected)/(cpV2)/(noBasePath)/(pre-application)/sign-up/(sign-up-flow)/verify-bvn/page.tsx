import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { BvnVerify } from "@/src/app/(protected)/(cpV2)/_components";
import { getCpV2ClientInfo, signUpRedirection } from "@/src/app/actions";
import { CustomerBvnStatusInput, Viewer } from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Verify Bvn",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      account {
        id
      }
      me {
        bvnStatus {
          bvn
          dateOfBirth
          firstName
          id
          phone
          lastName 
        }
      }
    }
  }
`;

const CUSTOMER_BVN_STATUS = `
  query CustomerBvnStatus($input: CustomerBvnStatusInput!) {
  customerBvnStatus(input: $input) {
    name
    firstName
    lastName
    phone
    hasCustomerAccount
    isVerified
  }
}
`;

const getCustomerBvn = async ({
  accessToken,
  input,
}: {
  accessToken: string;
  input: CustomerBvnStatusInput;
}) => {
  "use server";
  try {
    const result = await graphqlRequest(
      CUSTOMER_BVN_STATUS,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (result?.errors?.length) {
      throw new Error(result?.errors[0]?.message);
    }

    return { data: result?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const BvnVerificationPage = async () => {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const clientInfo = await getCpV2ClientInfo({ basePath: null });
  const clientId = clientInfo?.clientId!;
  const account = viewerData?.account;

  const getCustomerBvnStatus = async ({
    bvn,
    bvnDOB,
    bvnPhoneDigits,
  }: {
    bvn: CustomerBvnStatusInput["bvn"];
    bvnDOB: CustomerBvnStatusInput["bvnDOB"];
    bvnPhoneDigits: CustomerBvnStatusInput["bvnPhoneDigits"];
  }) => {
    "use server";

    return getCustomerBvn({
      accessToken,
      input: {
        bvn,
        clientId,
        bvnDOB,
        accountId: account?.id,
        bvnPhoneDigits,
      },
    });
  };

  const redirectResult = await signUpRedirection({ basePath: null });

  if (redirectResult !== `/sign-up/verify-bvn`) {
    redirect(redirectResult);
  }

  return <BvnVerify getCustomerBvnStatus={getCustomerBvnStatus} />;
};

export default BvnVerificationPage;
