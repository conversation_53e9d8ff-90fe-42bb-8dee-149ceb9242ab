import { loginRedirection } from "@/src/app/actions";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";
import { RegisterPage } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Sign Up",
};

const Register = async () => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken!;

  if (accessToken) {
    return await loginRedirection({ basePath: null });
  }

  return <RegisterPage />;
};

export default Register;
