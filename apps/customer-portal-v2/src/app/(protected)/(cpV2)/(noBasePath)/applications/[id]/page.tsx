import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { Application, ApplicationStatusEnum } from "@/__generated/graphql";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      status {
        name
      }
      loanCategory {
        id
        products {
          applicationForm
          id
        }
      }
      customApplicationForm
    }
  }
`;

type PageProps = {
  params: {
    id: string;
  };
};

const ApplicationDetailsPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const applicationNumber = params?.id;
  const accessToken = session?.accessToken;

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );
  const application: Application = applicationQuery?.data?.application!;
  const status = application?.status?.name;

  if (status !== ApplicationStatusEnum.Denied) {
    const firstTab =
      application?.loanCategory?.products?.[0]?.applicationForm.filter(
        (tab: any) =>
          !tab?.linkedToOption &&
          !tab?.builders.some(
            (builder: { title: string }) =>
              builder?.title === "Card" || builder?.title === "Bank Info"
          )
      )[0];

    redirect(
      `/applications/${applicationNumber}/${
        firstTab
          ? firstTab?.name.trim().toLowerCase().replace(/\s+/g, "-")
          : "payment-options"
      }`
    );
  } else {
    redirect(`/applications/${applicationNumber}`);
  }
};

export default ApplicationDetailsPage;
