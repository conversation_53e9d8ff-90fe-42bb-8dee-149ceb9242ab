import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { appRedirection } from "@/src/app/actions";
import { Viewer } from "@/__generated/graphql";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { LoanInfo } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

const VIEWER_QUERY = `
  query Account {
    viewer {
      account {
        portfolios {
          nodes {
            amountPaid
            fullAmount
            portfolioNumber
            status {
              name
            }
            dateOfRepayment
            repayments {
              status {
                name
              }
              dueDate
              id
              totalPayment
              outstandingPayment
            }
          }
        }
        applications {
          nodes {
            dateOfRepayment
            applicationNumber
            fullAmount
            completedSteps
            user {
              isEmailConfirmed
              isPhoneConfirmed
            }
            account {
              cards {
                id
              }
              bankAccounts {
                id
              }
            }
            portfolio {
              portfolioNumber
              amountPaid
            }
            status {
              name
            }
            loanCategory {
              id
              loanCategoryAttributes {
                attribute {
                  id
                  name
                }
                value
              }
            }
          }
        }
      }
    }
  }
`;

const LoanInfoPage = async () => {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const result = await appRedirection({ basePath: null });

  return (
    <LoanInfo
      viewer={viewerData}
      redirectLink={result!}
    />
  );
};

export default LoanInfoPage;
