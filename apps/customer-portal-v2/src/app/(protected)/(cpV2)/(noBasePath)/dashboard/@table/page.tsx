import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { omitBy, isEmpty } from "lodash";
import { Tables } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

const VIEWER_QUERY = `
  query Account($where: ApplicationWhereInput, $after: ConnectionCursor, $first: ConnectionLimitInt) {
    viewer {
      account {
        applications(where: $where, after: $after, first: $first) {
          edges {
            node {
              applicationNumber
              baseAmount
              fullAmount
              id
              status {
                name
              }
              displayStatus {
                name
              }
              portfolio {
                portfolioNumber
                status {
                  name
                }
              }
              loanCategory {
                id
                products {
                  applicationForm
                  id
                }
              }
              customApplicationForm
              dateOfRepayment
              createdAt
            }
            cursor
          }
          totalCount
          pageInfo {
            endCursor
            hasNextPage
            startCursor
            hasPreviousPage
          }
        }
        portfolios {
          nodes {
            id
            status {
              name
            }
          }
        }
      }
    }
  }
`;

interface PageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

const TablesPage = async ({ searchParams }: PageProps) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  if (!accessToken) redirect(`/`);

  const search = (searchParams.search as string) || "";
  const page = 10;

  let variables: { where: any; first: number; after?: string } = {
    where: {},
    first: page,
  };

  // Build search filter - search in both application number and portfolio number
  const cleanSearch = search.trim();
  if (cleanSearch) {
    omitBy(
      (variables.where = {
        applicationNumber: cleanSearch.toUpperCase(),
      }),
      isEmpty
    );
  }

  let data = null;
  let error = null;

  try {
    const res = await graphqlRequest(
      VIEWER_QUERY,
      "POST",
      variables,
      accessToken
    );

    if (res?.errors && res.errors.length > 0) {
      error = res.errors[0].message;
    } else if (res?.data) {
      data = res.data.viewer?.account;
    } else {
      error = "No data received from server";
    }
  } catch (err) {
    error =
      err instanceof Error
        ? err.message
        : "An error occurred while fetching data";
  }

  const enhancedSearchParams = {
    ...searchParams,
    search: search,
  };

  return (
    <Tables
      initialData={data}
      searchParams={enhancedSearchParams}
      error={error}
    />
  );
};

export default TablesPage;
