import { BreadCrumbs } from "@/components/BreadCrumbs";
import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { Application, Viewer } from "@/__generated/graphql";
import { SimpleGrid, Container, Flex } from "@chakra-ui/react";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { ReactNode } from "react";
import { Banners, NoData } from "../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Dashboard",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        id
        isEmailConfirmed
        bvnStatus {
          bvn
        }
      }
      account {
        applications {
          nodes {
            id
            status {
              name
            }
          }
        }
        portfolios {
          nodes {
            id
            status {
              name
            }
          }
        }
      }
    }
  }
`;

export default async function DashboardLayout({
  table,
  loanInfo,
}: {
  table: ReactNode;
  loanInfo: ReactNode;
}) {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken!;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;

  const latestApplication = viewerData?.account?.applications
    ?.nodes?.[0] as Application;

  const hasApplication = !!viewerData?.account?.applications?.nodes?.length;

  const currentLoan = viewerData?.account?.portfolios?.nodes?.find(
    (app: any) => app?.status?.name !== "CLOSED"
  );

  const showApplyButton = Boolean(
    currentLoan?.id ||
      latestApplication?.status?.name === "UNDER_REVIEW" ||
      latestApplication?.status?.name === "PENDING" ||
      latestApplication?.status?.name === "AWAITING_FEEDBACK"
  );
  const hasConfirmedEmail = !!viewerData?.me?.isEmailConfirmed!;
  const hasConfirmedBvn = !!viewerData?.me?.bvnStatus?.bvn!;

  return (
    <Container
      maxW={{ base: "100vw", md: "8xl" }}
      px={{ base: 0, md: 8 }}
      mx={{ base: 0, md: "auto" }}
      w='full'
    >
      <SimpleGrid
        columns={1}
        spacing={4}
        w='full'
        py={4}
      >
        <Flex
          justifyContent='space-between'
          alignContent='center'
        >
          <BreadCrumbs showApplyButton={showApplyButton} />
        </Flex>

        {hasApplication ? (
          <>
            <Banners
              hasConfirmedEmail={hasConfirmedEmail}
              hasConfirmedBvn={hasConfirmedBvn}
            />
            {showApplyButton && loanInfo}
            {table}
          </>
        ) : (
          <NoData />
        )}
      </SimpleGrid>
    </Container>
  );
}
