import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { Viewer } from "@/__generated/graphql";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";
import LoanTabs from "../../../../_components/LoanTabz/LoanTabs";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

const VIEWER_QUERY = `
 query ViewerQuery ($after: ConnectionCursor, $first: ConnectionLimitInt) {
     viewer {
      account {
        portfolios {
          edges {
            node {
            status {
              name
            }
            repayments {
              status {
                name
              }
              dueDate
              id
              outstandingPayment
              principalPortion
              interestPortion
            }
            transactions(after: $after, first: $first) {
                pageInfo {
                  endCursor
                  hasNextPage
                  startCursor
                  hasPreviousPage
                }
                totalCount
                edges {
                  cursor
                  node {
                    amount
                    createdAt
                    id
                    paymentMethod {
                      name
                      iconUrl
                    }
                    status
                    type {
                      name
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;

const VIEWER_QUERY_DATA = `
query ViewerQuery {
  viewer {
    account {
      portfolios {
        nodes {
          status {
            name
          }
          repayments {
            status {
              name
            }
            dueDate
            id
            outstandingPayment
            principalPortion
            interestPortion
          }
        }
      }
    }
  }
}
`;

const LoansTabsPage = async () => {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY_DATA,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;

  const page = 10;

  let variables: { first: number; after?: string } = {
    first: page,
  };

  let data = null;
  let error = null;

  try {
    const res = await graphqlRequest(
      VIEWER_QUERY,
      "POST",
      variables,
      accessToken
    );

    if (res?.errors && res.errors.length > 0) {
      error = res.errors[0].message;
    } else if (res?.data) {
      const account = res.data.viewer?.account;
      const portfolios = account?.portfolios?.edges || [];

      if (portfolios.length > 0) {
        const transactions = portfolios[0]?.node?.transactions;
        data = {
          transactions: transactions?.edges || [],
          pageInfo: transactions?.pageInfo || {
            hasNextPage: false,
            endCursor: null,
          },
          totalCount: transactions?.totalCount || 0,
        };
      } else {
        data = {
          transactions: [],
          pageInfo: { hasNextPage: false, endCursor: null },
          totalCount: 0,
        };
      }
    } else {
      error = "No data received from server";
    }
  } catch (err) {
    error =
      err instanceof Error
        ? err.message
        : "An error occurred while fetching data";
  }
  return (
    <LoanTabs
      viewer={viewerData}
      initialData={data}
      error={error}
    />
  );
};

export default LoansTabsPage;
