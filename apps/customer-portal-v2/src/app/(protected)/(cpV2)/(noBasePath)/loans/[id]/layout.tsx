import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { Viewer } from "@/__generated/graphql";
import { SimpleGrid, Container, Center } from "@chakra-ui/react";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { ReactNode } from "react";
import { LoanDetails, LoanTabs } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Loan Details",
};

const VIEWER_QUERY = `
  query Account {
    viewer {
      account {
        portfolios {
          nodes {
            createdAt
            amountDisbursed
            policy {
              interestRate {
                value
                type
                name
                calcBy
              }
              graduatedLoanCycles {
                amount
                interestRate
              }
            }
            amountPaid
            fullAmount
            portfolioNumber
            status {
              name
            }
            dateOfRepayment
            repayments {
              status {
                name
              }
              dueDate
              id
              totalPayment
              outstandingPayment
            }
          }
        }
        applications {
          nodes {
            dateOfRepayment
            applicationNumber
            fullAmount
            completedSteps
            user {
            isEmailConfirmed
            isPhoneConfirmed
            }
            account {
            cards {
              id
            }
            bankAccounts {
              id
            }
            }
            portfolio {
              amountPaid
            }
            status {
              name
            }
            loanCategory {
              id
              loanCategoryAttributes {
                attribute {
                  id
                  name
                }
                value
              }
            }
          }
        }
      }
    }
  }
`;

type PageProps = {
  children: ReactNode;
};

export default async function LoanDetailsLayout({ children }: PageProps) {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const clientInfo = await getCpV2ClientInfo({ basePath: null });
  const viewerData: Viewer = viewerQuery?.data?.viewer;

  const termsAndConditionsUrl = clientInfo?.termsAndConditionsUrl!;

  return (
    <Container
      maxW={{ base: "100vw", md: "8xl" }}
      px={{ base: 0, md: 8 }}
      mx={{ base: 0, md: "auto" }}
      w='full'
    >
      <Center>
        <SimpleGrid
          columns={1}
          spacing={4}
          w='full'
        >
          <LoanDetails
            viewer={viewerData}
            termsAndConditionsUrl={termsAndConditionsUrl}
          />
          <LoanTabs>{children}</LoanTabs>
        </SimpleGrid>
      </Center>
    </Container>
  );
}
