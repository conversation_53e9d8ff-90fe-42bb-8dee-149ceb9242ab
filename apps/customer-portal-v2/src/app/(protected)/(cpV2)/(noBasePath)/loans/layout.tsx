import { BreadCrumbs } from "@/components/BreadCrumbs";
import { Center, Container, Flex, SimpleGrid } from "@chakra-ui/react";
import { Metadata } from "next";
import React, { ReactNode } from "react";

export const metadata: Metadata = {
  title: "Loans",
};
const LoansLayout = ({ children }: { children: ReactNode }) => {
  return (
    <Container
      maxW={{ base: "100vw", md: "8xl" }}
      px={{ base: 0, md: 8 }}
      mx={{ base: 0, md: "auto" }}
      w='full'
    >
      <Center>
        <SimpleGrid
          columns={1}
          spacing={4}
          w='full'
          py={4}
        >
          <Flex
            justifyContent='space-between'
            alignContent='center'
          >
            <BreadCrumbs showApplyButton={true} />
          </Flex>
          <Center maxW='8xl'>{children}</Center>
        </SimpleGrid>
      </Center>
    </Container>
  );
};

export default LoansLayout;
