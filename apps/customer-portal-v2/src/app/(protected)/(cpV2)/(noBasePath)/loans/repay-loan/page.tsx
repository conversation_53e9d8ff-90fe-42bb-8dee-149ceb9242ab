import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { GENERATE_PAYSTACK_CARD_REFERENCE } from "@/src/graphql/query";
import {
  Application,
  Card,
  CreatePaystackCardRepaymentInput,
  CreatePaystackReferenceRepaymentInput,
  GenerateAddCardReferenceInput,
  Portfolio,
  Viewer,
} from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";
import { RepayLoan } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Repay Loan",
};

const VIEWER_QUERY = `
  query Account {
    viewer {
      id
      me {
        email
      }
      account {
        id  
        cards {
          email
          bankName
          expiryDate
          isDefault
          isDefault
          id
          maskedPan
          status
          type
        }
        portfolios {
          nodes {
            amountPaid
            id
            fullAmount
            portfolioNumber
            status {
              name
            }
            repayments {
              status {
                name
              }
              id
              totalPayment
              outstandingPayment
            }
          }
        }
        applications {
          nodes {
            id       
            account {
              cards {
                id
                isDefault
                expiryDate
                maskedPan
                status
                type
              }
            }
            status {
              name
            }
            loanCategory {
              id
              loanCategoryAttributes {
                attribute {
                  id
                  name
                }
                value
              }
            }
          }
        }
      }
    }
  }
`;

const MAKE_PAYSTACK_CARD_REPAYMENT = `
 mutation MakePaystackCardRepayment($input: CreatePaystackCardRepaymentInput!) {
  makePaystackCardRepayment(input: $input) {
    message
    success
    transaction {
      amount
      id
      status
    }
  }
}
`;

const MAKE_PAYSTACK_REFERENCE_REPAYMENT = `
 mutation MakePaystackReferenceRepayment($input: CreatePaystackReferenceRepaymentInput!) {
  makePaystackReferenceRepayment(input: $input) {
    success
    transaction {
      id
      amount
      status
    }
    message
  }
}
`;

const GET_PAYSTACK_CARD_REFERENCE = `
query GetAddCardReference($input: GenerateAddCardReferenceInput) {
  getAddCardReference(input: $input) {
    status
    reference
    id
  }
}`;

const generateByPayStackRefAction = async ({
  accessToken,
  input,
}: {
  input: GenerateAddCardReferenceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.clientId) {
      return;
    }

    const response = await graphqlRequest(
      GENERATE_PAYSTACK_CARD_REFERENCE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return { data: response?.data?.getAddCardReference, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const getByPayStackRefAction = async ({
  accessToken,
  input,
}: {
  input: GenerateAddCardReferenceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.clientId) {
      return;
    }

    const response = await graphqlRequest(
      GET_PAYSTACK_CARD_REFERENCE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return { data: response?.data?.getAddCardReference, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const makePaystackCardRepaymentAction = async ({
  accessToken,
  input,
}: {
  input: CreatePaystackCardRepaymentInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.portfolioId || !input?.accountCardId) {
      return;
    }

    const response = await graphqlRequest(
      MAKE_PAYSTACK_CARD_REPAYMENT,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return { data: response?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const makePaystackRefRepaymentAction = async ({
  accessToken,
  input,
}: {
  input: CreatePaystackReferenceRepaymentInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.paystackReference) {
      return;
    }

    const response = await graphqlRequest(
      MAKE_PAYSTACK_REFERENCE_REPAYMENT,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return { data: response?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const RepayLoanPage = async () => {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken!;

  if (!accessToken) {
    redirect(`/`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const clientInfo = await getCpV2ClientInfo({ basePath: null });
  const clientId = clientInfo?.clientId!;

  const latestApplication = viewerData?.account?.applications
    ?.nodes?.[0] as Application;

  const currentLoan = viewerData?.account?.portfolios?.nodes?.find(
    (app: any) => app?.status?.name !== "CLOSED"
  ) as Portfolio;

  const cards = latestApplication?.account?.cards as Card[];
  const defaultCard = cards?.find((card) => card?.isDefault === true);

  const generateByPayStackRef = async ({
    metadata,
  }: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => {
    "use server";
    return generateByPayStackRefAction({
      input: {
        clientId,
        metadata,
      },
      accessToken,
    });
  };

  const makePaystackCardRepayment = async ({
    amount,
  }: {
    amount: CreatePaystackCardRepaymentInput["amount"];
  }) => {
    "use server";
    return makePaystackCardRepaymentAction({
      input: {
        amount,
        accountCardId: defaultCard?.id!,
        portfolioId: currentLoan?.id,
      },
      accessToken,
    });
  };

  const makePaystackRefRepayment = async ({
    paystackReference,
  }: {
    paystackReference: CreatePaystackReferenceRepaymentInput["paystackReference"];
  }) => {
    "use server";

    return makePaystackRefRepaymentAction({
      input: {
        paystackReference,
      },
      accessToken,
    });
  };

  const getByPayStackRef = async ({
    metadata,
  }: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => {
    "use server";
    return getByPayStackRefAction({
      input: {
        clientId,
        metadata,
      },
      accessToken,
    });
  };

  return (
    <RepayLoan
      viewer={viewerData}
      generateByPayStackRef={generateByPayStackRef}
      getByPayStackRef={getByPayStackRef}
      makePaystackCardRepayment={makePaystackCardRepayment}
      makePaystackRefRepayment={makePaystackRefRepayment}
    />
  );
};

export default RepayLoanPage;
