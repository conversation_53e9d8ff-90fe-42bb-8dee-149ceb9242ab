import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import logger from "@/lib/logger";
import { BankInfo } from "@/src/app/(protected)/(cpV2)/_components";
import { appRedirection } from "@/src/app/actions";
import {
  ADD_BANK_ACCOUNT,
  CREATE_APPLICATION_TRACE,
  SET_BANK_ACCOUNT,
} from "@/src/graphql/mutation";
import { GET_BANKS, RESOLVE_ACCOUNT_NUMBER } from "@/src/graphql/query";
import {
  Application,
  ApplicationBankStageInput,
  Bank,
  BankAccountType,
  CreateAccountBankInput,
  CreateApplicationTraceInput,
  Viewer,
} from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Bank Information",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      account {
        bankAccounts {
          id
          bank {
            id
            name
            code
          }
          accountName
          accountNumber
          status
          isDefault
        }
      }
    }
  }
`;

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      loanCategory {
        id
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
      }
    }
  }
`;

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId && !input?.comment) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

const addBankAction = async ({
  input,
  accessToken,
}: {
  input: CreateAccountBankInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.bankId) {
      return;
    }

    const res = await graphqlRequest(
      ADD_BANK_ACCOUNT,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return { error: res?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: res?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

const setBankAction = async ({
  input,
  accessToken,
}: {
  input: ApplicationBankStageInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.accountBankId) {
      return;
    }

    const res = await graphqlRequest(
      SET_BANK_ACCOUNT,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return { error: res?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: res?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

const resloveAccountNumber = async ({
  bankId,
  accountNumber,
  accountType,
  accessToken,
}: {
  bankId: string;
  accountNumber: string;
  accountType: BankAccountType;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!bankId) {
      return;
    }

    const res = await graphqlRequest(
      RESOLVE_ACCOUNT_NUMBER,
      "POST",
      {
        bankId,
        accountNumber,
        accountType,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return { error: res?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: res?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

interface PageProps {
  params: { id: string; basePath: string };
}

const BankInfoPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const basePath = params?.basePath;
  const applicationNumber = params?.id;

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );
  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const application: Application = applicationQuery?.data?.application;
  const applicationId = application?.id;
  const loanCategoryId = application?.loanCategory?.id!;

  const getBanks = await graphqlRequest(
    GET_BANKS,
    "POST",
    {
      loanCategoryId,
    },
    accessToken
  );

  const banks: Bank[] = getBanks?.data?.getBanks;

  const createAppTrace = async ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => {
    "use server";
    return createTrace({
      input: {
        page,
        comment,
        applicationId,
        isDebug,
        metadata,
      },
      accessToken,
    });
  };

  const addBank = async ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => {
    "use server";
    return addBankAction({
      input: {
        bankId,
        accountName,
        accountNumber,
      },
      accessToken,
    });
  };

  const setBank = async ({
    applicationId,
    accountBankId,
  }: {
    applicationId: ApplicationBankStageInput["applicationId"];
    accountBankId: ApplicationBankStageInput["accountBankId"];
  }) => {
    "use server";
    return setBankAction({
      input: {
        applicationId,
        accountBankId,
      },
      accessToken,
    });
  };

  const getResolvedAccountNumber = async ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => {
    "use server";
    return resloveAccountNumber({
      bankId,
      accountNumber,
      accountType,
      accessToken,
    });
  };

  const result = await appRedirection({ basePath });

  if (result !== `/${basePath}/application/${applicationNumber}/bank-info`) {
    redirect(result!);
  }

  return (
    <BankInfo
      viewer={viewerData}
      application={application}
      applicationNumber={applicationNumber}
      banks={banks}
      createAppTrace={createAppTrace}
      addBank={addBank}
      setBank={setBank}
      getResolvedAccountNumber={getResolvedAccountNumber}
    />
  );
};

export default BankInfoPage;
