import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import logger from "@/lib/logger";
import { appRedirection } from "@/src/app/actions";
import { CREATE_APPLICATION_TRACE } from "@/src/graphql/mutation";
import {
  Application,
  CreateApplicationTraceInput,
  Viewer,
} from "@/__generated/graphql";
import { Metadata, ResolvingMetadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";
import { FormBuilder } from "../../../../_components";
import { FormBuilderProvider } from "../../../../_services";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      loanCategory {
        id
        products {
          applicationForm
          id
        }

      }
      customApplicationForm
    }
  }
`;

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        id
        firstName
        lastName
      }
      account {
        name
        applications {
          nodes {
            status {
              name
            }
            applicationNumber
            loanCategory {
              id
              products {
                applicationForm
                id
              }
            }
            customApplicationForm
          }
        }
      }
    }
  }
`;

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

interface PageProps {
  params: { id: string; basePath: string };
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const session = await getServerSession(authOptions);
  const { id: applicationNumber } = params || {};
  const accessToken = session?.accessToken;

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const application: Application = applicationQuery?.data?.application;

  const { customApplicationForm, loanCategory } = application || {};
  const { step } = customApplicationForm || {};

  const forms = loanCategory?.products?.[0]?.applicationForm ?? [];
  const activeFormTabs =
    forms?.filter(
      (tab: any) =>
        !tab?.linkedToOption &&
        !tab?.builders.some(
          (builder: { title: string }) =>
            builder?.title === "Card" || builder?.title === "Bank Info"
        )
    ) ?? [];

  const currentStepName = activeFormTabs?.[step || 0]?.name ?? "";

  return {
    title: currentStepName,
  };
}
const CustomApplicationPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const basePath = params?.basePath;
  const applicationNumber = params?.id;
  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const application: Application = applicationQuery?.data?.application;
  const applicationFormData: Application["loanCategory"] =
    application?.loanCategory;
  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const applicationId = application?.id;

  const createAppTrace = async ({
    page,
    comment,
  }: {
    page: string;
    comment: string;
  }) => {
    "use server";
    return createTrace({
      input: {
        page,
        comment,
        applicationId,
      },
      accessToken,
    });
  };

  const result = await appRedirection({ basePath });

  if (result !== `/${basePath}/application/${applicationNumber}`) {
    redirect(result!);
  }

  return (
    <FormBuilderProvider
      viewer={viewerData}
      applicationNumber={applicationNumber}
      application={application}
      applicationFormData={applicationFormData}
      token={accessToken}
      createAppTrace={createAppTrace}
    >
      <FormBuilder />
    </FormBuilderProvider>
  );
};
export default CustomApplicationPage;
