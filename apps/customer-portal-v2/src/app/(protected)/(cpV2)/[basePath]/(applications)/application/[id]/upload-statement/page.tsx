import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import logger from "@/lib/logger";
import { UploadBankStatement } from "@/src/app/(protected)/(cpV2)/_components";
import { appRedirection, upload } from "@/src/app/actions";
import { CREATE_APPLICATION_TRACE } from "@/src/graphql/mutation";
import { GET_BANKS } from "@/src/graphql/query";
import { getAttributrFromProduct } from "@/utils/index";
import {
  Application,
  Bank,
  BankStatementUploadInput,
  CreateApplicationTraceInput,
  GetDecidePdfStatusInput,
  Viewer,
} from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Upload Bank Statement",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
     account {
        id
        bankAccounts {
          id
          bank {
            id
            name
            code
          }
          accountName
          accountNumber
          status
          isDefault
        }
      }
    }
  }
`;

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      loanCategory {
        id
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
      }
      hasPdfBankStatement
      bankAccount {
        id
        bank {
          id
          name
          code
          okraSlug
        }
        accountName
        accountNumber
        status
        isDefault
        
      }
      requiredSteps
    }
  }
`;

const GET_DECIDE_JOB_STATUS = `
  query GetDecidePdfStatus($input: GetDecidePdfStatusInput) {
  getDecidePdfStatus(input: $input) {
    bankStatementId
    message
    status
  }
}
`;

const UPLOAD_BANK_STATEMENT = `
  mutation UploadBankStatement($input: BankStatementUploadInput!) {
    uploadBankStatement(input: $input) {
      filename
      mimetype
      encoding
    }
  }
`;

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId && !input?.comment) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

const getDecideJobStatusAction = async ({
  input,
  accessToken,
}: {
  input: GetDecidePdfStatusInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const status = await graphqlRequest(
      GET_DECIDE_JOB_STATUS,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (status?.errors?.length) {
      return { error: status?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: status?.data?.getDecidePdfStatus };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

const uploadBankStatementAction = async ({
  accessToken,
  input,
}: {
  accessToken: string;
  input: BankStatementUploadInput;
}) => {
  "use server";
  try {
    const { applicationId, bankId, file, password } = input || {};

    if (!bankId) {
      return { error: "Bank ID is required", data: null };
    }

    const formData = new FormData();
    formData.append(
      "operations",
      JSON.stringify({
        query: UPLOAD_BANK_STATEMENT,
        variables: { input: { applicationId, bankId, file, password } },
      })
    );
    formData.append("map", JSON.stringify({ "1": ["variables.input.file"] }));
    formData.append("1", file, file?.name);
    const result = await upload({ data: formData, token: accessToken! });

    if (result?.errors?.length) {
      return { error: result?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: result?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

interface PageProps {
  params: { id: string; basePath: string };
}

const UploadBankStatementPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const basePath = params?.basePath;
  const applicationNumber = params?.id;

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );
  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const application: Application = applicationQuery?.data?.application;
  const applicationId = application?.id;
  const loanCategory = application?.loanCategory;

  const getBanks = await graphqlRequest(GET_BANKS, "POST", {}, accessToken);

  const banks: Bank[] = getBanks?.data?.getBanks;

  const namesSet = new Set(["allowBankSelectionOnPdfUpload"]);
  const loanCategoryAttributes = loanCategory?.loanCategoryAttributes!;

  const applicationAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const allowBankSelectionOnPdfUpload: boolean =
    applicationAttributes?.allowBankSelectionOnPdfUpload;

  const createAppTrace = async ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => {
    "use server";
    return createTrace({
      input: {
        page,
        comment,
        applicationId,
        isDebug,
        metadata,
      },
      accessToken,
    });
  };

  const getDecideJobStatus = async () => {
    "use server";
    return getDecideJobStatusAction({
      input: {
        applicationId,
      },
      accessToken,
    });
  };

  const uploadBankStatement = async ({
    file,
    bankId,
    password,
  }: {
    bankId: BankStatementUploadInput["bankId"];
    file: BankStatementUploadInput["file"];
    password: BankStatementUploadInput["password"];
  }) => {
    "use server";

    return uploadBankStatementAction({
      accessToken,
      input: {
        applicationId,
        bankId,
        file,
        password,
      },
    });
  };

  const uploadAction = async (formData: FormData) => {
    "use server";

    const file = formData.get("file") as File;
    const bankId = formData.get("bankId") as string;
    const password = formData.get("password") as string | undefined;

    return uploadBankStatement({
      file,
      bankId,
      password,
    });
  };

  const result = await appRedirection({ basePath });

  if (
    result !== `/${basePath}/application/${applicationNumber}/upload-statement`
  ) {
    redirect(result!);
  }
  return (
    <UploadBankStatement
      application={application}
      applicationNumber={applicationNumber}
      viewer={viewerData}
      banks={banks}
      createAppTrace={createAppTrace}
      getDecideJobStatus={getDecideJobStatus}
      uploadAction={uploadAction}
      allowBankSelectionOnPdfUpload={allowBankSelectionOnPdfUpload}
    />
  );
};
export default UploadBankStatementPage;
