import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { VerifyEmail } from "@/src/app/(protected)/(cpV2)/_components";
import { appRedirection } from "@/src/app/actions";
import { UpdateUserEmailInput, Viewer } from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Verify Email",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        id
        firstName
        lastName
        email
        isEmailConfirmed
      }
    }
  }
`;

const TRIGGER_PERSONAL_EMAIL_CONFIRMATION = `
  mutation TriggerPersonalEmailConfirmation {
    triggerPersonalEmailConfirmation {
      ok
    }
  }
`;

const UPDATE_USER_EMAIL = `
 mutation UpdateUserEmail($input: UpdateUserEmailInput!) {
  updateUserEmail(input: $input) {
    ok
  }
}
`;

const CONFIRM_PERSONAL_EMAIL = `
  mutation ConfirmPersonalEmail($code: String!) {
    confirmPersonalEmail(code: $code) {
      ok
    }
  }
`;

const triggerOtpAction = async (accessToken: string) => {
  "use server";
  try {
    const otpResult = await graphqlRequest(
      TRIGGER_PERSONAL_EMAIL_CONFIRMATION,
      "POST",
      {},
      accessToken
    );

    if (otpResult?.errors?.length) {
      throw new Error(otpResult?.errors[0]?.message);
    }

    if (otpResult?.data?.triggerPersonalEmailConfirmation?.ok) {
      return { success: true };
    }
    return { success: false };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

const confirmEmailAction = async (code: string, accessToken: string) => {
  "use server";
  try {
    if (!code) {
      return { success: false };
    }

    const verifyEmail = await graphqlRequest(
      CONFIRM_PERSONAL_EMAIL,
      "POST",
      { code },
      accessToken
    );

    if (verifyEmail?.errors?.length) {
      throw new Error(verifyEmail?.errors[0]?.message);
    }

    if (verifyEmail?.data?.confirmPersonalEmail?.ok) {
      return { success: true, redirect: true };
    }
    return { success: false };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

const editEmailAction = async ({
  input,
  accessToken,
}: {
  input: UpdateUserEmailInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.email || !input?.userId) {
      return;
    }

    const updateEmail = await graphqlRequest(
      UPDATE_USER_EMAIL,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (updateEmail?.errors?.length) {
      throw new Error(updateEmail?.errors[0]?.message);
    }

    if (updateEmail?.data?.updateUserEmail?.ok) {
      await triggerOtpAction(accessToken);
      return { success: true, redirect: true };
    }
    return { success: false };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

interface PageProps {
  params: { id: string; basePath: string };
}
const VerifyEmailPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const basePath = params?.basePath;
  const applicationNumber = params?.id;
  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const userEmail = viewerData?.me?.email!;
  const userId = viewerData?.me?.id!;

  const triggerOtp = async () => {
    "use server";
    return triggerOtpAction(accessToken!);
  };

  const confirmEmail = async ({ code }: { code: string }) => {
    "use server";
    return confirmEmailAction(code, accessToken!);
  };

  const editEmail = async ({ email }: { email: string }) => {
    "use server";
    return editEmailAction({
      input: {
        userId,
        email,
      },
      accessToken,
    });
  };

  const nextPath = `/${basePath}/application/${applicationNumber}/verify-phone`;

  const result = await appRedirection({ basePath });

  if (result !== `/${basePath}/application/${applicationNumber}/verify-email`) {
    redirect(result!);
  }

  return (
    <VerifyEmail
      email={userEmail}
      confirmEmail={confirmEmail}
      editEmail={editEmail}
      triggerOtp={triggerOtp}
      path={nextPath}
    />
  );
};

export default VerifyEmailPage;
