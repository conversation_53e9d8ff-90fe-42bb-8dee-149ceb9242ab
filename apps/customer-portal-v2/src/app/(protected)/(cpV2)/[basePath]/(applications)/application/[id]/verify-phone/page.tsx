import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import logger from "@/lib/logger";
import { PhoneVerification } from "@/src/app/(protected)/(cpV2)/_components";
import { appRedirection } from "@/src/app/actions";
import { CREATE_APPLICATION_TRACE } from "@/src/graphql/mutation";
import { maskPhoneNumber } from "@/utils/index";
import {
  Application,
  ConfirmPhoneInput,
  CreateApplicationTraceInput,
  TriggerPhoneConfirmationInput,
  Viewer,
} from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Verify Phone",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        id
        bvnStatus {
          phone
        }
        isPhoneConfirmed
      }
    }
  }
`;
const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      loanCategory {
        id
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
      }
    }
  }
`;
const CONFIRM_PHONE = `
  mutation ConfirmPhone($input: ConfirmPhoneInput!) {
  confirmPhone(input: $input) {
    ok
  }
}
`;

const TRIGGER_PHONE_CONFIRMATION = `
  mutation TriggerPhoneConfirmation($input: TriggerPhoneConfirmationInput!) {
  triggerPhoneConfirmation(input: $input) {
    ok
  }
}
`;

const triggerOtpAction = async ({
  accessToken,
  input,
}: {
  accessToken: string;
  input: TriggerPhoneConfirmationInput;
}) => {
  "use server";
  try {
    if (!input?.userId) {
      return;
    }

    const otpResult = await graphqlRequest(
      TRIGGER_PHONE_CONFIRMATION,
      "POST",
      {
        input: {
          userId: input?.userId,
          useBvnPhone: true,
        },
      },
      accessToken
    );

    if (otpResult?.errors?.length) {
      throw new Error(otpResult?.errors[0]?.message);
    }

    if (otpResult?.data?.triggerPhoneConfirmation?.ok) {
      return { success: true };
    }
    return { success: false };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

const confirmPhoneAction = async ({
  accessToken,
  input,
}: {
  accessToken: string;
  input: ConfirmPhoneInput;
}) => {
  "use server";
  try {
    if (!input?.code) {
      return;
    }

    const verifyPhone = await graphqlRequest(
      CONFIRM_PHONE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (verifyPhone?.errors?.length) {
      throw new Error(verifyPhone?.errors[0]?.message);
    }

    if (verifyPhone?.data?.confirmPhone?.ok) {
      return { success: true, redirect: true };
    }
    return { success: false };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

interface PageProps {
  params: { id: string; basePath: string };
}

const PhoneVerificationPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);

  const basePath = params?.basePath;
  const applicationNumber = params?.id;

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const userId = viewerData?.me?.id!;
  const application: Application = applicationQuery?.data?.application;
  const applicationId = application?.id;
  const bvnPhone = viewerData?.me?.bvnStatus?.phone!;

  const maskedPhone = maskPhoneNumber(`0${bvnPhone}`);

  const createAppTrace = async ({
    page,
    comment,
  }: {
    page: string;
    comment: string;
  }) => {
    "use server";
    return createTrace({
      input: {
        applicationId,
        page,
        comment,
      },
      accessToken,
    });
  };

  const confirmPhone = async ({ code }: { code: string }) => {
    "use server";
    return confirmPhoneAction({
      input: {
        code,
        userId,
      },
      accessToken,
    });
  };

  const triggerOtp = async () => {
    "use server";
    return triggerOtpAction({
      accessToken,
      input: {
        userId,
      },
    });
  };

  const result = await appRedirection({ basePath });

  if (result !== `/${basePath}/application/${applicationNumber}/verify-phone`) {
    redirect(result!);
  }

  return (
    <PhoneVerification
      applicationNumber={applicationNumber}
      triggerOtp={triggerOtp}
      confirmPhone={confirmPhone}
      createAppTrace={createAppTrace}
      maskedPhone={maskedPhone}
      bvnPhone={bvnPhone}
    />
  );
};
export default PhoneVerificationPage;
