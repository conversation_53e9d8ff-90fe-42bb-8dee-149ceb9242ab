import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";
import { loginRedirection } from "@/src/app/actions";
import { LoginPage } from "../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Login",
};

interface PageProps {
  params: { basePath: string };
}

const Login = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);
  const basePath = params?.basePath;
  const accessToken = session?.accessToken!;

  if (accessToken) {
    return await loginRedirection({ basePath });
  }

  return <LoginPage />;
};

export default Login;
