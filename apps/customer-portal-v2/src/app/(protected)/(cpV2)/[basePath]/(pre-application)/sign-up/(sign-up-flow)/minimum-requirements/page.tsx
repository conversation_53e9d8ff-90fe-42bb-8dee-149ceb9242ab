import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { MinimumRequirements } from "@/src/app/(protected)/(cpV2)/_components";
import { Viewer } from "@/__generated/graphql";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import React from "react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Minimum Requirements",
};

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      
      me {
        bvnStatus {
          bvn
        }
      }
    }
  }
`;

type PageProps = {
  params: {
    basePath: string;
  };
};
const MinimumRequirementsPage = async ({ params }: PageProps) => {
  const session = await getServerSession(authOptions);

  const basePath = params?.basePath;

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;

  if (viewerData?.me?.bvnStatus?.bvn) {
    redirect(`/${basePath}/sign-up/verify-email`);
  }
  return <MinimumRequirements />;
};
export default MinimumRequirementsPage;
