import { notFound, redirect } from "next/navigation";
import { graphqlRequest } from "@/lib/graphql-client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";
import {
  Application,
  ApplicationBankStageInput,
  Bank,
  BankAccountType,
  BankStatementUploadInput,
  CreateAccountBankInput,
  CreateApplicationTraceInput,
  LoanCategory,
  Policy,
  Viewer,
} from "@/__generated/graphql";
import { getCpV2ClientInfo, upload } from "@/src/app/actions";
import {
  ADD_BANK_ACCOUNT,
  CREATE_APPLICATION_TRACE,
  SET_BANK_ACCOUNT,
} from "@/src/graphql/mutation";
import logger from "@/lib/logger";
import { GET_BANKS, RESOLVE_ACCOUNT_NUMBER } from "@/src/graphql/query";
import { revalidatePath } from "next/cache";
import { TabContent } from "../../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      id
      applicationNumber
      hasPdfBankStatement
      loanCategory {
        id
        products {
          applicationForm
          id
        }
      }
      status {
        name
      }
      customApplicationForm
      user {
        id
        firstName
        lastName
        email
      }
      account {
        cards {
          id
          maskedPan
          expiryDate
          type
          bankName
          email
          status
          isDefault
        }
        bankAccounts {
          accountName
          accountNumber
          id
          bank {
            name
            id
            code
          }
          status
          isDefault
        }
      }
      loanDuration
      bankAccount {
        id
        bank {
          id
          name
          code
        }
        accountName
        accountNumber
        status
        isDefault
      }
      loanCategory {
        id
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
        products {
          id
          applicationForm
        }
      }
    }
  }
`;

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        email
      }
      account {
        id
        channel {
          type
        }
        cards {
          id
        }
      }
    }
  }
`;

const UPLOAD_BANK_STATEMENT = `
  mutation UploadBankStatement($input: BankStatementUploadInput!) {
    uploadBankStatement(input: $input) {
      filename
      mimetype
      encoding
    }
  }
`;

const createTrace = async ({
  input,
  accessToken,
}: {
  input: CreateApplicationTraceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const trace = await graphqlRequest(
      CREATE_APPLICATION_TRACE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (trace?.data?.createApplicationTrace?.success) {
      return trace;
    }
  } catch (error: any) {
    logger({ error });
  }
};

const uploadBankStatementAction = async ({
  accessToken,
  input,
  path,
}: {
  accessToken: string;
  input: BankStatementUploadInput;
  path: string;
}) => {
  "use server";
  try {
    const { applicationId, bankId, file, password } = input || {};

    if (!bankId) {
      return { error: "Bank ID is required", data: null };
    }

    const formData = new FormData();
    formData.append(
      "operations",
      JSON.stringify({
        query: UPLOAD_BANK_STATEMENT,
        variables: { input: { applicationId, bankId, file, password } },
      })
    );
    formData.append("map", JSON.stringify({ "1": ["variables.input.file"] }));
    formData.append("1", file, file?.name);
    const result = await upload({ data: formData, token: accessToken! });

    if (result?.errors?.length) {
      return { error: result?.errors?.[0]?.message, data: null };
    }
    revalidatePath(path);
    return { error: null, data: result?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

const addBankAction = async ({
  input,
  accessToken,
}: {
  input: CreateAccountBankInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.bankId) {
      return;
    }

    const res = await graphqlRequest(
      ADD_BANK_ACCOUNT,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return { error: res?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: res?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

const setBankAction = async ({
  input,
  accessToken,
  path,
}: {
  input: ApplicationBankStageInput;
  accessToken: string;
  path: string;
}) => {
  "use server";
  try {
    if (!input?.accountBankId) {
      return;
    }

    const res = await graphqlRequest(
      SET_BANK_ACCOUNT,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return { error: res?.errors?.[0]?.message, data: null };
    }
    // revalidatePath(path);
    return { error: null, data: res?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

const resloveAccountNumber = async ({
  bankId,
  accountNumber,
  accountType,
  accessToken,
}: {
  bankId: string;
  accountNumber: string;
  accountType: BankAccountType;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!bankId) {
      return;
    }

    const res = await graphqlRequest(
      RESOLVE_ACCOUNT_NUMBER,
      "POST",
      {
        bankId,
        accountNumber,
        accountType,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return { error: res?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: res?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

export default async function TabPage({
  params,
}: {
  params: { id: string; basePath: string; tabSlug: string };
}) {
  const session = await getServerSession(authOptions);
  const applicationNumber = params?.id;
  const tabSlug = params?.tabSlug;
  const basePath = params?.basePath;
  const id = params?.id;
  const accessToken = session?.accessToken!;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const application: Application = applicationQuery?.data?.application!;
  const applicationId = application?.id!;
  const clientInfo = await getCpV2ClientInfo({ basePath });
  const secondaryColor = clientInfo?.clientTheme?.secondaryColor!;
  const loanCategory: LoanCategory = application?.loanCategory!;

  const activeFormTabs: Policy["applicationForm"] =
    loanCategory?.products?.[0].applicationForm.filter(
      (tab: any) =>
        !tab?.linkedToOption &&
        !tab?.builders.some(
          (builder: { title: string }) =>
            builder?.title === "Card" || builder?.title === "Bank Info"
        )
    );

  // Find current tab
  const currentTabIndex = activeFormTabs.findIndex(
    (tab: any) => tab.name.trim().toLowerCase().replace(/\s+/g, "-") === tabSlug
  );
  const currentTab = activeFormTabs[currentTabIndex];

  // Handle static tabs
  const staticTabs = ["payment-options", "documents-upload"];
  const isStaticTab = staticTabs.includes(tabSlug);

  if (currentTabIndex === -1 && !isStaticTab) notFound();
  if (!currentTab && !isStaticTab) notFound();

  const defaultFormValues = application?.customApplicationForm!;

  const path = `/${basePath}/application/${id}/${tabSlug}`;

  const getBanks = await graphqlRequest(
    GET_BANKS,
    "POST",
    {
      loanCategoryId: loanCategory?.id,
    },
    accessToken
  );

  const banks: Bank[] = getBanks?.data?.getBanks;

  const createAppTrace = async ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => {
    "use server";
    return createTrace({
      input: {
        page,
        comment,
        applicationId,
        isDebug,
        metadata,
      },
      accessToken: accessToken!,
    });
  };

  const addBank = async ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => {
    "use server";
    return addBankAction({
      input: {
        bankId,
        accountName,
        accountNumber,
      },
      accessToken,
    });
  };

  const setBank = async ({
    applicationId,
    accountBankId,
  }: {
    applicationId: ApplicationBankStageInput["applicationId"];
    accountBankId: ApplicationBankStageInput["accountBankId"];
  }) => {
    "use server";
    return setBankAction({
      input: {
        applicationId,
        accountBankId,
      },
      accessToken,
      path,
    });
  };

  const getResolvedAccountNumber = async ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => {
    "use server";
    return resloveAccountNumber({
      bankId,
      accountNumber,
      accountType,
      accessToken,
    });
  };

  const uploadBankStatement = async ({
    file,
    bankId,
    password,
  }: {
    bankId: BankStatementUploadInput["bankId"];
    file: BankStatementUploadInput["file"];
    password: BankStatementUploadInput["password"];
  }) => {
    "use server";

    return uploadBankStatementAction({
      accessToken,
      path,
      input: {
        applicationId,
        bankId,
        file,
        password,
      },
    });
  };

  const uploadAction = async (formData: FormData) => {
    "use server";

    const file = formData.get("file") as File;
    const bankId = formData.get("bankId") as string;
    const password = formData.get("password") as string | undefined;

    return uploadBankStatement({
      file,
      bankId,
      password,
    });
  };

  return (
    <TabContent
      tabSlug={tabSlug}
      activeFormTabs={activeFormTabs}
      currentTabIndex={currentTabIndex}
      defaultFormData={defaultFormValues}
      currentTab={currentTab}
      secondaryColor={secondaryColor!}
      application={application}
      createAppTrace={createAppTrace}
      viewer={viewerData}
      banks={banks}
      addBank={addBank}
      setBank={setBank}
      getResolvedAccountNumber={getResolvedAccountNumber}
      uploadAction={uploadAction}
    />
  );
}
