import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { Application, ApplicationStatusEnum } from "@/__generated/graphql";
import { SimpleGrid, Container, Center } from "@chakra-ui/react";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { ReactNode } from "react";
import { ApplicationDetails, ApplicationTabs } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Loan Details",
};

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      dateOfRepayment
      createdAt
      applicationNumber
      fullAmount
      amount
      baseAmount
      loanDuration
      completedSteps
      user {
      isEmailConfirmed
      isPhoneConfirmed
      }
      account {
      cards {
        id
      }
      bankAccounts {
        id
      }
      }
      portfolio {
        amountPaid
      }
      status {
        name
      }
      displayStatus {
        name
      }
      policy {
        interestRate {
          value
          type
          name
          calcBy
        }
        repaymentInterval
        graduatedLoanCycles {
          amount
          interestRate
        }
      }
      loanCategory {
        id
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
        products {
          applicationForm
          id
        }
      }
      customApplicationForm
      reviewDetails {
        rejectReason
      }
    }
  }
`;

type PageProps = {
  params: {
    basePath: string;
    id: string;
  };
  children: ReactNode;
};

export default async function LoanDetailsLayout({
  params,
  children,
}: PageProps) {
  const session = await getServerSession(authOptions);
  const basePath = params?.basePath;

  const applicationNumber = params?.id;

  const accessToken = session?.accessToken;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );

  const application: Application = applicationQuery?.data?.application;

  const clientInfo = await getCpV2ClientInfo({ basePath });
  const termsAndConditionsUrl = clientInfo?.termsAndConditionsUrl!;
  const status = application?.status?.name!;

  return (
    <Container
      maxW={{ base: "100vw", md: "8xl" }}
      px={{ base: 0, md: 8 }}
      mx={{ base: 0, md: "auto" }}
      w='full'
    >
      <Center>
        <SimpleGrid
          columns={1}
          spacing={4}
          w='full'
        >
          <ApplicationDetails
            application={application}
            termsAndConditionsUrl={termsAndConditionsUrl}
          />
          {status !== ApplicationStatusEnum.Denied ? (
            <ApplicationTabs application={application}>
              {children}
            </ApplicationTabs>
          ) : null}
        </SimpleGrid>
      </Center>
    </Container>
  );
}
