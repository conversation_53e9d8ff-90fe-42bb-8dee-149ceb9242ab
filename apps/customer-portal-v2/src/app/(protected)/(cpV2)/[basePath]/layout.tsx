import { getCpV2ClientInfo } from "@/src/app/actions";
import { dmSans } from "@/src/app/fonts";
import { type ReactNode } from "react";
import { Layout } from "../_components";
import type { Metadata } from "next";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

interface LayoutProps {
  children: ReactNode;
  params: {
    basePath: string;
    id: string;
  };
}

export async function generateMetadata({
  params,
}: LayoutProps): Promise<Metadata> {
  const basePath = params.basePath;
  const clientInfo = await getCpV2ClientInfo({ basePath });

  if (!clientInfo) {
    return {
      title: "Customer Portal",
    };
  }

  return {
    title: `${clientInfo.name} Customer Portal`,
    icons: clientInfo.faviconUrl!,
  };
}

export default async function AppLayout({ children }: LayoutProps) {
  return (
    <div
      className={dmSans.className}
      suppressHydrationWarning
    >
      <Layout>{children}</Layout>
    </div>
  );
}
