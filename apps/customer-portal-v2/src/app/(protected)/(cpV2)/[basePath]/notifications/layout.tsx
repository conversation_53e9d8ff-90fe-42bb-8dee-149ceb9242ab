import { Metadata } from "next";
import React from "react";
import { BreadCrumbs } from "@/components/BreadCrumbs";
import { Center, Container, Flex, SimpleGrid } from "@chakra-ui/react";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Notifications",
};

type Props = {
  children: React.ReactNode;
};
const NotificationLayout = async ({ children }: Props) => {
  return (
    <Container
      maxW={{ base: "100vw", md: "8xl" }}
      px={{ base: 0, md: 8 }}
      mx={{ base: 0, md: "auto" }}
      w='full'
    >
      <Center>
        <SimpleGrid
          columns={1}
          spacing={4}
          w='full'
          py={4}
        >
          <Flex
            justifyContent='space-between'
            alignContent='center'
            ml={{ base: 2, md: "inherit" }}
          >
            <BreadCrumbs showApplyButton={true} />
          </Flex>
          <Center maxW='8xl'>{children}</Center>
        </SimpleGrid>
      </Center>
    </Container>
  );
};

export default NotificationLayout;
