import { Box } from "@chakra-ui/react";
import { NotificationPanel } from "@/components/NotificationCenter/NotificationPanel";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { Application, ReviewFeedbackType, Viewer } from "@/__generated/graphql";
import { Notification } from "@/src/types/notifications";
import { redirect } from "next/navigation";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        firstName
        lastName
      }
      account {
        applications {
          nodes {
            applicationNumber
          }
        }
      }
    }
  }
`;

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      applicationNumber
      reviewFeedbacks {
        status
        reviewType
        oldApplicationStatus {
          name
        }
        id
        documentRequests {
          document {
            id
            user {
              id
            }
            documentName
            uploadType
            file {
              key
              bucket
              etag
              url
            }
            createdAt
          }
          id
          message
          status
          title
        }
        createdAt
        comment
      }
    }
  }
`;

const NotificationsPage = async ({
  params,
}: {
  params: { basePath: string };
}) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;
  const basePath = params?.basePath;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewer: Viewer = viewerQuery?.data?.viewer;
  const applicationNumber =
    viewer?.account?.applications?.nodes?.[0]?.applicationNumber;

  const generateTitleFromReviewType = (reviewType: ReviewFeedbackType) => {
    const titleMapping = {
      AMOUNT_MODIFICATION: "Application Details Update Required",
      DOCUMENT_REQUEST: "Additional Documents Required",
      DURATION_MODIFICATION: "Loan Duration Modification Required",
      DETAILS_MODIFICATION: "Loan Amount Modification Required",
      OFFER_LETTER_REQUEST: "Offer Letter Request",
    };

    return titleMapping[reviewType];
  };

  const transformReviewFeedbacksToNotifications = (
    application: Application
  ) => {
    if (!application?.reviewFeedbacks) {
      return [];
    }

    return application.reviewFeedbacks
      .filter(
        (feedback): feedback is NonNullable<typeof feedback> =>
          feedback !== null && feedback.id !== null && feedback.id !== undefined
      )
      .map((feedback) => ({
        id: feedback.id,
        title: generateTitleFromReviewType(feedback.reviewType!),
        type: feedback.reviewType,
        customerName: viewer?.me?.firstName!,
        date: feedback.createdAt,
        read: feedback?.status === "COMPLETED" ? true : false,
        status: feedback.status,
        comment: feedback.comment ?? "",
        documentRequests: feedback.documentRequests ?? [],
      }));
  };

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );
  const application: Application = applicationQuery?.data?.application;

  const reviewFeedbackNotifications: Notification[] =
    transformReviewFeedbacksToNotifications(application);

  const sortedNotifications = reviewFeedbackNotifications?.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return (
    <Box w='100%'>
      <NotificationPanel initialNotifications={sortedNotifications} />
    </Box>
  );
};
export default NotificationsPage;
