import { authOptions } from "@/lib/authOptions";
import { graphqlRequest } from "@/lib/graphql-client";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { ADD_BANK_ACCOUNT } from "@/src/graphql/mutation";
import { RESOLVE_ACCOUNT_NUMBER } from "@/src/graphql/query";
import {
  Bank,
  BankAccountType,
  CardReferenceStatusInput,
  ChangePasswordInput,
  CreateAccountBankInput,
  GenerateAddCardReferenceInput,
  SetDefaultAccountBankInput,
  SetDefaultCardInput,
  SupportingDocumentTypes,
  Viewer,
} from "@/__generated/graphql";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { TabContentz } from "../../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;
const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        id
        email
        firstName
        phone
        lastName
        userMetadata {
          name
          value
        }
        kycInformation
        isEmailConfirmed
      }
      account {
        id
        cards {
          bankName
          email
          expiryDate
          id
          isDefault
          maskedPan
          status
          type
        }
        bankAccounts {
          accountName
          accountNumber
          bank {
            code
            name
            id
          }
          id
          isDefault
          status
        }
      }
    }
  }
`;

const TRIGGER_PERSONAL_EMAIL_CONFIRMATION = `
  mutation TriggerPersonalEmailConfirmation {
    triggerPersonalEmailConfirmation {
      ok
    }
  }
`;

const CONFIRM_PERSONAL_EMAIL = `
  mutation ConfirmPersonalEmail($code: String!) {
    confirmPersonalEmail(code: $code) {
      ok
    }
  }
`;

const GET_ADD_CARD_REFERENCE = `
  query GetAddCardReference($input: GenerateAddCardReferenceInput) {
    getAddCardReference(input: $input) {
      id
      reference
      status
    }
  }
`;
const GET_CARD_REFERENCE_STATUS = `
  query GetCardReferenceStatus($input: CardReferenceStatusInput) {
    getCardReferenceStatus(input: $input) {
      bank
      card {
        bankName
        email
        expiryDate
        id
        isDefault
        maskedPan
        status
        type
      }
      reason
      status
    }
  }
`;

const SET_DEFAULT_ACCOUNT_CARD = `
  mutation SetDefaultAccountCard($input: SetDefaultCardInput!) {
    setDefaultAccountCard(input: $input) {
      bankName
      email
      expiryDate
      id
      isDefault
      maskedPan
      status
      type
    }
  }
`;

const SET_DEFAULT_ACCOUNT_BANK = `
  mutation SetDefaultAccountBank($input: SetDefaultAccountBankInput!) {
    setDefaultAccountBank(input: $input) {
      accountName
      accountNumber
      bank {
        code
        name
        id
      }
      id
      isDefault
      status
    }
  }
`;

const GET_SUPPORTING_DOCUMENTS = `
  query SupportingDocument($filterBy: SupportingDocumentTypes) {
    viewer {
      me {
        supportingDocument(filterBy: $filterBy) {
          createdAt
          documentName
          file {
            bucket
            etag
            key
            url
          }
          id
          uploadType
        
        }
      }
    }
  }
`;

const SUPPORTING_DOCUMENT_S3_URL = `
  query supportingDocumentS3Url($bucket: String!, $key: String!) {
    getPresignedUrlFromS3(bucket: $bucket, key: $key) {
      dataUrl
      status
    }
  }
`;

const UPDATE_PASSWORD = `
  mutation ChangePassword($input: ChangePasswordInput!) {
    changePassword(input: $input) {
      token
    }
  }
`;

const GET_BANKS = `
  query GetBanks {
    getBanks {
      code
      id
      name
      okraSlug
    }
  }
`;

const triggerOtpAction = async (accessToken: string) => {
  "use server";
  try {
    const otpResult = await graphqlRequest(
      TRIGGER_PERSONAL_EMAIL_CONFIRMATION,
      "POST",
      {},
      accessToken
    );

    if (otpResult?.errors?.length) {
      return { success: false, error: otpResult?.errors?.[0]?.message };
    }

    if (otpResult?.data?.triggerPersonalEmailConfirmation?.ok) {
      return { success: true, error: null };
    }
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

const confirmEmailAction = async (code: string, accessToken: string) => {
  "use server";
  try {
    if (!code) {
      return { success: false };
    }

    const verifyEmail = await graphqlRequest(
      CONFIRM_PERSONAL_EMAIL,
      "POST",
      { code },
      accessToken
    );

    if (verifyEmail?.errors?.length) {
      return { success: false, error: verifyEmail?.errors?.[0]?.message };
    }

    if (verifyEmail?.data?.confirmPersonalEmail?.ok) {
      return { success: true, redirect: true };
    }
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

const getCardRefStatusAction = async ({
  accessToken,
  input,
}: {
  input: CardReferenceStatusInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.reference) {
      return;
    }

    const response = await graphqlRequest(
      GET_CARD_REFERENCE_STATUS,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return { data: response?.data?.getCardReferenceStatus, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const getCdnFileAction = async ({
  accessToken,
  bucket,
  key,
}: {
  bucket: string;
  key: string;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!key || !bucket) {
      return;
    }

    const response = await graphqlRequest(
      SUPPORTING_DOCUMENT_S3_URL,
      "POST",
      {
        bucket,
        key,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }
    return { data: response?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const getAddCardRefAction = async ({
  accessToken,
  input,
}: {
  input: GenerateAddCardReferenceInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.clientId) {
      return;
    }

    const response = await graphqlRequest(
      GET_ADD_CARD_REFERENCE,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }
    return { data: response?.data?.getAddCardReference, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const resloveAccountNumber = async ({
  bankId,
  accountNumber,
  accountType,
  accessToken,
}: {
  bankId: string;
  accountNumber: string;
  accountType: BankAccountType;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!bankId) {
      return;
    }

    const res = await graphqlRequest(
      RESOLVE_ACCOUNT_NUMBER,
      "POST",
      {
        bankId,
        accountNumber,
        accountType,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return { error: res?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: res?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

const setDefaultCardAction = async ({
  accessToken,
  input,
}: {
  input: SetDefaultCardInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.accountId) {
      return;
    }

    const response = await graphqlRequest(
      SET_DEFAULT_ACCOUNT_CARD,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }
    return { data: response?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const setDefaultBankAction = async ({
  accessToken,
  input,
}: {
  input: SetDefaultAccountBankInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.accountId) {
      return;
    }

    const response = await graphqlRequest(
      SET_DEFAULT_ACCOUNT_BANK,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }
    return { data: response?.data, error: null };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const addBankAction = async ({
  input,
  accessToken,
}: {
  input: CreateAccountBankInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.bankId) {
      return;
    }

    const res = await graphqlRequest(
      ADD_BANK_ACCOUNT,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors?.length) {
      return { error: res?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: res?.data };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

const getSupportingDocumentsAction = async ({
  accessToken,
  filterBy,
}: {
  accessToken: string;
  filterBy: SupportingDocumentTypes;
}) => {
  "use server";
  try {
    const response = await graphqlRequest(
      GET_SUPPORTING_DOCUMENTS,
      "POST",
      {
        filterBy,
      },
      accessToken
    );
    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }
    return {
      data: response?.data?.viewer?.me?.supportingDocument,
      error: null,
    };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const changePasswordAction = async ({
  accessToken,
  input,
}: {
  accessToken: string;
  input: ChangePasswordInput;
}) => {
  "use server";
  try {
    if (!input?.oldPassword || !input?.newPassword) {
      return;
    }

    const response = await graphqlRequest(
      UPDATE_PASSWORD,
      "POST",
      { input },
      accessToken
    );

    if (response?.errors?.length) {
      throw new Error(response?.errors[0]?.message);
    }

    return {
      data: response?.data,
      error: null,
    };
  } catch (error: any) {
    return { data: null, error: error?.message };
  }
};

const SettingsTab = async ({
  params,
}: {
  params: { basePath: string; tabSlug: string };
}) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken!;
  const basePath = params?.basePath;
  const tabSlug = params?.tabSlug;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const getBanks = await graphqlRequest(GET_BANKS, "POST", {}, accessToken);

  const banks: Bank[] = getBanks?.data?.getBanks;

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const triggerOtp = async () => {
    "use server";
    return triggerOtpAction(accessToken!);
  };

  const confirmEmail = async ({ code }: { code: string }) => {
    "use server";
    return confirmEmailAction(code, accessToken!);
  };

  const clientInfo = await getCpV2ClientInfo({ basePath });
  const clientId = clientInfo?.clientId!;

  const getCardRefStatus = async ({
    reference,
  }: {
    reference: CardReferenceStatusInput["reference"];
  }) => {
    "use server";
    return getCardRefStatusAction({
      input: {
        reference,
      },
      accessToken,
    });
  };

  const getAddCardRef = async () => {
    "use server";
    return getAddCardRefAction({
      input: { clientId },
      accessToken,
    });
  };

  const setDefaultCard = async ({
    cardId,
  }: {
    cardId: SetDefaultCardInput["cardId"];
  }) => {
    "use server";
    return setDefaultCardAction({
      input: {
        cardId,
        accountId: viewerData?.account?.id,
      },
      accessToken,
    });
  };

  const setDefaultBank = async ({
    accountBankId,
  }: {
    accountBankId: SetDefaultAccountBankInput["accountBankId"];
  }) => {
    "use server";
    return setDefaultBankAction({
      input: {
        accountBankId,
        accountId: viewerData?.account?.id,
      },
      accessToken,
    });
  };

  const getSupportingDocuments = async ({
    filterBy,
  }: {
    filterBy: SupportingDocumentTypes;
  }) => {
    "use server";
    return getSupportingDocumentsAction({
      accessToken,
      filterBy,
    });
  };

  const getCdnFile = async ({
    key,
    bucket,
  }: {
    key: string;
    bucket: string;
  }) => {
    "use server";
    return getCdnFileAction({
      key,
      bucket,
      accessToken,
    });
  };

  const changePassword = async ({ input }: { input: ChangePasswordInput }) => {
    "use server";
    return changePasswordAction({
      input,
      accessToken,
    });
  };

  const addBank = async ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => {
    "use server";
    return addBankAction({
      input: {
        bankId,
        accountName,
        accountNumber,
      },
      accessToken,
    });
  };

  const getResolvedAccountNumber = async ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => {
    "use server";
    return resloveAccountNumber({
      bankId,
      accountNumber,
      accountType,
      accessToken,
    });
  };

  return (
    <TabContentz
      viewer={viewerData}
      triggerOtp={triggerOtp}
      confirmEmail={confirmEmail}
      getCardRefStatus={getCardRefStatus}
      getAddCardRef={getAddCardRef}
      setDefaultCard={setDefaultCard}
      setDefaultBank={setDefaultBank}
      getSupportingDocuments={getSupportingDocuments}
      getCdnFile={getCdnFile}
      changePassword={changePassword}
      banks={banks}
      addBank={addBank}
      getResolvedAccountNumber={getResolvedAccountNumber}
    />
  );
};

export default SettingsTab;
