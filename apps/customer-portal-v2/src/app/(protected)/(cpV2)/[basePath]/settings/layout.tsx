import { BreadCrumbs } from "@/components/BreadCrumbs";
import { authOptions } from "@/lib/authOptions";
import { SimpleGrid, Container, Center, Flex } from "@chakra-ui/react";
import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { ReactNode } from "react";
import { SettingsTabs } from "../../_components";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

export const metadata: Metadata = {
  title: "Settings",
};

export default async function DashboardLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: { basePath: string };
}) {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken!;
  const basePath = params?.basePath;

  if (!accessToken) {
    redirect(`/${basePath}`);
  }

  return (
    <Container
      maxW={{ base: "100vw", md: "8xl" }}
      px={{ base: 0, md: 8 }}
      mx={{ base: 0, md: "auto" }}
      w='full'
    >
      <Center>
        <SimpleGrid
          columns={1}
          spacing={4}
          w='full'
          py={4}
          px={{ base: 0, md: 12 }}
        >
          <Flex
            justifyContent='space-between'
            alignContent='center'
          >
            <BreadCrumbs showApplyButton={true} />
          </Flex>
          <SettingsTabs>{children}</SettingsTabs>
        </SimpleGrid>
      </Center>
    </Container>
  );
}
