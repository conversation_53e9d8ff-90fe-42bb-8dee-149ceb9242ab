"use client";
import { formatAmount, formatDate, isAbsolutePath } from "@/utils/index";
import { Application, ApplicationStatusEnum } from "@/__generated/graphql";
import {
  Box,
  Button,
  Flex,
  Heading,
  Stack,
  Text,
  VStack,
  Link as ChakraLink,
  <PERSON><PERSON><PERSON>ck,
  Badge,
  ThemeTypings,
} from "@chakra-ui/react";
import React from "react";
import { ViewReason } from "../ViewReason";

type ApplicationDetailsProps = {
  application: Application;
  termsAndConditionsUrl: string;
};

const ApplicationDetails = ({
  application,
  termsAndConditionsUrl,
}: ApplicationDetailsProps) => {
  const getDisplayInterestRate = () => {
    let interestRate: number;
    const { policy, amount } = application || {};

    if (policy?.interestRate) {
      interestRate = policy?.interestRate?.value;
    } else if (
      policy?.graduatedLoanCycles &&
      policy.graduatedLoanCycles.length > 0
    ) {
      const gCycle = policy.graduatedLoanCycles.find(
        (cycle) => cycle.amount === amount
      );
      if (gCycle && gCycle.interestRate) {
        interestRate = gCycle.interestRate;
      }
    }
    return interestRate! ? `${interestRate!}%` : "N/A";
  };

  const renderStatus = (
    status: string
  ): { label: string; color: ThemeTypings["colorSchemes"] } => {
    switch (status) {
      case ApplicationStatusEnum.Approved:
        return { label: "Approved", color: "green" };
      case ApplicationStatusEnum.Denied:
        return { label: "Denied", color: "red" };
      case ApplicationStatusEnum.Abandoned:
        return { label: "Abandoned", color: "purple" };
      case ApplicationStatusEnum.UnderReview:
        return { label: "Under Review", color: "orange" };
      case ApplicationStatusEnum.AwaitingFeedback:
        return { label: "Awaiting Feedback", color: "blue" };
      case ApplicationStatusEnum.Incomplete:
        return { label: "Incomplete", color: "yellow" };
      default:
        return { label: "Pending", color: "blue" };
    }
  };

  return (
    <Box
      py={6}
      px={{ base: 4, md: 6 }}
      borderRadius='md'
      boxShadow='sm'
      maxW='8xl'
      bg='white'
      position='relative'
    >
      <Heading
        size='xs'
        textAlign='left'
        mb={4}
      >
        {application?.applicationNumber}
      </Heading>

      <Stack
        width='full'
        direction={{ base: "column", lg: "row" }}
        px={{ base: 0, md: 4 }}
        pt={{ base: 0, md: 4 }}
        spacing={{ base: 12, lg: 20, xl: 28 }}
        alignItems='center'
      >
        <Stack
          width='full'
          justifyContent='flex-start'
          spacing={4}
          direction={{ base: "column" }}
        >
          <Stack
            width='full'
            direction={{ base: "column", sm: "row" }}
            gap={6}
          >
            <VStack
              spacing={4}
              width='full'
              align='flex-start'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Loan Amount
              </Text>
              <Text
                fontSize='xl'
                fontWeight='bold'
              >
                {formatAmount(application?.baseAmount!)}
              </Text>
            </VStack>
            <VStack
              spacing={4}
              width='full'
              align='flex-start'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Total Repayment Amount
              </Text>
              <Flex
                alignItems={{ base: "flex-start", md: "center" }}
                gap={4}
                w='full'
                flexDirection={{
                  base: "column",
                  md: "row",
                }}
              >
                <Text
                  fontSize='xl'
                  fontWeight='bold'
                >
                  {formatAmount(application?.fullAmount!)}
                </Text>
              </Flex>
            </VStack>
          </Stack>
          <VStack
            width='full'
            direction={{ base: "column", sm: "row" }}
            spacing={4}
            gap={6}
            alignItems='center'
          >
            <HStack
              spacing={4}
              width='full'
              align='center'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Status
              </Text>
              <Badge
                h={6}
                p={4}
                fontSize='xs'
                w='fit-content'
                display='flex'
                variant='solid'
                textAlign='center'
                alignItems='center'
                borderRadius={6}
                justifyContent='center'
                color={`${renderStatus(application?.displayStatus?.name)?.color}.500`}
                bg={`${renderStatus(application?.displayStatus?.name)?.color}.100`}
              >
                {renderStatus(application?.displayStatus?.name)?.label}
              </Badge>
            </HStack>

            {application?.status?.name === ApplicationStatusEnum.Denied ? (
              <VStack
                spacing={4}
                width='full'
                align='flex-start'
              >
                <ViewReason
                  rejectReason={application?.reviewDetails?.rejectReason!}
                />
              </VStack>
            ) : null}
          </VStack>
        </Stack>

        <VStack
          width='full'
          align='flex-start'
          spacing={2}
          bg='customBrand.50'
          borderRadius={6}
          p={4}
        >
          <ApplicationSummaryItem
            label='Application Date'
            value={formatDate({
              date: application?.createdAt,
              dateFormat: "dd-MMM-yyyy",
            })}
          />
          <ApplicationSummaryItem
            label='Interest Rate'
            value={getDisplayInterestRate()}
          />

          <ApplicationSummaryItem
            label='Repayment Amount'
            value={formatAmount(application?.fullAmount || 0)}
          />
          <ApplicationSummaryItem
            label='Repayment Date'
            value={formatDate({
              date: application?.dateOfRepayment,
              dateFormat: "dd-MMM-yyyy",
            })}
          />

          <Button
            as={ChakraLink}
            isExternal
            href={isAbsolutePath(termsAndConditionsUrl)}
            w={"full"}
            disabled={!termsAndConditionsUrl}
            bg='white'
            colorScheme='customPrimary'
            borderColor='customPrimary.500'
            color='customPrimary.500'
            variant='outline'
            _hover={{
              bg: "customPrimary.100",
              color: "customPrimary.600",
              textDecoration: "none",
            }}
            _visited={{
              color: "customPrimary.500",
            }}
          >
            View Contract and Terms
          </Button>
        </VStack>
      </Stack>
    </Box>
  );
};
export default ApplicationDetails;

const ApplicationSummaryItem = ({
  label,
  value,
}: {
  label: string;
  value: React.ReactNode;
}) => (
  <Flex
    w='full'
    justifyContent='space-between'
    alignItems='center'
  >
    <Text minW='80px'>{label}</Text>
    <Text>{value}</Text>
  </Flex>
);
