"use client";

import { Application } from "@/__generated/graphql";
import {
  Tabs,
  TabList,
  Tab,
  TabPanels,
  Box,
  TabPanel,
  Heading,
} from "@chakra-ui/react";
import { useParams, useRouter, usePathname } from "next/navigation";
import React, { useState, useEffect, useRef, useCallback } from "react";

const ApplicationTabs = ({
  children,
  application,
  tabContents,
}: {
  children: React.ReactNode;
  application: Application;
  tabContents?: React.ReactNode[];
}) => {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const { id, basePath } = params || {};
  const tabListRef = useRef<HTMLDivElement>(null);
  const activeTabRef = useRef<HTMLButtonElement>(null);
  const isNavigatingRef = useRef(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const applicationForm =
    application?.loanCategory?.products?.[0]?.applicationForm;

  const activeFormTabs =
    applicationForm?.filter(
      (tab: any) =>
        !tab?.linkedToOption &&
        !tab?.builders.some(
          (builder: { title: string }) =>
            builder?.title === "Card" || builder?.title === "Bank Info"
        )
    ) || [];
  const staticTabs = ["Payment Options"]; // Add "Documents Upload" here

  const [activeTabIndex, setActiveTabIndex] = useState(0);

  const scrollToActiveTab = useCallback(() => {
    if (activeTabRef.current && tabListRef.current) {
      const tabList = tabListRef.current;
      const activeTab = activeTabRef.current;

      const tabListRect = tabList.getBoundingClientRect();
      const activeTabRect = activeTab.getBoundingClientRect();

      const scrollLeft =
        activeTab.offsetLeft - tabListRect.width / 2 + activeTabRect.width / 2;

      tabList.scrollTo({
        left: scrollLeft,
        behavior: "smooth",
      });
    }
  }, []);

  // Update active tab index based on current pathname
  useEffect(() => {
    if (!pathname || isNavigatingRef.current) return;

    const segments = pathname.split("/");
    const currentSegment = segments[segments.length - 1];

    // Check static tabs first
    const staticIndex = staticTabs.findIndex(
      (tab) => currentSegment === tab.trim().toLowerCase().replace(/\s+/g, "-")
    );

    if (staticIndex !== -1) {
      setActiveTabIndex(activeFormTabs.length + staticIndex);
      return;
    }

    // Check dynamic tabs
    const dynamicIndex = activeFormTabs.findIndex(
      (tab: any) =>
        currentSegment === tab.name.trim().toLowerCase().replace(/\s+/g, "-")
    );

    if (dynamicIndex !== -1) {
      setActiveTabIndex(dynamicIndex);
    } else {
      // Default to first tab if no match found
      setActiveTabIndex(0);
    }

    // Clear transition state when pathname changes
    setIsTransitioning(false);
  }, [pathname, activeFormTabs, staticTabs]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      scrollToActiveTab();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [activeTabIndex, scrollToActiveTab]);

  const handleTabChange = (index: number) => {
    // Set flag to prevent pathname effect from overriding our immediate update
    isNavigatingRef.current = true;
    setIsTransitioning(true);

    // Immediately update the active tab index for instant visual feedback
    setActiveTabIndex(index);

    let path: string;

    if (index < activeFormTabs.length) {
      // Dynamic tab
      const tabName = activeFormTabs[index].name
        .trim()
        .toLowerCase()
        .replace(/\s+/g, "-");
      path = basePath
        ? `/${basePath}/applications/${id}/${tabName}`
        : `/applications/${id}/${tabName}`;
    } else {
      // Static tab
      const staticIndex = index - activeFormTabs.length;
      const tabName = staticTabs[staticIndex]
        .trim()
        .toLowerCase()
        .replace(/\s+/g, "-");
      path = basePath
        ? `/${basePath}/applications/${id}/${tabName}`
        : `/applications/${id}/${tabName}`;
    }

    // Use replace instead of push for faster navigation
    router.replace(path);

    // Reset the flag after navigation
    setTimeout(() => {
      isNavigatingRef.current = false;
    }, 50); // Reduced timeout
  };

  if (!activeFormTabs) {
    return <Box>Loading application form...</Box>;
  }

  return (
    <Box
      p={{ base: 4, md: 6 }}
      borderRadius={6}
      boxShadow='sm'
      bg='white'
      maxW='8xl'
      mb={4}
    >
      <Heading
        size='sm'
        my={4}
      >
        Application Data
      </Heading>
      <Tabs
        index={activeTabIndex}
        onChange={handleTabChange}
        isLazy={false} // Disable lazy loading for instant switching
      >
        <TabList
          ref={tabListRef}
          overflowX={{ base: "auto", lg: "visible" }}
          overflowY='visible'
          css={{
            "&::-webkit-scrollbar": { display: "none" },
            "-ms-overflow-style": "none",
            "scrollbar-width": "none",
          }}
        >
          {activeFormTabs.map((tab: any, index: number) => (
            <Tab
              key={tab.id}
              ref={activeTabIndex === index ? activeTabRef : null}
              _selected={{
                borderBottom: { base: "4px solid", lg: "2px solid" },
                borderColor: "blue.600",
                color: "blue.600",
                fontWeight: "semibold",
              }}
              _hover={{ color: "blue.600" }}
              minW='max-content'
              transition='all 0.2s'
            >
              {tab.name}
            </Tab>
          ))}
          {staticTabs.map((tab, index) => {
            const tabIndex = activeFormTabs.length + index;
            return (
              <Tab
                key={tab}
                ref={activeTabIndex === tabIndex ? activeTabRef : null}
                _selected={{
                  borderBottom: { base: "4px solid", lg: "2px solid" },
                  borderColor: "blue.600",
                  color: "blue.600",
                  fontWeight: "semibold",
                }}
                _hover={{ color: "blue.600" }}
                minW='max-content'
                transition='all 0.2s'
              >
                {tab}
              </Tab>
            );
          })}
        </TabList>

        <TabPanels>
          {activeFormTabs.map(
            (tab: { id: React.Key | null | undefined }, index: any) => (
              <TabPanel
                key={tab.id}
                px={0}
                mx={0}
              >
                {tabContents && tabContents[index]
                  ? tabContents[index]
                  : children}
              </TabPanel>
            )
          )}
          {staticTabs.map((tab, index) => (
            <TabPanel
              key={tab}
              px={0}
              mx={0}
            >
              {tabContents && tabContents[index]
                ? tabContents[index]
                : children}
            </TabPanel>
          ))}
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default ApplicationTabs;
