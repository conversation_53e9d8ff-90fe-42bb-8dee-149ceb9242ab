"use client";

import {
  Application,
  BankAccountType,
  Bank,
  Viewer,
  CreateApplicationTraceInput,
  CreateAccountBankInput,
  ApplicationBankStageInput,
} from "@/__generated/graphql";
import {
  Button,
  Flex,
  Alert,
  AlertIcon,
  AlertDescription,
  Box,
  Heading,
} from "@chakra-ui/react";
import React from "react";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { addBankFormSchema, ValidatedAddBankFormData } from "@/src/schema";
import useSelectBank from "../../_services/SelectBank/useSelectBank";
import { AddBankForm, BankSelection } from "./_components";

type Props = {
  viewer: Viewer;
  application: Application;
  applicationNumber: string;
  banks: Bank[];
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  addBank: ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  setBank: ({
    applicationId,
    accountBankId,
  }: {
    applicationId: ApplicationBankStageInput["applicationId"];
    accountBankId: ApplicationBankStageInput["accountBankId"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  getResolvedAccountNumber: ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
};

const BankInfo = ({
  application,
  applicationNumber,
  viewer,
  banks,
  createAppTrace,
  addBank,
  setBank,
  getResolvedAccountNumber,
}: Props) => {
  const form = useForm<ValidatedAddBankFormData>({
    mode: "onChange",
    resolver: zodResolver(addBankFormSchema),
  });

  const {
    showAddNewBankAccountForm,
    toggleShowAddNewBankAccountForm,
    bankAccounts,
    handleAccountBankSwitch,
    resolveAccountNumber,
    handleAddBankDetails,
    handleSetApplicationBank,
    isLoading,
    error,
    setError,
    selectedBankAccountId,
  } = useSelectBank({
    application,
    applicationNumber,
    viewer,
    createAppTrace,
    addBank,
    setBank,
    getResolvedAccountNumber,
  });

  return (
    <Box
      w={{ base: "100vw", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={4}
        textAlign='center'
        data-testid='bank-info-heading'
      >
        Add Bank Information
      </Heading>

      {error && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='bank-info-error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {error}
          </AlertDescription>
        </Alert>
      )}

      <FormProvider {...form}>
        {bankAccounts && bankAccounts.length ? (
          <BankSelection
            bankAccounts={bankAccounts}
            setBankLoading={isLoading}
            handleAccountBankSwitch={handleAccountBankSwitch}
            selectedBankAccountId={selectedBankAccountId}
          />
        ) : null}

        {bankAccounts && bankAccounts.length > 0 && (
          <Flex
            alignItems='left'
            my={4}
            w='100%'
          >
            <Button
              variant='link'
              onClick={() => {
                toggleShowAddNewBankAccountForm();
                form.reset();
              }}
              colorScheme='customPrimary'
              _hover={{
                textDecoration: "underline",
                color: "customPrimary.600",
              }}
              data-testid='add-new-bank-account-button'
            >
              {showAddNewBankAccountForm
                ? "Add New Bank Account"
                : "Close Form"}
            </Button>
          </Flex>
        )}

        {!showAddNewBankAccountForm && (
          <AddBankForm
            banks={banks}
            resolveAccountNumber={resolveAccountNumber}
            showAddNewBankAccountForm={showAddNewBankAccountForm}
            handleAddBankDetails={handleAddBankDetails}
            addBankDataLoading={isLoading}
            setError={setError}
            toggleShowAddNewBankAccountForm={toggleShowAddNewBankAccountForm}
          />
        )}

        {showAddNewBankAccountForm ? (
          <Button
            onClick={() => {
              handleSetApplicationBank();
            }}
            isLoading={isLoading}
            w='full'
            px={8}
            data-testid='continue-button'
          >
            Continue
          </Button>
        ) : null}
      </FormProvider>
    </Box>
  );
};
export default BankInfo;
