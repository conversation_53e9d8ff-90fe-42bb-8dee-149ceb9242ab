"use client";
import { NumericInput, Select, Input } from "@/components/forms";
import { AddBankFormProps } from "@/src/types/addBankForm";
import {
  BankAccountDetails,
  Bank,
  BankAccountType,
} from "@/__generated/graphql";
import { Button, Flex, FormControl, Text } from "@chakra-ui/react";
import React, { useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";

type Props = {
  banks: Bank[];
  resolveAccountNumber: (
    data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountType">
  ) => Promise<any>;
  showAddNewBankAccountForm: boolean;
  handleAddBankDetails: (
    data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountName">
  ) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  addBankDataLoading: boolean;
  setError: React.Dispatch<React.SetStateAction<string>>;
  toggleShowAddNewBankAccountForm: () => void;
};

const AddBankForm = ({
  addBankDataLoading,
  banks,
  handleAddBankDetails,
  resolveAccountNumber,
  showAddNewBankAccountForm,
  setError,
  toggleShowAddNewBankAccountForm,
}: Props) => {
  const {
    handleSubmit,
    control,
    watch,
    formState: { errors, isSubmitting },
    reset,
  } = useFormContext<AddBankFormProps>();

  const [resolveBank, setResolveBank] = useState<BankAccountDetails | null>(
    null
  );

  const bankId = watch("bankId");
  const accountNumber = watch("accountNumber");
  const accountType = watch("accountType");

  const accountName =
    resolveBank && !Array.isArray(resolveBank)
      ? resolveBank?.accountName
      : undefined;

  const onSubmit = async (data: AddBankFormProps) => {
    try {
      const payload = {
        bankId: data?.bankId,
        accountName,
        accountNumber: data?.accountNumber,
      };
      const res = await handleAddBankDetails(payload);

      if (res?.error) {
        setError(res?.error);
        return;
      }

      if (res?.data) {
        reset();
        toggleShowAddNewBankAccountForm();
      }
    } catch (error: any) {
      setError(error?.message);
    }
  };

  const bankAccountType = accountType || BankAccountType.Personal;

  useEffect(() => {
    if (accountNumber?.length === 10 && bankId && bankAccountType) {
      const fetchAccountDetails = async () => {
        const res = await resolveAccountNumber({
          bankId,
          accountNumber,
          accountType: bankAccountType,
        });
        setResolveBank(res?.resolveAccountNumber);
      };

      fetchAccountDetails();
    }
  }, [bankId, accountNumber, bankAccountType, resolveAccountNumber]);
  return (
    <FormControl as='form'>
      <Flex
        gap={4}
        flexDir='column'
      >
        <Controller
          name='accountType'
          control={control}
          render={({ field }) => (
            <Select
              {...field}
              label='Account Type'
              autoFocus
              isDisabled={addBankDataLoading || isSubmitting}
              isInvalid={!!errors?.accountType}
              defaultValue='personal'
              data-testid='account-type-select'
            >
              <option value='personal'>Personal</option>
              <option value='business'>Business</option>
            </Select>
          )}
        />

        <Controller
          name='bankId'
          control={control}
          render={({ field }) => (
            <Select
              {...field}
              placeholder='--Select a Bank--'
              label='Bank'
              isDisabled={addBankDataLoading || isSubmitting}
              isInvalid={!!errors?.bankId}
              errorMessage={errors?.bankId?.message as string}
              data-testid='bank-select'
            >
              {banks?.map((data, idx) => (
                <option
                  key={idx}
                  value={data?.id}
                >
                  {data?.name}
                </option>
              ))}
            </Select>
          )}
        />

        <Flex
          gap={6}
          flexDir='column'
        >
          <Controller
            name='accountNumber'
            control={control}
            render={({ field }) => (
              <NumericInput
                allowLeadingZeros
                thousandSeparator={false}
                {...field}
                label='Account Number'
                maxLength={10}
                placeholder='Enter your account number'
                isInvalid={!!errors?.accountNumber}
                errorMessage={errors?.accountNumber?.message as string}
                disabled={addBankDataLoading || isSubmitting}
                data-testid='account-number-input'
              />
            )}
          />
          {accountName &&
            accountNumber?.length === 10 &&
            bankId &&
            bankAccountType && (
              <Input
                value={accountName}
                borderRadius={6}
                label='Account Name'
                isDisabled
                isLoading={addBankDataLoading}
                data-testid='account-name-input'
              />
            )}
        </Flex>

        <Text
          mb={6}
          size='sm'
        >
          <b>Note:</b> Please select the <b>Personal</b> account type for
          personal loans and the <b>Business</b> account type for business
          loans.
        </Text>
      </Flex>

      {!showAddNewBankAccountForm && (
        <Button
          onClick={handleSubmit(onSubmit)}
          isLoading={isSubmitting}
          w='full'
          px={8}
          data-testid='add-bank-button'
        >
          Next
        </Button>
      )}
    </FormControl>
  );
};
export default AddBankForm;
