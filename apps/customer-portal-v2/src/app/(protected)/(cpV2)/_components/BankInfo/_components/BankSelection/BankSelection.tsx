"use client";

import { Viewer } from "@/__generated/graphql";
import {
  FormControl,
  Radio,
  RadioGroup,
  Stack,
  Flex,
  Text,
} from "@chakra-ui/react";
import React, { useEffect, useRef } from "react";

const BankSelection = ({
  bankAccounts,
  setBankLoading,
  handleAccountBankSwitch,
  selectedBankAccountId,
}: {
  bankAccounts: Viewer["account"]["bankAccounts"];
  setBankLoading: boolean;
  handleAccountBankSwitch: ({ id }: { id: string }) => void;
  selectedBankAccountId: string;
}) => {
  const refs = useRef<{ [key: string]: HTMLElement | null }>({});

  useEffect(() => {
    if (selectedBankAccountId) {
      const selectedElement = refs.current[selectedBankAccountId];
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }
  }, [selectedBankAccountId]);

  return (
    <FormControl
      as='div'
      gap={4}
      display='flex'
      flexDirection='column'
      maxH='500px'
      overflowY='auto'
      border='1px solid'
      borderColor='gray.200'
      p={{
        base: 1,
        md: 4,
      }}
      borderRadius={6}
      data-testid='bank-selection-list'
    >
      {bankAccounts?.map((bankAccount) => (
        <RadioGroup
          key={bankAccount?.id}
          onChange={(e) => {
            handleAccountBankSwitch({ id: e });
          }}
          value={selectedBankAccountId}
          border='1px solid'
          borderColor='gray.200'
          borderRadius={6}
          isDisabled={setBankLoading}
          w='100%'
          lineHeight='18px'
          _hover={{
            borderColor: "customBrand.400",
          }}
          data-testid='bank-selection-item'
        >
          <Flex>
            <Radio
              value={bankAccount?.id}
              w='100%'
              p={{
                base: 2,
                md: 4,
              }}
              justifyContent='space-between'
              flexDirection='row-reverse'
              isRequired
              ref={(el) => {
                if (bankAccount?.id) {
                  refs.current[bankAccount.id] = el;
                }
              }}
            >
              <Flex width='100%'>
                <Stack lineHeight=''>
                  <Text
                    textTransform='capitalize'
                    fontWeight='bold'
                  >
                    {bankAccount?.accountName} - {bankAccount?.accountNumber}
                  </Text>
                  <Text>
                    {bankAccount?.bank?.name}{" "}
                    {bankAccount?.isDefault && <b>(Default)</b>}
                  </Text>
                </Stack>
              </Flex>
            </Radio>
          </Flex>
        </RadioGroup>
      ))}
    </FormControl>
  );
};
export default BankSelection;
