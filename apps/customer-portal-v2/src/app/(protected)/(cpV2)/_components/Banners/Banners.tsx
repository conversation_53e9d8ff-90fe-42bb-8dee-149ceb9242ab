"use client";
import {
  <PERSON>ert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  Flex,
  Text,
  VStack,
} from "@chakra-ui/react";
import Link from "next/link";
import { useParams } from "next/navigation";
import React from "react";

const Banners = ({
  hasConfirmedEmail,
  hasConfirmedBvn,
}: {
  hasConfirmedEmail: boolean;
  hasConfirmedBvn: boolean;
}) => {
  const params = useParams();

  const { basePath, id } = params || {};

  const settingsUrl = basePath ? `/${basePath}/settings` : `/settings`;
  const verifyEmailUrl = basePath
    ? `/${basePath}/application/${id}/verify-email`
    : `/application/${id}/verify-email`;

  return (
    <Box
      maxW='8xl'
      bg='transparent'
    >
      <VStack spacing={4}>
        {!hasConfirmedBvn && (
          <Alert
            status='error'
            w='full'
            py={4}
            borderRadius={6}
          >
            <AlertIcon />
            <AlertDescription
              whiteSpace='pre-wrap'
              wordBreak='break-word'
              w='full'
            >
              <Flex
                alignItems='center'
                justifyContent='flex-start'
              >
                <Text>
                  We require your BVN to fetch and verify your information.
                  Please proceed to the settings page to verify your BVN.
                  <Text as='span'>
                    <Button
                      as={Link}
                      variant='link'
                      href={settingsUrl}
                      color='customPrimary.500'
                      mx={1}
                      fontWeight='bold'
                      _visited={{
                        color: "customPrimary.500",
                      }}
                      _hover={{
                        color: "customPrimary.600",
                      }}
                    >
                      Click to proceed
                    </Button>
                  </Text>
                </Text>
              </Flex>
            </AlertDescription>
          </Alert>
        )}

        {!hasConfirmedEmail && (
          <Alert
            status='error'
            w='full'
            py={4}
            borderRadius={6}
          >
            <AlertIcon />
            <AlertDescription
              whiteSpace='pre-wrap'
              wordBreak='break-word'
              w='full'
            >
              <Flex
                alignItems='center'
                justifyContent='flex-start'
              >
                <Text>
                  A verification link has been sent to your email. Please, click
                  on this link and verify your email.
                  <Text as='span'>
                    <Button
                      as={Link}
                      variant='link'
                      href={verifyEmailUrl}
                      color='customPrimary.500'
                      mx={1}
                      fontWeight='bold'
                      _visited={{
                        color: "customPrimary.500",
                      }}
                      _hover={{
                        color: "customPrimary.600",
                      }}
                    >
                      Click to proceed
                    </Button>
                  </Text>
                </Text>
              </Flex>
            </AlertDescription>
          </Alert>
        )}
      </VStack>
    </Box>
  );
};

export default Banners;
