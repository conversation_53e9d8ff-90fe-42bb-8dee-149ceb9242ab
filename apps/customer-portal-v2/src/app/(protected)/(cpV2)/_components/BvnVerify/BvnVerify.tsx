"use client";

import {
  Box,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Stack,
  Text,
  AlertDescription,
  AlertIcon,
  Alert,
  Input,
} from "@chakra-ui/react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { DatePicker, NumericInput } from "@/components/forms";
import { Controller, useForm } from "react-hook-form";
import { useParams, useRouter } from "next/navigation";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { BvnType, BvnValidationSchema } from "@/src/schema";
import {
  ClientInfo,
  CustomerBvnStatus,
  CustomerBvnStatusInput,
} from "@/__generated/graphql";
import { generateChakraScale } from "@/utils/index";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { FullPageLoader } from "@/components/FullPageLoader";

type Props = {
  getCustomerBvnStatus: ({
    bvn,
    bvnDOB,
    bvnPhoneDigits,
  }: {
    bvn: CustomerBvnStatusInput["bvn"];
    bvnDOB: CustomerBvnStatusInput["bvnDOB"];
    bvnPhoneDigits: CustomerBvnStatusInput["bvnPhoneDigits"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
  >;
};

const BvnVerify = ({ getCustomerBvnStatus }: Props) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath as string;

  const path = basePath ?? null;

  const { error, setError, success, setSuccess } = useMessageTimer({
    duration: 10000,
  });
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const [bvnData, setBvnData] = useState<CustomerBvnStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { clientTheme } = clientInfo || {};
  const { secondaryColor } = clientTheme || {};

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting, isValid },
    watch,
  } = useForm<BvnType>({
    resolver: zodResolver(BvnValidationSchema),
    mode: "onChange",
  });

  const values = watch();

  const payload = useMemo(
    () => ({
      bvn: values?.bvn,
      bvnDOB: values?.bvnDOB,
      bvnPhoneDigits: values?.bvnPhoneDigits,
    }),
    [values]
  );

  const verifyBvn = useCallback(async () => {
    if (isValid) {
      setError("");
      setBvnData(null);
      setIsLoading(true);

      try {
        const res = await getCustomerBvnStatus(payload);

        if (res?.error) {
          setError(res?.error);
          setBvnData(null);
        }

        if (res?.data?.customerBvnStatus?.name) {
          setBvnData(res?.data?.customerBvnStatus);
          setError("");
          setSuccess("BVN verified successfully. Click next to continue.");
        }
        setIsLoading(false);
      } catch (error: any) {
        const errorMessage =
          error?.message ||
          "BVN verification failed due to an unexpected error.";
        setError(errorMessage);
        setBvnData(null);
        setIsLoading(false);
      }
    }
  }, [getCustomerBvnStatus, payload, isValid]);

  const onSubmit = async () => {
    setIsLoading(true);
    setError("");
    const path = basePath
      ? `/${basePath}/sign-up/verify-email`
      : `/sign-up/verify-email`;
    router.push(path);
  };

  const customColor = generateChakraScale(secondaryColor!);

  if (!clientInfo) return <FullPageLoader />;

  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='24px'
        textAlign='center'
        data-testid='bvn-verify-heading'
      >
        BVN Verification
      </Heading>

      {error && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {error}
          </AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert
          status='success'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='success-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {success}
          </AlertDescription>
        </Alert>
      )}

      <Stack
        as='form'
        spacing={8}
        w='full'
        onSubmit={handleSubmit(onSubmit)}
        pt={8}
      >
        {isSubmitting && <Text>Verifying BVN...</Text>}
        <FormControl isInvalid={!!errors?.bvn}>
          <FormLabel>BVN</FormLabel>
          <Controller
            name='bvn'
            control={control}
            defaultValue=''
            render={({ field }) => (
              <NumericInput
                maxLength={11}
                allowLeadingZeros
                placeholder='Enter your BVN'
                thousandSeparator={false}
                value={field.value}
                onChange={field.onChange}
                isInvalid={!!errors?.bvn}
                disabled={isLoading}
                data-testid='bvn-input'
              />
            )}
          />
          <FormErrorMessage
            fontSize='sm'
            data-testid='bvn-error'
          >
            {errors?.bvn?.message}
          </FormErrorMessage>
        </FormControl>

        <FormControl isInvalid={!!errors?.bvnPhoneDigits}>
          <FormLabel>Last 4 digits of BVN phone number</FormLabel>
          <Controller
            name='bvnPhoneDigits'
            control={control}
            defaultValue=''
            render={({ field }) => (
              <NumericInput
                maxLength={4}
                allowLeadingZeros
                placeholder='Enter last 4 digits'
                thousandSeparator={false}
                value={field.value}
                onChange={field.onChange}
                isInvalid={!!errors?.bvnPhoneDigits}
                disabled={isLoading}
                data-testid='bvn-phone-digits-input'
              />
            )}
          />
          <FormErrorMessage
            fontSize='sm'
            data-testid='bvn-phone-digits-error'
          >
            {errors?.bvnPhoneDigits?.message}
          </FormErrorMessage>
        </FormControl>

        <FormControl isInvalid={!!errors?.bvnDOB}>
          <FormLabel>BVN date of birth</FormLabel>
          <Controller
            name='bvnDOB'
            control={control}
            defaultValue=''
            render={({ field }) => (
              <DatePicker
                value={field.value}
                onSelectDate={(dates) => field.onChange(dates)}
                dateFormat='dd-MMM-yyyy'
                placeholder='Select BVN date of birth'
                isDisabled={isLoading}
                customColor={customColor}
                data-testid='bvn-dob-input'
              />
            )}
          />
          <FormErrorMessage
            fontSize='sm'
            data-testid='bvn-dob-error'
          >
            {errors?.bvnDOB?.message}
          </FormErrorMessage>
        </FormControl>

        {bvnData && (
          <FormControl>
            <FormLabel>BVN name</FormLabel>
            <Input
              value={bvnData?.name!}
              disabled
              data-testid='bvn-name-input'
            />
          </FormControl>
        )}

        {!bvnData ? (
          <Button
            isLoading={isLoading}
            w='full'
            px={8}
            onClick={verifyBvn}
            data-testid='verify-bvn-button'
          >
            Verify Bvn
          </Button>
        ) : (
          <Button
            type='submit'
            isLoading={isSubmitting || isLoading}
            w='full'
            px={8}
            data-testid='next-button'
          >
            Next
          </Button>
        )}
      </Stack>

      <Text
        fontSize='sm'
        color='gray.500'
        textAlign='center'
        my={4}
        w='full'
      >
        NOTE: Dial *565*0# on your Bank registered mobile number to get your BVN
      </Text>
    </Box>
  );
};

export default BvnVerify;
