"use client";

import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { formatAmount } from "@/utils/index";
import {
  Application,
  CardReferenceStatusInput,
  ClientInfo,
  CreateApplicationTraceInput,
  GenerateAddCardReferenceInput,
  Viewer,
} from "@/__generated/graphql";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  Flex,
  Heading,
  Text,
} from "@chakra-ui/react";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import useAddCard from "../../_services/AddCard/useAddCard";

type Props = {
  viewer: Viewer;
  application: Application;
  applicationNumber: string;
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  generateByPayStackRef: ({
    metadata,
  }: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  runSkipCardCollection: () => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  getByPayStackRef: ({
    reference,
    loanDuration,
  }: {
    reference: CardReferenceStatusInput["reference"];
    loanDuration: CardReferenceStatusInput["loanDuration"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
};

const CardInfo = ({
  application,
  applicationNumber,
  viewer,
  createAppTrace,
  generateByPayStackRef,
  runSkipCardCollection,
  getByPayStackRef,
}: Props) => {
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);
  const {
    reference,
    generateAddCardRef,
    handleSkipCardCollection,
    collectionMethod,
    error,
    isSubmitting,
    handlePaystackWidgetTrigger,
    amount,
    skipAddCard,
    skipCardStep,
  } = useAddCard({
    application,
    applicationNumber,
    clientInfo,
    viewer,
    createAppTrace,
    generateByPayStackRef,
    runSkipCardCollection,
    getByPayStackRef,
  });

  if (!clientInfo) return <FullPageLoader />;
  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={2}
        textAlign='center'
        data-testid='card-info-heading'
      >
        Add Debit Card
      </Heading>
      <Text
        mb={6}
        textAlign='center'
      >
        Please add the card that is linked to your main income account to
        increase your chances of getting a loan.
      </Text>

      {error && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='card-info-error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {error}
          </AlertDescription>
        </Alert>
      )}

      <Box textAlign='center'>
        <Box data-testid='card-info-card-note'>
          Please note that{" "}
          <b data-testid='card-info-card-note-text'>
            {formatAmount(amount || 0)}
          </b>{" "}
          is a
          <Box
            as='span'
            color='red.500'
            fontWeight={600}
            mx={1}
          >
            NON-REFUNDABLE FEE
          </Box>
          for bank statement check, credit bureau check and card tokenization.
          This fee is not a guarantee of loan disbursement.
        </Box>
      </Box>
      {collectionMethod?.length > 0 && (
        <Flex
          alignItems='center'
          gap={2}
          my={4}
          justifyContent='center'
        >
          <Text> Having trouble adding a card?</Text>
          <Button
            variant='link'
            color='red.500'
            onClick={handleSkipCardCollection}
            data-testid='card-info-skip-card-collection-button'
          >
            Skip Add Debit Card Step
          </Button>
        </Flex>
      )}

      {!isSubmitting && !reference && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='add-card-refrence-error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            <AlertDescription>
              There was an error generating payment reference.
              <Button
                variant='link'
                ml={2}
                color='red.500'
                onClick={generateAddCardRef}
                data-testid='add-card-refrence-retry-button'
              >
                Please, retry
              </Button>
            </AlertDescription>
          </AlertDescription>
        </Alert>
      )}

      <Button
        isDisabled={!reference}
        isLoading={isSubmitting}
        w='full'
        px={8}
        mt={6}
        onClick={handlePaystackWidgetTrigger}
        data-testid='card-info-add-card-button'
      >
        Add Card
      </Button>

      {skipAddCard ? (
        <Button
          variant='outline'
          w='full'
          px={8}
          mt={4}
          onClick={skipCardStep}
          colorScheme='customPrimary'
          color='customPrimary.500'
          data-testid='card-info-skip-card-button'
        >
          I will do this later
        </Button>
      ) : (
        ""
      )}
    </Box>
  );
};
export default CardInfo;
