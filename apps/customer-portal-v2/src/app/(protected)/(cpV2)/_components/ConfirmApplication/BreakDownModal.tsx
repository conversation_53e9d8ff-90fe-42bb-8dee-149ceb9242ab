import { formatAmount } from "@/utils/index";
import { OfferPayload } from "@/__generated/graphql";
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Button,
  Table,
  Thead,
  Tbody,
  Tfoot,
  Tr,
  Th,
  Td,
  TableContainer,
} from "@chakra-ui/react";
import { format } from "date-fns";
import React from "react";

const BreakDownModal = ({
  repaymentBreakdown,
}: {
  repaymentBreakdown: OfferPayload["repaymentBreakdown"];
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const totalPrincipal =
    repaymentBreakdown?.reduce((acc, repayment) => {
      return acc + (repayment?.principalPortion || 0);
    }, 0) || 0;

  const totalInterest =
    repaymentBreakdown?.reduce((acc, repayment) => {
      return acc + (repayment?.interestPortion || 0);
    }, 0) || 0;

  const totalPayment = totalPrincipal + totalInterest;
  return (
    <>
      <Button
        onClick={onOpen}
        w='100%'
        variant='link'
        mx={1}
        colorScheme='customPrimary'
        _hover={{
          textDecoration: "underline",
          color: "customPrimary.600",
        }}
        data-testid='mbs-breakdown-button'
      >
        View Repayment Details
      </Button>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        isCentered
        size='3xl'
      >
        <ModalOverlay />
        <ModalContent
          pos='fixed'
          top='20%'
          transform='translate(-50%, -50%)'
          py={6}
        >
          <ModalHeader
            textAlign='center'
            my={2}
          >
            Repayment Breakdown
          </ModalHeader>
          <ModalCloseButton data-testid='mbs-breakdown-modal-close-button' />
          <ModalBody>
            <TableContainer>
              <Table borderColor='customPrimary.100'>
                <Thead>
                  <Tr fontWeight='900'>
                    <Th borderColor='customPrimary.100'> Repayment Date</Th>
                    <Th borderColor='customPrimary.100'>Principal</Th>
                    <Th borderColor='customPrimary.100'>Interest</Th>
                    <Th borderColor='customPrimary.100'>Total Payment</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {repaymentBreakdown?.map((repayment, idx) => (
                    <Tr
                      key={idx}
                      _odd={{
                        backgroundColor: "customPrimary.50",
                      }}
                    >
                      <Td borderColor='customPrimary.100'>
                        {repayment?.dueDate &&
                          format(new Date(repayment?.dueDate), "MMM dd, yyyy")}
                      </Td>

                      <Td borderColor='customPrimary.100'>
                        {formatAmount(repayment?.principalPortion || 0)}
                      </Td>
                      <Td borderColor='customPrimary.100'>
                        {formatAmount(repayment?.interestPortion || 0)}
                      </Td>
                      <Td borderColor='customPrimary.100'>
                        {formatAmount(
                          (repayment?.principalPortion || 0) +
                            (repayment?.interestPortion || 0)
                        )}
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
                <Tfoot>
                  <Tr fontWeight='600'>
                    <Td borderColor='customPrimary.100'>Total</Td>
                    <Td borderColor='customPrimary.100'>
                      {formatAmount(totalPrincipal)}
                    </Td>
                    <Td borderColor='customPrimary.100'>
                      {formatAmount(totalInterest)}
                    </Td>
                    <Td borderColor='customPrimary.100'>
                      {formatAmount(totalPayment)}
                    </Td>
                  </Tr>
                </Tfoot>
              </Table>
            </TableContainer>{" "}
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};
export default BreakDownModal;
