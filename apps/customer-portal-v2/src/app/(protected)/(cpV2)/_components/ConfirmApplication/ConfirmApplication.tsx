"use client";

import { formatAmount } from "@/utils/index";
import {
  Application,
  ClientInfo,
  CreateApplicationTraceInput,
  PotentialOffer,
} from "@/__generated/graphql";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  Checkbox,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Stack,
  Text,
} from "@chakra-ui/react";
import { format } from "date-fns";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ConfirmAppSchema, ConfirmAppType } from "@/src/schema";
import { useParams, useRouter } from "next/navigation";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";
import useConfirmApplication from "../../_services/ConfirmApplication/useConfirmApplication";
import BreakDownModal from "./BreakDownModal";

const ConfirmApplication = ({
  application,
  potentialOffer,
  error,
  completeApplication,
  createAppTrace,
}: {
  application: Application;
  potentialOffer: PotentialOffer;
  error: string;
  completeApplication: () => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  createAppTrace: ({
    page,
    comment,
    isDebug,
    metadata,
  }: {
    page: CreateApplicationTraceInput["page"];
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
  }) => Promise<any>;
}) => {
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const router = useRouter();
  const params = useParams();

  const basePath = params?.basePath as string;
  const { runCompleteApplication, offerError, setError } =
    useConfirmApplication({
      completeApplication,
      createAppTrace,
    });
  const {
    setError: setConfirmApplicationError,
    error: confirmApplicationError,
  } = useMessageTimer({
    duration: 10000,
  });
  const path = basePath ?? null;

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ConfirmAppType>({
    mode: "onChange",
    resolver: zodResolver(ConfirmAppSchema),
  });

  const { dateOfRepayment } = application || {};
  const {
    repaymentBreakdown,
    baseAmount,
    fullAmount,
    loanTenor,
    interestRate,
    repaymentType,
    policy: suggestedPolicy,
  } = potentialOffer || {};

  const { processingFee, processingFeeDebitMethod } = suggestedPolicy || {};

  const details = [
    {
      label: "Requested Amount",
      value: formatAmount(baseAmount || 0),
    },
    {
      label: "Repayment Amount",
      value: formatAmount(fullAmount || 0),
    },
    {
      label: "Repayment Date",
      value:
        dateOfRepayment && format(new Date(dateOfRepayment), "MMM d, yyyy"),
    },
    {
      label: "Interest Rate",
      value: `${interestRate || 0}% ${repaymentType || ""}`,
    },
    {
      label: "Loan Tenor",
      value: loanTenor || "N/A",
    },
  ];

  const onSubmit = async () => {
    try {
      const res = await runCompleteApplication();
      if (res?.error) {
        setError(res?.error);
      }

      if (res?.data) {
        const path = basePath ? `/${basePath}/dashboard` : `/dashboard`;
        router.push(path);
      }
    } catch (error: any) {
      setError(error?.message);
    }
  };

  const breakDownError = error || offerError;

  useEffect(() => {
    if (breakDownError) {
      setConfirmApplicationError(breakDownError);
    }
  }, [breakDownError]);

  if (!clientInfo) return <FullPageLoader />;
  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={6}
        textAlign='center'
        data-testid='confirm-application-heading'
      >
        Confirm your loan application
      </Heading>

      {confirmApplicationError && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='confirm-application-error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {confirmApplicationError}
          </AlertDescription>
        </Alert>
      )}

      <Stack
        spacing={6}
        as='form'
        onSubmit={handleSubmit(onSubmit)}
      >
        <Flex
          w='100%'
          p={4}
          border='1px solid'
          borderColor='gray.300'
          flexDir='column'
          gap={6}
          bg='blackAlpha.100'
          borderRadius={6}
        >
          {details?.map((detail, idx) => (
            <Flex
              key={idx}
              justifyContent='space-between'
            >
              <Text>{detail?.label}</Text>
              <Box fontWeight='700'>{detail?.value}</Box>
            </Flex>
          ))}
        </Flex>

        <BreakDownModal repaymentBreakdown={repaymentBreakdown} />

        {processingFee?.value === 0 && (
          <Text
            textAlign='center'
            data-testid='confirm-application-processing-fee-warning'
          >
            If your loan is approved, a processing fee of{" "}
            {processingFee?.calcBy === "PERCENTAGE"
              ? `${processingFee.value}%`
              : formatAmount(processingFee?.value || 0)}{" "}
            will be deducted from the loan amount{" "}
            {processingFeeDebitMethod === "POST_DISBURSEMENT"
              ? "after"
              : "before"}{" "}
            credit to your bank account.
          </Text>
        )}

        <FormControl isInvalid={!!errors?.policyCheck}>
          <FormLabel
            htmlFor='policy'
            m={0}
          >
            <Flex
              alignItems='flex-start'
              gap={4}
            >
              <Checkbox
                id='policy'
                {...register("policyCheck")}
                isInvalid={!!errors?.policyCheck}
                mt={1}
                disabled={isSubmitting}
                data-testid='confirm-application-policy-checkbox'
              />

              <Text
                as='span'
                data-testid='confirm-application-policy-text'
              >
                I have read all the information provided in my application and
                would like to be considered as a beneficiary under the{" "}
                <b>{clientInfo?.name}</b> Scheme. The information provided in my
                application is accurate and I understand there are penalties for
                providing any false information to the <b>{clientInfo?.name}</b>
                .
              </Text>
            </Flex>
          </FormLabel>

          <FormErrorMessage
            fontSize='sm'
            data-testid='confirm-application-policy-error'
          >
            {errors?.policyCheck?.message}
          </FormErrorMessage>
        </FormControl>

        <Button
          type='submit'
          w='full'
          isLoading={isSubmitting}
          onClick={handleSubmit(onSubmit)}
          data-testid='confirm-application-complete-button'
        >
          Complete Application
        </Button>
      </Stack>
    </Box>
  );
};
export default ConfirmApplication;
