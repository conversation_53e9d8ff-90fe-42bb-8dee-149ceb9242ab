"use client";
import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo, updateForm } from "@/src/app/actions";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { generateChakraScale } from "@/utils/index";
import { overrides } from "@/utils/theme";
import { ClientInfo, Viewer } from "@/__generated/graphql";
import {
  Alert,
  AlertIcon,
  Button,
  VStack,
  AlertDescription,
  Heading,
  Box,
  ChakraProviderProps,
  extendTheme,
  withDefaultColorScheme,
} from "@chakra-ui/react";
import { OriginateFormGeneratorV2 } from "@indicina1/originate-form-builder";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import useUploadHelper from "../../_services/UploadHelper/useUploadHelper";

type Props = {
  user: Viewer["me"];
  token: string;
};

const CustomerInfo = ({ user, token }: Props) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath as string;
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const { error, setError } = useMessageTimer();

  const path = basePath ?? null;

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const { kycConfiguration, clientTheme } = clientInfo || {};
  const { secondaryColor } = clientTheme || {};

  const { handleUpdateForm, uploadError, customerLoading, setCustomerLoading } =
    useUploadHelper({
      user,
      token,
    });

  const onSubmit = async (data: any) => {
    setError("");
    try {
      setCustomerLoading(true);
      const upload = await handleUpdateForm(
        { ...data },
        "Customer Information"
      );
      if (upload?.data) {
        const res = await updateForm(
          JSON.parse(JSON.stringify({ ...data, token }))
        );

        if (res?.data?.updateUserData?.success) {
          const path = basePath
            ? `/${basePath}/application/new`
            : `/application/new`;
          router.push(path);
        }

        if (res?.error) {
          setCustomerLoading(false);
          setError(res?.error);
        }
      }

      if (upload?.error) {
        setCustomerLoading(false);
        setError(upload?.error);
      }
    } catch (error: any) {
      setCustomerLoading(false);
      setError(error?.message);
    }
  };

  useEffect(() => {
    if ((error || uploadError) && typeof window !== "undefined") {
      window.scrollTo(0, 0);
      setError(uploadError);
    }
  }, [error, uploadError]);

  const customColorScale = generateChakraScale(secondaryColor!);
  const theme: ChakraProviderProps["theme"] = extendTheme(
    {
      ...overrides,
      colors: {
        customBrand: secondaryColor ? customColorScale : overrides.colors?.blue,
      },
      components: {
        Input: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },

        Select: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },

        Textarea: {
          baseStyle: {
            borderRadius: 6,
          },
        },
        NumberInput: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },
        Checkbox: {
          baseStyle: {
            control: {
              borderRadius: 4,
            },
          },
        },
        Radio: {
          baseStyle: {
            control: {
              borderRadius: 10,
            },
          },
        },
        Button: {
          baseStyle: {
            borderRadius: 6,
          },
          variants: {
            outline: {
              borderRadius: 6,
            },
          },
        },
      },
    },
    withDefaultColorScheme({
      colorScheme: secondaryColor ? "customBrand" : "blue",
    })
  );

  if (!clientInfo) return <FullPageLoader />;
  return (
    <Box
      w={{ base: "100vw", md: "2xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <VStack w='100%'>
        <Heading
          size='lg'
          data-testid='customer-info-heading'
        >
          Customer Information
        </Heading>
        {error && (
          <Alert
            status='error'
            borderRadius={6}
            data-testid='customer-info-error'
          >
            <AlertIcon />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <OriginateFormGeneratorV2
          onSubmit={onSubmit}
          step={0}
          tabs={[kycConfiguration?.form]}
          defaultFormValues={""}
          theme={theme}
          kyc={true}
          disabled={customerLoading}
        >
          <Button
            type='submit'
            width='100%'
            isLoading={customerLoading}
            w='full'
            colorScheme='customBrand'
            data-testid='customer-info-submit-button'
          >
            Proceed
          </Button>
        </OriginateFormGeneratorV2>
      </VStack>
    </Box>
  );
};

export default CustomerInfo;
