"use client";

import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { TriggerPasswordResetInput } from "@/__generated/graphql";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Heading,
  Stack,
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Button,
  Text,
  Flex,
  Link as ChakraLink,
  Center,
} from "@chakra-ui/react";
import Link from "next/link";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ForgotPasswordType, ForgotPasswordSchema } from "@/src/schema";
import { BadgeCheck } from "lucide-react";
import { useParams } from "next/navigation";

type Props = {
  forgotPassword: ({
    email,
  }: {
    email: TriggerPasswordResetInput["email"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
  >;
};
const ForgotPasswordPage = ({ forgotPassword }: Props) => {
  const { error, setError } = useMessageTimer();
  const [onSuccess, setOnSuccess] = useState(false);
  const [userEmail, setUserEmail] = useState("");

  const params = useParams();
  const basePath = params?.basePath;

  const signUpUrl = basePath ? `/${basePath}/sign-up` : `/sign-up`;
  const baseUrl = basePath ? `/${basePath}` : `/`;

  const {
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ForgotPasswordType>({
    resolver: zodResolver(ForgotPasswordSchema),
    mode: "onChange",
  });

  const onSubmit = async (data: ForgotPasswordType) => {
    setError("");
    try {
      const { email } = data;
      const res = await forgotPassword({ email: email.trim() });

      if (res?.error) {
        setError(res?.error);
      }

      if (res?.data?.triggerPasswordReset?.ok) {
        setOnSuccess(true);
        setUserEmail(email);
        reset();
      }
    } catch (error: any) {
      setError(error?.message);
    }
  };

  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      borderRadius='lg'
      py={8}
      px={{ base: 4, md: 8 }}
    >
      <Heading
        as='h2'
        fontSize={{ base: "20px", md: "24px" }}
        mb={4}
        textAlign='center'
        data-testid='title'
      >
        {onSuccess
          ? "Reset Password"
          : "Enter your email address to reset your password"}
      </Heading>
      {onSuccess ? (
        <SuccessPage email={userEmail} />
      ) : (
        <>
          {error && (
            <Alert
              status='error'
              w='full'
              my={4}
              borderRadius={6}
              data-testid='error-alert'
            >
              <AlertIcon />
              <AlertDescription
                whiteSpace='pre-wrap'
                wordBreak='break-word'
              >
                {error}
              </AlertDescription>
            </Alert>
          )}

          <Stack
            spacing={6}
            pt={6}
            as='form'
            onSubmit={handleSubmit(onSubmit)}
          >
            <Stack spacing={4}>
              <FormControl isInvalid={!!errors.email}>
                <FormLabel>Email</FormLabel>
                <Input
                  type='email'
                  placeholder='Enter your email address'
                  {...register("email")}
                  disabled={isSubmitting}
                  data-testid='email-input'
                />
                <FormErrorMessage
                  fontSize='sm'
                  data-testid='email-error'
                >
                  {errors?.email?.message}
                </FormErrorMessage>
              </FormControl>

              <Button
                type='submit'
                isLoading={isSubmitting}
                data-testid='submit-button'
              >
                Reset Password
              </Button>
            </Stack>
          </Stack>

          <Flex
            mt={4}
            justifyContent='center'
            alignItems='center'
          >
            Don't have an account?
            <Text mx={2}>
              <ChakraLink
                as={Link}
                href={signUpUrl}
                passHref
                fontWeight='bold'
                _visited={{
                  color: "customPrimary.500",
                }}
                _hover={{
                  color: "customPrimary.600",
                }}
                color='customPrimary.500'
                data-testid='sign-up-link'
              >
                Sign Up
              </ChakraLink>
            </Text>
          </Flex>
        </>
      )}

      <Text
        mx={2}
        textAlign='center'
        mt={4}
      >
        <ChakraLink
          as={Link}
          href={baseUrl}
          passHref
          fontWeight='bold'
          _visited={{
            color: "customPrimary.500",
          }}
          _hover={{
            color: "customPrimary.600",
          }}
          color='customPrimary.500'
          data-testid='back-to-sign-in-link'
        >
          Back to Sign In
        </ChakraLink>
      </Text>
    </Box>
  );
};

export default ForgotPasswordPage;

type SuccessProps = {
  email: string;
};
const SuccessPage = ({ email }: SuccessProps) => {
  return (
    <Stack
      spacing={8}
      textAlign='center'
      pt={4}
    >
      <Text data-testid='success-message'>
        If <b>{email}</b> is registered to an account in our system, an email
        with a password reset link has been sent to the email address.
      </Text>
      <Center>
        <BadgeCheck
          width='80px'
          height='80px'
          stroke='#9DD858'
        />
      </Center>
    </Stack>
  );
};
