"use client";

import { saveForm } from "@/src/app/actions";
import { generateChakraScale } from "@/utils/index";
import { overrides } from "@/utils/theme";
import {
  Box,
  Alert,
  AlertIcon,
  Button,
  VStack,
  AlertDescription,
  Heading,
  extendTheme,
  ChakraProviderProps,
  withDefaultColorScheme,
} from "@chakra-ui/react";
import { OriginateFormGeneratorV2 } from "@indicina1/originate-form-builder";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";
import { useFormBuilder } from "../../_services";

const FormBuilder = () => {
  const {
    applicationForm,
    formIndex,
    handleSubmit,
    isSubmitting,
    defaultFormValues,
    error,
    setError,
    currentStepName,
    setIsSubmitting,
    activeFormTabs,
    backToLastStep,
    isLoading,
    clientInfo,
    application,
    token,
  } = useFormBuilder();

  const { clientTheme } = clientInfo || {};
  const { secondaryColor } = clientTheme || {};
  const applicationId = application?.id;
  const router = useRouter();

  const onSubmit = async (data: any) => {
    try {
      setError("");
      await handleSubmit({ data });
      setIsSubmitting(false);
    } catch (error: any) {
      setIsSubmitting(false);
      setError(error?.message);
    }
  };

  useEffect(() => {
    if (error && typeof window !== "undefined") {
      window.scrollTo(0, 0);
    }
  }, [error]);

  const customFormName = activeFormTabs?.[formIndex]?.builders?.[0]?.title;

  useEffect(() => {
    const saveFormData = async () => {
      if (customFormName === "Card" || customFormName === "Bank Info") {
        await saveForm({
          formData: {
            data: { step: formIndex + 1, path: "" },
            applicationId,
          },
          token,
        });
        router.refresh();
      }
    };
    saveFormData();
  }, [customFormName, activeFormTabs]);

  const customColorScale = generateChakraScale(secondaryColor!);
  const theme: ChakraProviderProps["theme"] = extendTheme(
    {
      ...overrides,
      colors: {
        customBrand: secondaryColor ? customColorScale : overrides.colors?.blue,
      },
      components: {
        Input: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },

        Select: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },

        Textarea: {
          baseStyle: {
            borderRadius: 6,
          },
        },
        NumberInput: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },
        Checkbox: {
          baseStyle: {
            control: {
              borderRadius: 4,
            },
          },
        },
        Radio: {
          baseStyle: {
            control: {
              borderRadius: 10,
            },
          },
        },
        Button: {
          baseStyle: {
            borderRadius: 6,
          },
          variants: {
            outline: {
              borderRadius: 6,
            },
          },
        },
      },
    },
    withDefaultColorScheme({
      colorScheme: secondaryColor ? "customBrand" : "blue",
    })
  );

  return (
    <>
      {Array.isArray(activeFormTabs) && activeFormTabs?.length > 0 && (
        <Box
          w={{ base: "100vw", md: "2xl" }}
          bg='white'
          py={8}
          px={{ base: 4, md: 8 }}
          borderRadius='md'
          boxShadow='sm'
        >
          <VStack w='100%'>
            <Heading
              size='lg'
              data-testid='form-builder-heading'
            >
              {currentStepName}
            </Heading>
            {error && (
              <Alert
                status='error'
                borderRadius={6}
                data-testid='form-builder-error-alert'
              >
                <AlertIcon />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            {formIndex < activeFormTabs?.length && (
              <OriginateFormGeneratorV2
                key={`${defaultFormValues}_${formIndex}`}
                tabs={applicationForm}
                onSubmit={onSubmit}
                step={formIndex}
                theme={theme}
                defaultFormValues={defaultFormValues}
                kyc={true}
                disabled={isSubmitting || isLoading}
              >
                <Button
                  type='submit'
                  isLoading={isSubmitting}
                  w='full'
                  px={8}
                  data-testid='form-builder-next-button'
                >
                  Next
                </Button>
              </OriginateFormGeneratorV2>
            )}
            {formIndex > 0 ? (
              <Button
                variant='outline'
                isLoading={isLoading}
                w='full'
                colorScheme='customPrimary'
                px={8}
                mt={2}
                onClick={backToLastStep}
                color='customPrimary.500'
                data-testid='form-builder-back-button'
              >
                Back
              </Button>
            ) : null}
          </VStack>
        </Box>
      )}
    </>
  );
};
export default FormBuilder;
