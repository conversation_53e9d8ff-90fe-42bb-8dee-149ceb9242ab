"use client";
import {
  type ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  Center,
  Flex,
  ChakraProviderProps,
  extendTheme,
  Grid,
  GridItem,
  ThemeProvider,
  withDefaultColorScheme,
  Heading,
  Text,
} from "@chakra-ui/react";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { signOut, useSession } from "next-auth/react";
import { IdleTimeoutManager } from "idle-timer-manager";
import { ClientInfo } from "@/__generated/graphql";
import { useParams, useRouter } from "next/navigation";
import { jwtDecode } from "jwt-decode";
import { generateChakraScale } from "@/utils/index";
import { overrides } from "@/utils/theme";
import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";

interface LayoutProps {
  children: ReactNode;
}

const IDLE_TIMEOUT_MINUTES = 10 * 60; // 10 minute
const FIVE_MINUTES_MS = 5 * 60 * 1000; // 5 minutes in milliseconds

interface DecodedToken {
  exp: number;
  [key: string]: any;
}

const Layout = ({ children }: LayoutProps) => {
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const { clientTheme, name, contactDetails } = clientInfo || {};
  const { primaryColor, secondaryColor } = clientTheme || {};

  const params = useParams();
  const basePath = params?.basePath! as string;
  const path = basePath ?? null;

  const router = useRouter();
  const { data } = useSession();
  const accessToken = data?.accessToken || "";

  const tokenExpiryTimerRef = useRef<ReturnType<typeof setTimeout> | null>(
    null
  );

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        setIsLoading(true);
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
        setIsLoading(false);
      } catch (error) {
        setClientInfo(null);
        setIsLoading(false);
      }
    };

    fetchClientInfo();
  }, []);

  const handleLogout = useCallback(async () => {
    await signOut({ redirect: false });
    // Clear all relevant localStorage items in one go
    ["otpRequested", "phoneOtpRequested"].forEach((item) =>
      localStorage.removeItem(item)
    );
    const path = basePath ? `/${basePath}` : `/`;
    router.push(path);
  }, [router, basePath]);

  const handleTokenExpiry = useCallback(() => {
    if (!accessToken) return;

    const decodedToken = jwtDecode<DecodedToken>(accessToken);

    if (!decodedToken?.exp) {
      return;
    }

    const expirationTime = decodedToken.exp * 1000;
    const currentTime = Date.now();
    const timeUntilExpiry = expirationTime - currentTime;

    // Clear any existing timer before setting a new one
    if (tokenExpiryTimerRef.current) {
      clearTimeout(tokenExpiryTimerRef.current);
      tokenExpiryTimerRef.current = null;
    }

    // If token is about to expire in less than 5 minutes, logout immediately
    if (timeUntilExpiry <= FIVE_MINUTES_MS) {
      handleLogout();
      return;
    }

    // Set timer to logout 5 minutes before token expiry
    const logoutTime = timeUntilExpiry - FIVE_MINUTES_MS;
    tokenExpiryTimerRef.current = setTimeout(handleLogout, logoutTime);
  }, [accessToken, handleLogout]);

  useEffect(() => {
    if (!accessToken) return;

    // Initialize idle timeout manager
    const idleManager = new IdleTimeoutManager({
      timeout: IDLE_TIMEOUT_MINUTES,
      onExpired: handleLogout,
    });

    // Check token expiry
    handleTokenExpiry();

    // Cleanup function
    return () => {
      idleManager.clear();
      if (tokenExpiryTimerRef.current) {
        clearTimeout(tokenExpiryTimerRef.current);
        tokenExpiryTimerRef.current = null;
      }
    };
  }, [accessToken, handleLogout, handleTokenExpiry]);

  // Only generate color scales if clientInfo is available
  const customColorScale =
    clientInfo && secondaryColor
      ? generateChakraScale(secondaryColor)
      : generateChakraScale("#3182CE");

  const customColorPrimaryScale =
    clientInfo && primaryColor
      ? generateChakraScale(primaryColor)
      : overrides.colors?.blue;

  const theme: ChakraProviderProps["theme"] = extendTheme(
    {
      ...overrides,
      colors: {
        customBrand:
          clientInfo && secondaryColor
            ? customColorScale
            : overrides.colors?.blue,
        customPrimary:
          clientInfo && primaryColor
            ? customColorPrimaryScale
            : overrides.colors?.blue,
      },
    },
    withDefaultColorScheme({
      colorScheme: clientInfo && secondaryColor ? "customBrand" : "blue",
    })
  );

  if (isLoading) return <FullPageLoader />;

  if (!clientInfo) return <MerchantNotFound />;

  return (
    <ThemeProvider theme={theme}>
      <Grid
        templateRows='auto 1fr auto'
        minH='100vh'
      >
        <Header
          accessToken={accessToken}
          aria-label='Site headers'
          clientInfo={clientInfo!}
        />

        <MainContent>{children}</MainContent>

        <Footer
          name={name!}
          clientContactInfo={contactDetails!}
          aria-label='Site footer'
        />
      </Grid>
    </ThemeProvider>
  );
};

const MainContent = ({ children }: { children: ReactNode }) => (
  <GridItem
    as='main'
    bg='#f2f2f2'
    role='main'
    w='100vw'
  >
    {children}
  </GridItem>
);

export default Layout;

const MerchantNotFound = () => {
  return (
    <Center
      bg='#f2f2f2'
      h='100vh'
      data-testid='merchant-not-found-wrapper'
    >
      <Flex
        w={{ base: "full", md: "xl" }}
        bg='white'
        borderRadius='lg'
        py={8}
        px={{ base: 6, md: 8 }}
        textAlign='center'
        flexDir='column'
        gap={4}
        data-testid='merchant-not-found-conatiner'
      >
        <Heading
          size='2xl'
          fontWeight='extrabold'
          as='h1'
          data-testid='merchant-not-found-title'
        >
          404
        </Heading>
        <Text fontWeight={800}>Merchant not found</Text>
        <Text>Please check the URL and try again.</Text>
      </Flex>
    </Center>
  );
};
