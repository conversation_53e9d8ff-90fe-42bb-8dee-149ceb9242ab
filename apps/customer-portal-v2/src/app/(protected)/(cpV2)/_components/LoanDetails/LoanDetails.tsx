"use client";
import {
  formatAmount,
  formatDate,
  getAttributrFromProduct,
  isAbsolutePath,
} from "@/utils/index";
import {
  Application,
  Portfolio,
  Repayment,
  Viewer,
} from "@/__generated/graphql";
import {
  Box,
  Button,
  Flex,
  Heading,
  Stack,
  StackDivider,
  Text,
  VStack,
  Link as ChakraLink,
} from "@chakra-ui/react";
import { CalendarDays } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import React from "react";

type LoanDetailsProps = {
  viewer: Viewer;
  termsAndConditionsUrl: string;
};

const LoanDetails = ({ viewer, termsAndConditionsUrl }: LoanDetailsProps) => {
  const params = useParams();
  const basePath = params?.basePath;
  const repayLoanUrl = basePath
    ? `/${basePath}/loans/repay-loan/`
    : `/loans/repay-loan/`;

  const data = viewer?.account?.portfolios?.nodes?.find(
    (status: any) => status.name !== "CLOSED"
  ) as Portfolio;

  const latestApplication = viewer?.account?.applications
    ?.nodes?.[0] as Application;

  const { repayments } = data || {};

  const nextInstallment: Repayment | undefined =
    repayments?.find((repayment) => repayment?.status?.name === "PENDING") ||
    undefined;

  const getPaymentStatusSummary = () => {
    const total = repayments?.length!;
    const paidCount = repayments?.filter(
      (repayment) => repayment?.status?.name === "PAID"
    ).length!;

    const percentage =
      total > 0 ? Number(((paidCount / total) * 100).toFixed(2)) : 0;

    return {
      count: `${paidCount}/${total}`,
      percentage: percentage,
      raw: {
        paid: paidCount,
        total: total,
        percentage: percentage,
      },
    };
  };

  const repaymentSummary = getPaymentStatusSummary();

  const namesSet = new Set(["noCardImplementation"]);
  const loanCategoryAttributes =
    latestApplication?.loanCategory?.loanCategoryAttributes!;
  const applicationAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const noCardImplementation = !!applicationAttributes?.noCardImplementation;

  const getDisplayInterestRate = () => {
    let interestRate: number;
    const { policy, amount } = data || {};

    if (policy.interestRate) {
      interestRate = policy?.interestRate?.value;
    } else if (
      policy?.graduatedLoanCycles &&
      policy.graduatedLoanCycles.length > 0
    ) {
      const gCycle = policy.graduatedLoanCycles.find(
        (cycle) => cycle.amount === amount
      );
      if (gCycle && gCycle.interestRate) {
        interestRate = gCycle.interestRate;
      }
    }
    return `${interestRate!}%` || "N/A";
  };

  const allRepaymentsPaid =
    repayments?.every((repayment) => repayment?.status?.name === "PAID") ??
    false;

  const shouldShowRepaymentButton = !noCardImplementation && !allRepaymentsPaid;
  return (
    <Box
      py={6}
      px={{ base: 4, md: 6 }}
      borderRadius='md'
      boxShadow='sm'
      maxW='8xl'
      bg='white'
      position='relative'
    >
      <Heading
        size='xs'
        textAlign='left'
        mb={4}
      >
        {data?.portfolioNumber}
      </Heading>

      <Stack
        width='full'
        direction={{ base: "column", lg: "row" }}
        px={{ base: 0, md: 4 }}
        pt={{ base: 0, md: 4 }}
        spacing={{ base: 12, lg: 20, xl: 28 }}
        alignItems='center'
      >
        <Stack
          width='full'
          align='flex-start'
          spacing={4}
          direction={{ base: "column" }}
          divider={<StackDivider py={2} />}
        >
          <Stack
            width='full'
            divider={<StackDivider />}
            direction={{ base: "column", sm: "row" }}
            spacing={4}
            gap={6}
          >
            <VStack
              spacing={4}
              width='full'
              align='flex-start'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Loan Amount
              </Text>
              <Text
                fontSize='xl'
                fontWeight='bold'
              >
                {formatAmount(data?.fullAmount!)}
              </Text>
            </VStack>
            <VStack
              spacing={4}
              width='full'
              align='flex-start'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Amount Paid Till Date
              </Text>
              <Flex
                alignItems={{ base: "flex-start", md: "center" }}
                gap={4}
                w='full'
                flexDirection={{
                  base: "column",
                  md: "row",
                }}
              >
                <Text
                  fontSize='xl'
                  fontWeight='bold'
                >
                  {formatAmount(data?.amountPaid!)}
                </Text>
                <Text
                  color='customPrimary.500'
                  fontWeight='bold'
                  whiteSpace='nowrap'
                >
                  {repaymentSummary?.count} paid
                </Text>
              </Flex>
            </VStack>
          </Stack>
          <Stack
            width='full'
            direction={{ base: "column", sm: "row" }}
            spacing={4}
            gap={6}
            alignItems='center'
          >
            <VStack
              spacing={4}
              width='full'
              align='flex-start'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Next Repayment
              </Text>
              <Text
                fontSize='xl'
                fontWeight='bold'
              >
                {nextInstallment?.outstandingPayment!
                  ? formatAmount(nextInstallment?.outstandingPayment!)
                  : "N/A"}
              </Text>
              <Flex
                alignItems='center'
                flex={1}
                minWidth={0}
                w='full'
                fontSize='sm'
                color='gray.600'
                gap={2}
              >
                <CalendarDays
                  width={16}
                  height={16}
                  color='black'
                />
                <Text>
                  Due Date: {formatDate({ date: nextInstallment?.dueDate! })}
                </Text>
              </Flex>
            </VStack>

            {shouldShowRepaymentButton && (
              <VStack
                spacing={4}
                width='full'
                align='flex-start'
              >
                <Button
                  as={Link}
                  href={repayLoanUrl}
                  colorScheme='customPrimary'
                  _hover={{
                    bg: "customPrimary.600",
                    textDecoration: "none",
                    color: "white",
                  }}
                  _visited={{
                    color: "white",
                  }}
                >
                  Make Repayment
                </Button>
              </VStack>
            )}
          </Stack>
        </Stack>

        <VStack
          width='full'
          align='flex-start'
          spacing={2}
          bg='customBrand.50'
          borderRadius={6}
          p={4}
        >
          <LoanSummaryItem
            label='You got this loan on'
            value={formatDate({
              date: data?.createdAt,
              dateFormat: "dd-MMM-yyyy",
            })}
          />
          <LoanSummaryItem
            label='Amount Disbursed'
            value={formatAmount(data?.amountDisbursed || 0)}
          />
          <LoanSummaryItem
            label='Interest Rate'
            value={getDisplayInterestRate()}
          />
          <LoanSummaryItem
            label='Number of Installments'
            value={data?.repayments?.length || 0}
          />
          <LoanSummaryItem
            label='Repayment Amount'
            value={formatAmount(data?.fullAmount || 0)}
          />
          <LoanSummaryItem
            label='Repayment Date'
            value={formatDate({
              date: data?.dateOfRepayment,
              dateFormat: "dd-MMM-yyyy",
            })}
          />

          <Button
            as={ChakraLink}
            isExternal
            href={isAbsolutePath(termsAndConditionsUrl)}
            w={"full"}
            bg='white'
            disabled={!termsAndConditionsUrl}
            colorScheme='customPrimary'
            borderColor='customPrimary.500'
            color='customPrimary.500'
            variant='outline'
            _hover={{
              bg: "customPrimary.100",
              color: "customPrimary.600",
              textDecoration: "none",
            }}
            _visited={{
              color: "customPrimary.500",
            }}
          >
            View Contract and Terms
          </Button>
        </VStack>
      </Stack>
    </Box>
  );
};
export default LoanDetails;

const LoanSummaryItem = ({
  label,
  value,
}: {
  label: string;
  value: React.ReactNode;
}) => (
  <Flex
    w='full'
    justifyContent='space-between'
    alignItems='center'
  >
    <Text minW='80px'>{label}</Text>
    <Text>{value}</Text>
  </Flex>
);
