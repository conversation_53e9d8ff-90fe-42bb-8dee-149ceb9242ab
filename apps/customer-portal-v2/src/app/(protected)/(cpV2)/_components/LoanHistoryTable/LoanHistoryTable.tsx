"use client";

import {
  <PERSON><PERSON>,
  AlertDescription,
  AlertIcon,
  Button,
  Flex,
  Stack,
  useMediaQuery,
} from "@chakra-ui/react";
import React, { useEffect, useRef, useCallback } from "react";
import {
  ListItem,
  ListItemSkeleton,
  ListRow,
  LoanApplicationsEmpty,
} from "./_components";

type LoanHistoryTableProps = {
  isLoading: boolean;
  isLoadingMore: boolean;
  isFiltered?: boolean;
  dataError?: string;
  dataCount: number;
  data: any;
  hasNextPage: boolean;
  onLoadMore: () => void;
};

const LoanHistoryTable = ({
  data,
  dataCount,
  isLoading,
  isLoadingMore,
  dataError,
  isFiltered,
  hasNextPage,
  onLoadMore,
}: LoanHistoryTableProps) => {
  const [isMeduimDevice] = useMediaQuery("(min-width: 1024px)");
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for infinite scroll
  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const target = entries[0];
      if (
        target.isIntersecting &&
        hasNextPage &&
        !isLoadingMore &&
        !isLoading
      ) {
        onLoadMore();
      }
    },
    [hasNextPage, isLoadingMore, isLoading, onLoadMore]
  );

  useEffect(() => {
    const observer = new IntersectionObserver(handleObserver, {
      threshold: 0.1,
      rootMargin: "100px",
    });

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [handleObserver]);

  return (
    <Stack
      spacing={0}
      flex={1}
      border='1px solid'
      borderColor='gray.200'
      borderRadius={6}
      mx={{ base: 4, md: 0 }}
    >
      <>
        {isMeduimDevice && (
          <ListRow isHeader>
            <Flex ml={{ base: 0, xl: 6 }}>Date</Flex>
            <Flex>Loan ID</Flex>
            <Flex ml={{ base: 0, xl: 8 }}>Loan Amount</Flex>
            <Flex>Repayment Amount</Flex>
            <Flex ml={{ base: 0, xl: 6 }}>Loan End Date</Flex>
            <Flex></Flex>
          </ListRow>
        )}

        <Stack spacing={0}>
          {isLoading ? (
            <Stack>
              {[...Array(5)].map((_, i) => (
                <ListItemSkeleton key={i} />
              ))}
            </Stack>
          ) : dataError ? (
            <Flex
              py={4}
              mx={8}
            >
              <Alert status='error'>
                <AlertIcon />
                <AlertDescription>{dataError}</AlertDescription>
              </Alert>
            </Flex>
          ) : !isLoading && dataCount === 0 ? (
            <LoanApplicationsEmpty isFiltered={isFiltered} />
          ) : (
            <>
              {data?.map((item: any, index: number) => (
                <ListItem
                  key={`${item?.node?.id}-${index}`}
                  node={item?.node}
                />
              ))}

              {/* Loading more indicator */}
              {isLoadingMore && (
                <Stack>
                  {[...Array(5)].map((_, i) => (
                    <ListItemSkeleton key={i} />
                  ))}
                </Stack>
              )}
            </>
          )}
          {/* Load more trigger element for intersection observer */}
          {hasNextPage && !isLoadingMore && (
            <div
              ref={loadMoreRef}
              style={{
                height: "20px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {/* Optional: Manual load more button as fallback */}
              <Button
                variant='ghost'
                size='sm'
                onClick={onLoadMore}
                disabled={isLoadingMore}
                _hover={{ bg: "gray.50" }}
              >
                Load More
              </Button>
            </div>
          )}

          {/* End of results indicator */}
          {!hasNextPage && data?.length > 0 && (
            <Flex
              justify='center'
              align='center'
              py={4}
              borderTop='1px solid'
              borderColor='gray.200'
              color='gray.500'
              fontSize='sm'
            >
              No more results to load
            </Flex>
          )}
        </Stack>
      </>
    </Stack>
  );
};

export default LoanHistoryTable;
