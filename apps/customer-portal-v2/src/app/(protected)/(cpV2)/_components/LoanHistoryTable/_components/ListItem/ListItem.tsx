"use client";
import { formatAmount } from "@/utils/index";
import {
  Application,
  ApplicationStatusEnum,
  PortfolioStatusEnum,
} from "@/__generated/graphql";
import {
  Badge,
  Flex,
  Text,
  ThemeTypings,
  Tooltip,
  useDisclosure,
  useMediaQuery,
} from "@chakra-ui/react";
import { format } from "date-fns";
import { useParams } from "next/navigation";
import React from "react";
import { ListRow } from "../ListRow";

type ListItemProps = {
  node: any;
};
const ListItem = ({ node }: ListItemProps) => {
  const params = useParams();
  const basePath = params?.basePath;

  const { onOpen, onClose } = useDisclosure();
  const [isSmallDevice] = useMediaQuery("(max-width: 768px)");

  const data: Application = node;
  const loanCategory = data?.loanCategory;

  const status = data?.status?.name;

  const tabNameByStatus = () => {
    if (!data?.portfolio?.portfolioNumber) {
      if (status !== ApplicationStatusEnum.Denied) {
        const firstTab = loanCategory?.products?.[0]?.applicationForm.filter(
          (tab: any) =>
            !tab?.linkedToOption &&
            !tab?.builders.some(
              (builder: { title: string }) =>
                builder?.title === "Card" || builder?.title === "Bank Info"
            )
        )[0];

        return firstTab
          ? firstTab?.name.trim().toLowerCase().replace(/\s+/g, "-")
          : "payment-options";
      } else {
        return "";
      }
    }
  };

  const tabName = tabNameByStatus();

  const url = basePath
    ? `/${basePath}/${data?.portfolio?.portfolioNumber ? "loans" : "applications"}/${data?.portfolio?.portfolioNumber || data?.applicationNumber}/${data?.portfolio?.portfolioNumber ? "repayment-schedule" : tabName}`
    : `/${data?.portfolio?.portfolioNumber ? "loans" : "applications"}/${data?.portfolio?.portfolioNumber || data?.applicationNumber}/${data?.portfolio?.portfolioNumber ? "repayment-schedule" : tabName}`;

  const renderStatus = (
    status: string
  ): { label: string; color: ThemeTypings["colorSchemes"] } => {
    switch (status) {
      case PortfolioStatusEnum.Disbursed:
        return { label: "Active", color: "green" };
      case PortfolioStatusEnum.Disbursing:
        return { label: "Disbursing", color: "yellow" };
      case PortfolioStatusEnum.PendingDisbursement:
        return { label: "Pending Disbursement", color: "blue" };
      case PortfolioStatusEnum.PendingManualDisbursement:
        return { label: "Pending Disbursement", color: "blue" };
      case PortfolioStatusEnum.Overdue:
        return { label: "Overdue", color: "orange" };
      case PortfolioStatusEnum.Closed:
        return { label: "Closed", color: "gray" };
      case PortfolioStatusEnum.OnHold:
        return { label: "On Hold", color: "pink" };
      case ApplicationStatusEnum.Approved:
        return { label: "Approved", color: "green" };
      case ApplicationStatusEnum.Denied:
        return { label: "Denied", color: "red" };
      case ApplicationStatusEnum.Abandoned:
        return { label: "Abandoned", color: "purple" };
      case ApplicationStatusEnum.UnderReview:
        return { label: "Under Review", color: "orange" };
      case ApplicationStatusEnum.AwaitingFeedback:
        return { label: "Awaiting Feedback", color: "blue" };
      case ApplicationStatusEnum.Incomplete:
        return { label: "Incomplete", color: "yellow" };
      default:
        return { label: "Pending", color: "blue" };
    }
  };

  return (
    <Flex
      onMouseEnter={onOpen}
      onMouseLeave={onClose}
    >
      <ListRow to={url}>
        {/* Date */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          ml={{ base: 0, xl: 6 }}
          _before={
            isSmallDevice
              ? {
                  content: "'Date'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>
            {(data?.createdAt && format(data?.createdAt, "MMM d, yyyy")) ||
              "N/A"}
          </Text>
        </Flex>

        {/* Loan ID */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Loan ID'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Tooltip
            label={
              data?.portfolio?.portfolioNumber ||
              data?.applicationNumber ||
              "N/A"
            }
            hasArrow
          >
            <Text
              isTruncated
              maxW={{ md: "150px", xl: "max-content" }}
            >
              {data?.portfolio?.portfolioNumber ||
                data?.applicationNumber ||
                "N/A"}
            </Text>
          </Tooltip>
        </Flex>

        {/* Loan Amount */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          ml={{ base: 0, xl: 8 }}
          _before={
            isSmallDevice
              ? {
                  content: "'Loan Amount'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>{formatAmount(data?.baseAmount)}</Text>
        </Flex>

        {/* Repayment Amount */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Repayment Amount'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>{formatAmount(data?.fullAmount)}</Text>
        </Flex>

        {/* Loan End Date */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          ml={{ base: 0, xl: 6 }}
          _before={
            isSmallDevice
              ? {
                  content: "'Loan End Date'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>
            {(data?.dateOfRepayment &&
              format(data?.dateOfRepayment, "MMM d, yyyy")) ||
              "N/A"}
          </Text>
        </Flex>

        {/* Status */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Status'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Badge
            h={6}
            px={2}
            fontSize='xs'
            w='fit-content'
            display='flex'
            variant='solid'
            textAlign='center'
            alignItems='center'
            borderRadius={6}
            justifyContent='center'
            color={`${
              renderStatus(
                data?.portfolio
                  ? data?.portfolio?.status?.name
                  : data?.displayStatus?.name
              )?.color
            }.500`}
            bg={`${
              renderStatus(
                data?.portfolio
                  ? data?.portfolio?.status?.name
                  : data?.displayStatus?.name
              )?.color
            }.100`}
          >
            {
              renderStatus(
                data?.portfolio
                  ? data?.portfolio?.status?.name
                  : data?.displayStatus?.name
              )?.label
            }
          </Badge>
        </Flex>
      </ListRow>
    </Flex>
  );
};
export default ListItem;
