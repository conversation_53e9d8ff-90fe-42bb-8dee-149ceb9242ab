"use client";
import { Flex, Grid } from "@chakra-ui/react";
import React, { ReactNode } from "react";

type ListRowProps = {
  to?: string;
  children: ReactNode;
  isHeader?: boolean;
  handleClick?: () => void;
};

const ListRow = ({ to, children, isHeader, handleClick }: ListRowProps) => {
  return (
    <Flex
      w='100%'
      alignItems='center'
      as={to ? "a" : undefined}
      href={to ?? undefined}
      style={{ textDecoration: "none" }}
      onClick={handleClick}
      cursor={!isHeader ? "pointer" : undefined}
    >
      <Grid
        p={{ base: 2, md: 4 }}
        flex={1}
        pos='relative'
        color={isHeader ? "gray.500" : "gray.700"}
        bg={isHeader ? "#D6DDEB80" : "white"}
        alignItems='center'
        textDecoration='none'
        wordBreak='break-all'
        gap={{ base: 4, lg: 0 }}
        borderBottom='1px solid'
        borderColor='gray.200'
        transitionDuration='slow'
        style={{ textDecoration: "none" }}
        fontWeight={isHeader ? "medium" : "normal"}
        templateColumns={{
          lg: "0.7fr 1.05fr 0.8fr repeat(2, 1fr) 0.6fr",
          xl: "repeat(auto-fit, minmax(100px, 1fr))",
        }}
        _hover={
          to || handleClick
            ? {
                bg: "gray.50",
                color: "gray.600",
              }
            : undefined
        }
      >
        {children}
      </Grid>
    </Flex>
  );
};
export default ListRow;
