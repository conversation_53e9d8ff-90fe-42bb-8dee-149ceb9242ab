"use client";
import { Flex, Text } from "@chakra-ui/react";
import { FileX } from "lucide-react";
import React from "react";

type LoanApplicationsEmptyProps = {
  isFiltered?: boolean;
};

const LoanApplicationsEmpty = ({ isFiltered }: LoanApplicationsEmptyProps) => {
  return (
    <Flex
      p={8}
      gap={4}
      flex={1}
      flexDir='column'
      alignItems='center'
      justifyContent='center'
      color='gray.400'
    >
      {isFiltered ? (
        <>
          {" "}
          <FileX
            width='60px'
            height='60px'
          />
          <Text
            fontSize='lg'
            textAlign='center'
            color='gray.600'
          >
            No data match your search criteria.
          </Text>
        </>
      ) : (
        <>
          <FileX
            width='60px'
            height='60px'
          />
          <Text
            fontSize='lg'
            textAlign='center'
            color='gray.600'
          >
            You currently have no data.
          </Text>
        </>
      )}
    </Flex>
  );
};

export default LoanApplicationsEmpty;
