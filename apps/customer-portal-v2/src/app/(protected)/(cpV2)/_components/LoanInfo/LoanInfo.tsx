"use client";
import { formatAmount, getAttributrFromProduct } from "@/utils/index";
import {
  Application,
  Portfolio,
  Repayment,
  Viewer,
} from "@/__generated/graphql";
import {
  Box,
  Button,
  Flex,
  Heading,
  Progress,
  Stack,
  StackDivider,
  Text,
  VStack,
} from "@chakra-ui/react";
import { format } from "date-fns";
import { CalendarDays } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import React from "react";
import { MessageOverlay } from "./_components";

type LoanInfoProps = {
  viewer: Viewer;
  redirectLink: string;
};

const LoanInfo = ({ viewer, redirectLink }: LoanInfoProps) => {
  const params = useParams();
  const basePath = params?.basePath;
  const repayLoanUrl = basePath
    ? `/${basePath}/loans/repay-loan`
    : `/loans/repay-loan`;

  const data = viewer?.account?.portfolios?.nodes?.find(
    (status: any) => status.name !== "CLOSED"
  ) as Portfolio;

  const latestApplication = viewer?.account?.applications
    ?.nodes?.[0] as Application;

  const { repayments } = data || {};

  const nextInstallment: Repayment | undefined =
    repayments?.find((repayment) => repayment?.status?.name === "PENDING") ||
    undefined;

  const getPaymentStatusSummary = () => {
    const total = repayments?.length!;
    const paidCount = repayments?.filter(
      (repayment) => repayment?.status?.name === "PAID"
    ).length!;

    const percentage =
      total > 0 ? Number(((paidCount / total) * 100).toFixed(2)) : 0;

    return {
      count: `${paidCount}/${total}`,
      percentage: percentage,
      raw: {
        paid: paidCount,
        total: total,
        percentage: percentage,
      },
    };
  };

  const repaymentSummary = getPaymentStatusSummary();

  const namesSet = new Set(["noCardImplementation"]);
  const loanCategoryAttributes =
    latestApplication?.loanCategory?.loanCategoryAttributes!;
  const applicationAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const noCardImplementation = !!applicationAttributes?.noCardImplementation;

  return (
    <Box
      py={6}
      px={{ base: 4, md: 6 }}
      borderRadius='md'
      boxShadow='sm'
      w='full'
      bg='white'
      position='relative'
    >
      <MessageOverlay
        application={latestApplication}
        redirectLink={redirectLink}
      />

      <Heading
        size='xs'
        mb={4}
        textAlign='left'
      >
        Active Loan
      </Heading>

      <Stack
        width='100%'
        spacing={{ base: 12, lg: 20 }}
        divider={<StackDivider />}
        direction={{ base: "column", lg: "row" }}
        p={{ base: 0, md: 6 }}
      >
        <VStack
          width='full'
          align='flex-start'
          spacing={4}
        >
          <Stack
            width='full'
            divider={<StackDivider />}
            direction={{ base: "column", sm: "row" }}
            spacing={4}
            gap={6}
          >
            <VStack
              spacing={4}
              width='full'
              align='flex-start'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Loan Amount
              </Text>
              <Text
                fontSize='xl'
                fontWeight='bold'
              >
                {formatAmount(
                  !latestApplication?.portfolio!
                    ? latestApplication?.fullAmount!
                    : data?.fullAmount!
                )}
              </Text>
            </VStack>
            <VStack
              spacing={4}
              width='full'
              align='flex-start'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Amount Paid Till Date
              </Text>
              <Text
                fontSize='xl'
                fontWeight='bold'
              >
                {formatAmount(
                  !latestApplication?.portfolio?.amountPaid!
                    ? 0
                    : data?.amountPaid!
                )}
              </Text>
            </VStack>
          </Stack>
          <Flex
            align='center'
            gap={4}
            flex={1}
            minWidth={0}
            w='full'
            mt={4}
          >
            <Progress
              value={
                !latestApplication?.portfolio!
                  ? 0
                  : repaymentSummary?.percentage
              }
              w='full'
              colorScheme='customPrimary'
            />
            <Text
              whiteSpace='nowrap'
              fontWeight='bold'
            >
              {latestApplication?.portfolio! ? repaymentSummary?.count : "N/A"}
            </Text>
          </Flex>
        </VStack>

        <VStack
          width='full'
          align='flex-start'
          spacing={4}
        >
          <Stack
            width='full'
            direction={{ base: "column", sm: "row" }}
            spacing={4}
            gap={6}
            alignItems='center'
          >
            <VStack
              spacing={4}
              width='full'
              align='flex-start'
            >
              <Text
                fontSize='sm'
                color='gray.400'
              >
                Next Repayment
              </Text>
              <Text
                fontSize='xl'
                fontWeight='bold'
              >
                {nextInstallment?.outstandingPayment!
                  ? formatAmount(nextInstallment?.outstandingPayment!)
                  : "N/A"}
              </Text>
            </VStack>
            {!noCardImplementation ? (
              <VStack
                spacing={4}
                width='full'
                align='flex-start'
              >
                <Button
                  as={Link}
                  href={repayLoanUrl}
                  colorScheme='customPrimary'
                  disabled={!latestApplication?.portfolio!}
                  _hover={{
                    bg: "customPrimary.600",
                    textDecoration: "none",
                    color: "white",
                  }}
                  _visited={{
                    color: "white",
                  }}
                >
                  Make Repayment
                </Button>
              </VStack>
            ) : null}
          </Stack>
          <Flex
            alignItems='center'
            flex={1}
            minWidth={0}
            w='full'
            fontSize='sm'
            color='gray.600'
            gap={2}
            mt={4}
          >
            <CalendarDays
              width={16}
              height={16}
              color='black'
            />
            <Text>
              Due Date:{" "}
              {nextInstallment?.dueDate &&
                format(nextInstallment?.dueDate, "dd-MMMM-yyyy")}
            </Text>
          </Flex>
        </VStack>
      </Stack>
    </Box>
  );
};

export default LoanInfo;
