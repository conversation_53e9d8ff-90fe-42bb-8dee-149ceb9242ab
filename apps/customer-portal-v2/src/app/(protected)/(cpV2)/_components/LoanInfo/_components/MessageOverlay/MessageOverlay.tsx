"use client";

import React, { useMemo } from "react";
import {
  Badge,
  Box,
  Button,
  Center,
  Flex,
  Text,
  ThemeTypings,
} from "@chakra-ui/react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { Application, ApplicationStatusEnum } from "@/__generated/graphql";
import { getAttributrFromProduct } from "@/utils/index";
import { APPLICATION_STEPS } from "@/utils/constants";

type MessageOverlayProps = {
  application: Application;
  redirectLink: string;
};

const MessageOverlay = ({ application, redirectLink }: MessageOverlayProps) => {
  const { basePath } = useParams() || {};
  const { account, loanCategory, status, completedSteps, portfolio } =
    application || {};

  const loanCategoryAttributes = loanCategory?.loanCategoryAttributes!;
  const paymentOptionsUrl = basePath
    ? `/${basePath}/applications/${application?.applicationNumber}/payment-options`
    : `/applications/${application?.applicationNumber}/payment-options`;

  const applicationAttributes: {
    [key: string]: string | string[] | undefined;
  } = useMemo(
    () => getAttributrFromProduct({ namesSet, loanCategoryAttributes }),
    [loanCategoryAttributes]
  );

  const currentStatus = useMemo(
    () => status?.name,
    [completedSteps, status?.name]
  );

  const { label: statusLabel, color: statusColor } = useMemo(
    () => STATUS_MESSAGES[currentStatus] || STATUS_MESSAGES.DEFAULT,
    [currentStatus]
  );

  const isCompletedApplication = application?.completedSteps?.includes(
    APPLICATION_STEPS.completeApplication.name
  )!;

  const isPendingAddCard = useMemo(
    () =>
      Boolean(
        completedSteps?.includes(APPLICATION_STEPS.completeApplication.name)
      ) &&
      !account?.cards?.length &&
      applicationAttributes?.allowCardSkipDuringApplication,
    [completedSteps, account?.cards, applicationAttributes]
  );

  if (!application) return null;

  return (
    <>
      {portfolio?.portfolioNumber ? null : (
        <Box
          borderRadius='md'
          maxW='8xl'
          position='absolute'
          bg='whiteAlpha.800'
          top={0}
          left={0}
          w='full'
          h='full'
          zIndex={9}
          p={{ base: 4, md: 8 }}
        >
          <Center
            h='full'
            w='full'
            position='relative'
          >
            <Badge
              p={4}
              borderRadius={6}
              border='1px solid'
              borderColor={
                !isCompletedApplication || isPendingAddCard
                  ? "customPrimary.500"
                  : `${statusColor}.500`
              }
              variant='subtle'
              w={{ base: "100%", md: "max-content" }}
              textAlign='center'
              color={
                !isCompletedApplication || isPendingAddCard
                  ? "customPrimary.500"
                  : `${statusColor}.500`
              }
              bg={
                !isCompletedApplication || isPendingAddCard
                  ? "customPrimary.50"
                  : `${statusColor}.50`
              }
            >
              {!isCompletedApplication ? (
                <MessageButton
                  href={redirectLink}
                  label='Your application is incomplete. Click to continue your application'
                  buttonText='Continue'
                />
              ) : isPendingAddCard ? (
                <MessageButton
                  href={paymentOptionsUrl}
                  label='Pending addition of a debit card. Click to add card'
                  buttonText='Add Card'
                />
              ) : currentStatus !== ApplicationStatusEnum.Approved ? (
                <Text
                  fontSize='lg'
                  fontWeight='semibold'
                  noOfLines={{ base: 3, md: 1 }}
                  whiteSpace='normal'
                  textAlign={{ base: "center", md: "left" }}
                >
                  {statusLabel}
                </Text>
              ) : null}
            </Badge>
          </Center>
        </Box>
      )}
    </>
  );
};

export default MessageOverlay;

const STATUS_MESSAGES: Record<
  string,
  { label: string; color: ThemeTypings["colorSchemes"] }
> = {
  [ApplicationStatusEnum.Approved]: { label: "Approved", color: "green" },
  [ApplicationStatusEnum.Denied]: { label: "Denied", color: "red" },
  [ApplicationStatusEnum.Abandoned]: { label: "Abandoned", color: "purple" },
  [ApplicationStatusEnum.UnderReview]: {
    label: "Under Review",
    color: "orange",
  },
  [ApplicationStatusEnum.AwaitingFeedback]: {
    label: "Awaiting Feedback",
    color: "blue",
  },
  DEFAULT: { label: "PROCESSING", color: "blue" },
};

const namesSet = new Set([
  "requiresBankStatement",
  "requiresBankStatementUpload",
  "allowCardSkipDuringApplication",
  "noCardImplementation",
  "requiresPhoneVerification",
]);

type MessageButtonProps = {
  href: string;
  label: string;
  buttonText: string;
};
const MessageButton = ({ href, label, buttonText }: MessageButtonProps) => (
  <Flex
    justify='center'
    alignItems='center'
    flexDirection={{ base: "column", md: "row" }}
    gap={{ base: 4, md: 2 }}
    textTransform='capitalize'
  >
    <Text
      fontSize='md'
      fontWeight='semibold'
      noOfLines={{ base: 3, md: 1 }}
      whiteSpace='normal'
      color='customPrimary.500'
      textAlign={{ base: "center", md: "left" }}
    >
      {label}
    </Text>
    <Button
      as={Link}
      w='fit-content'
      colorScheme='customPrimary'
      fontSize='sm'
      fontWeight='semibold'
      prefetch={false}
      h={0}
      py={4}
      px={2}
      passHref
      href={href}
      _hover={{
        color: "white",
        textDecoration: "none",
        bg: "customPrimary.600",
      }}
      _visited={{ color: "white" }}
    >
      {buttonText}
    </Button>
  </Flex>
);
