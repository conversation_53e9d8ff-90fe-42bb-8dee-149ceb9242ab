"use client";

import {
  Box,
  Flex,
  Heading,
  Text,
  VStack,
  Alert,
  AlertIcon,
  AlertDescription,
} from "@chakra-ui/react";
import {
  AmountEligibilityPayload,
  ApplicableTenorsResponse,
  ClientInfo,
  LoanCategory,
  LoanCategoryAttribute,
  Maybe,
  Policy,
  PolicyDurationType,
} from "src/__generated__/graphql";
import {
  LoanCategoryList,
  SearchLoanCategory,
  SelectedCategory,
} from "./_components";
import { useEffect, useState } from "react";
import { useLoanInitiation } from "@/src/services";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { FullPageLoader } from "@/components/FullPageLoader";
import { useParams } from "next/navigation";

type Props = {
  data: any;
  error: any;
  loanInitiationData: ({
    amount,
    selectedCategoryId,
  }: {
    selectedCategoryId?: string | undefined;
    amount?: number | undefined;
  }) => Promise<{
    applicableTenor: ApplicableTenorsResponse[];
    eligibleAmount: AmountEligibilityPayload[];
    initiationError: {
      eligibleAmountError: string;
      applicableTenorError: string;
    };
  }>;
};

const LoanInitiation = ({ data, error, loanInitiationData }: Props) => {
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const categories = data || [];
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;
  const { selectedCategoryId } = useLoanInitiation();

  const [filteredCategories, setFilteredCategories] = useState(categories);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      window.scrollTo(0, 0);
    }
  }, []);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  if (!clientInfo) return <FullPageLoader />;
  return (
    <Box
      w={{ base: "100vw", md: "2xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <VStack w='100%'>
        <Box
          textAlign='center'
          my={8}
        >
          <Heading
            size='lg'
            data-testid='loan-initiation-heading'
          >
            Select a Loan Product
          </Heading>
          <Text>
            Please select a loan product below to continue your application
          </Text>
        </Box>

        {error && (
          <Alert
            status='error'
            borderRadius={6}
            data-testid='loan-initiation-error'
          >
            <AlertIcon />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Flex
          justifyContent='left'
          alignItems='flex-start'
          flexDir='column'
          gap={4}
          w='100%'
        >
          {selectedCategoryId ? (
            <>
              {filteredCategories
                ?.filter(
                  (category: { id: string }) =>
                    category?.id === selectedCategoryId
                )
                ?.map(
                  (category: {
                    id: any;
                    createdAt?: any;
                    description?: Maybe<string> | undefined;
                    durationType?: Maybe<PolicyDurationType> | undefined;
                    loanCategoryAttributes?:
                      | Maybe<Maybe<LoanCategoryAttribute>[]>
                      | undefined;
                    maxAmount?: Maybe<number> | undefined;
                    maxDuration?: Maybe<number> | undefined;
                    minAmount?: Maybe<number> | undefined;
                    minDuration?: Maybe<number> | undefined;
                    name?: string;
                    products?: Maybe<Policy[]> | undefined;
                    status?: Maybe<string> | undefined;
                    updatedAt?: any;
                  }) => (
                    <SelectedCategory
                      category={category as LoanCategory}
                      key={category?.id}
                      loanInitiationData={loanInitiationData}
                      clientData={clientInfo}
                    />
                  )
                )}
            </>
          ) : categories?.length > 0 ? (
            <>
              <SearchLoanCategory
                categories={categories}
                setFilteredCategories={setFilteredCategories}
                setLoading={setIsLoading}
                filteredCategories={filteredCategories}
                loading={isLoading}
              />
              <VStack
                as='div'
                gap={4}
                display='flex'
                flexDirection='column'
                maxH='500px'
                overflowY='auto'
                border='1px solid'
                borderColor='gray.200'
                p={4}
                borderRadius={6}
                w='full'
                data-testid='loan-category-list'
              >
                {filteredCategories.map(
                  (category: {
                    id: any;
                    createdAt?: any;
                    description?: Maybe<string> | undefined;
                    durationType?: Maybe<PolicyDurationType> | undefined;
                    loanCategoryAttributes?:
                      | Maybe<Maybe<LoanCategoryAttribute>[]>
                      | undefined;
                    maxAmount?: Maybe<number> | undefined;
                    maxDuration?: Maybe<number> | undefined;
                    minAmount?: Maybe<number> | undefined;
                    minDuration?: Maybe<number> | undefined;
                    name?: string;
                    products?: Maybe<Policy[]> | undefined;
                    status?: Maybe<string> | undefined;
                    updatedAt?: any;
                  }) => (
                    <LoanCategoryList
                      category={category as LoanCategory}
                      key={category?.id}
                      loading={isLoading}
                    />
                  )
                )}
              </VStack>
            </>
          ) : (
            <Alert
              status='info'
              borderRadius={6}
              data-testid='loan-initiation-no-products'
            >
              <AlertIcon />
              <AlertDescription>No loan products yet.</AlertDescription>
            </Alert>
          )}
        </Flex>
      </VStack>
    </Box>
  );
};

export default LoanInitiation;
