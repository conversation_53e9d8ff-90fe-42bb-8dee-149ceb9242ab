"use client";
import { useLoanInitiation } from "@/src/services";
import {
  Box,
  Button,
  FormControl,
  Heading,
  VStack,
  Alert,
  AlertIcon,
  AlertDescription,
} from "@chakra-ui/react";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { NumericInput, Select } from "src/components";
import { formatAmount, parseNumericValue } from "src/utils";
import { LoanCategory } from "src/__generated__/graphql";

type Props = {
  category: LoanCategory | undefined | null;
};

const LoanAmount = ({ category }: Props) => {
  const {
    handleAmount,
    eligibleAmount,
    isLoanInitiationError,
    isLoanInitiationLoading,
    initiationLoading,
    setShowTenor,
    setLoanTenor,
  } = useLoanInitiation();

  const eligibleAmountErrors = isLoanInitiationError?.amountError;

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const onSubmit = (data: any) => {
    handleAmount(data?.amount);
    setShowTenor(true);
  };

  const amount = watch("amount");

  useEffect(() => {
    if (amount) {
      setShowTenor(false);
      setLoanTenor([]);
    }
  }, [amount]);

  return (
    <VStack w='100%'>
      <Box
        textAlign='center'
        my={8}
      >
        <Heading size='lg'
          data-testid='loan-amount-heading'
        >How much do you want to borrow?</Heading>
      </Box>

      {eligibleAmountErrors && (
        <Alert
          status='error'
          borderRadius={6}
          data-testid='loan-amount-error-alert'
        >
          <AlertIcon />
          <AlertDescription>{eligibleAmountErrors}</AlertDescription>
        </Alert>
      )}

      <FormControl
        as='form'
        onSubmit={handleSubmit(onSubmit)}
      >
        {eligibleAmount?.length ? (
          <Controller
            name='amount'
            control={control}
            render={({ field }) => (
              <Select
                {...field}
                value={field.value}
                label='Amount'
                onChange={(e) => {
                  const selectedValue = parseNumericValue(e.target.value);
                  field.onChange(selectedValue);
                }}
                placeholder='Select loan amount'
                isLoading={isLoanInitiationLoading && !amount}
                autoFocus
                isDisabled={initiationLoading}
                borderRadius={6}
                data-testid="loan-amount-select"
              >
                {eligibleAmount?.map((data, idx) => (
                  <option
                    key={idx}
                    value={data?.amount}
                  >
                    {formatAmount(Number(data?.amount) || 0)}
                  </option>
                ))}
              </Select>
            )}
          />
        ) : (
          <>
            <Controller
              name='amount'
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Please enter a value",
                },
                validate: (value) => {
                  const numericValue = parseNumericValue(value as string);
                  if (isNaN(numericValue) || numericValue === 0) {
                    return "Please enter a valid number";
                  }

                  if (!category) return "Category not loaded";

                  if (numericValue < (category.minAmount ?? 0)) {
                    return `Minimum loan amount is ${formatAmount(category.minAmount || 0)}`;
                  }
                  if (numericValue > (category.maxAmount ?? Infinity)) {
                    return `Maximum loan amount is ${formatAmount(category.maxAmount || Infinity)}`;
                  }
                  return true;
                },
              }}
              render={({ field }) => (
                <NumericInput
                  {...field}
                  label='Amount'
                  isAmountInput
                  errorMessage={errors?.amount?.message as string | undefined}
                  isInvalid={!!errors.amount}
                  onChange={(e) => {
                    const numericValue = parseNumericValue(e.target.value);
                    field.onChange(numericValue);
                  }}
                  helperText={
                    category &&
                    `Loan range from ${formatAmount(category.minAmount || 0)} up to ${formatAmount(category.maxAmount || 0)}`
                  }
                  isLoading={isLoanInitiationLoading && !amount}
                  disabled={initiationLoading}
                  data-testid="loan-amount-input"
                />
              )}
            />
          </>
        )}

        <Button
          type='submit'
          my={4}
          disabled={initiationLoading}
          data-testid='loan-amount-submit-button'
        >
          Continue
        </Button>
      </FormControl>
    </VStack>
  );
};

export default LoanAmount;
