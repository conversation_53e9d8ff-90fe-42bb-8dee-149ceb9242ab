"use client";
import { Flex, Radio, RadioGroup, Stack, Text } from "@chakra-ui/react";
import { Controller, useForm } from "react-hook-form";
import { LoanCategory, Maybe } from "src/__generated__/graphql";
import { formatAmount } from "src/utils";
import { useLoanInitiation } from "@/src/services";

type Props = {
  category: Maybe<LoanCategory>;
  loading: boolean;
  isAgent?: boolean;
};
const LoanCategoryList = ({ category, loading, isAgent }: Props) => {
  const { control } = useForm();
  const { selectedCategoryId, handleCategorySelect } = useLoanInitiation();

  return (
    <Controller
      name='categoryId'
      control={control}
      defaultValue=''
      render={({ field }) =>
        loading ? (
          <>{null}</>
        ) : (
          <RadioGroup
            {...field}
            onChange={(e) => {
              field.onChange(e);
              handleCategorySelect(e);
            }}
            border='1px solid'
            borderColor='gray.200'
            borderRadius={6}
            w='100%'
            lineHeight='18px'
            _hover={{
              borderColor: "customBrand.400",
            }}
            value={selectedCategoryId}
            data-testid='loan-category-item'
          >
            <Flex>
              <Radio
                value={category?.id}
                w='100%'
                p={4}
                justifyContent='space-between'
                flexDirection='row-reverse'
                isRequired
              >
                <Flex width='100%'>
                  <Stack lineHeight=''>
                    <Text
                      textTransform='capitalize'
                      fontWeight='bold'
                    >
                      {category?.name}
                    </Text>
                    <Text>{category?.description}</Text>
                    <Text>{`Min: ${formatAmount(category?.minAmount || 0)}`}</Text>
                    <Text>{`Max: ${formatAmount(category?.maxAmount || 0)}`}</Text>
                  </Stack>
                </Flex>
              </Radio>
            </Flex>
          </RadioGroup>
        )
      }
    />
  );
};

export default LoanCategoryList;
