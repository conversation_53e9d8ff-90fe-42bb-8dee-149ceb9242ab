"use client";

import { useLoanInitiation } from "@/src/services";
import { formatLoanDuration } from "@/utils/index";
import {
  Box,
  FormControl,
  Heading,
  VStack,
  Alert,
  AlertIcon,
  AlertDescription,
} from "@chakra-ui/react";
import { useEffect } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { Select } from "src/components";

const LoanTenor = () => {
  const {
    control,
    trigger,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const {
    isLoanInitiationError,
    isLoanInitiationLoading,
    loanTenor: applicableTenor,
    setTenor,
    initiationLoading,
    validateCardDuration,
  } = useLoanInitiation();

  const applicableTenorError = isLoanInitiationError?.tenorError;

  const tenorValue = useWatch({
    control,
    name: "tenor",
  });

  useEffect(() => {
    if (tenorValue && typeof window !== "undefined") {
      trigger("tenor");
      window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [tenorValue, trigger]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: "smooth",
      });
    }
  }, []);

  return (
    <VStack w='100%'>
      <Box
        textAlign='center'
        my={8}
      >
        <Heading
          size='lg'
          data-testid='loan-tenor-heading'
        >
          What tenor would you prefer?
        </Heading>
      </Box>

      {applicableTenorError && (
        <Alert
          status='error'
          borderRadius={6}
          data-testid='loan-tenor-error'
        >
          <AlertIcon />
          <AlertDescription>{applicableTenorError}</AlertDescription>
        </Alert>
      )}

      <FormControl as='form'>
        <Controller
          name='tenor'
          control={control}
          rules={{
            required: "Tenor is required",
            validate: (value: string) => {
              const validationResult = validateCardDuration(value);
              return validationResult === undefined ? true : validationResult;
            },
          }}
          render={({ field }) => (
            <Select
              {...field}
              placeholder='--Select Tenor--'
              isLoading={isLoanInitiationLoading}
              onChange={(e) => {
                const selectedValue = e.target.value;
                field.onChange(selectedValue);
                setTenor(selectedValue);
                trigger("tenor");
              }}
              isInvalid={!!errors.tenor}
              errorMessage={errors.tenor?.message as string | undefined}
              isDisabled={initiationLoading}
              borderRadius={6}
              data-testid='loan-tenor-select'
            >
              {applicableTenor?.map((data) => (
                <option
                  key={`${data?.duration}-${data?.durationType}`}
                  value={`${data?.duration} ${data?.durationType}`}
                >
                  {formatLoanDuration(
                    `${data?.duration} ${data?.durationType}`
                  )}
                </option>
              ))}
            </Select>
          )}
        />
      </FormControl>
    </VStack>
  );
};

export default LoanTenor;
