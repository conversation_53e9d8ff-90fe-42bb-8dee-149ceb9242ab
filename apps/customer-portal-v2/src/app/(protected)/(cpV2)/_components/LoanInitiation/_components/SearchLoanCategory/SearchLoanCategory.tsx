"use client";
import {
  Dispatch,
  SetStateAction,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import {
  FormControl,
  Input,
  InputGroup,
  InputLeftElement,
  Flex,
  Button,
  Skeleton,
  Alert,
  AlertIcon,
  AlertDescription,
} from "@chakra-ui/react";
import { Search } from "lucide-react";
import { useForm } from "react-hook-form";
import { debounce } from "lodash";

type Props = {
  categories: any[];
  setFilteredCategories: Dispatch<SetStateAction<any[]>>;
  filteredCategories: any[];
  setLoading: Dispatch<SetStateAction<boolean>>;
  loading: boolean;
};

const SearchLoanCategory = ({
  categories,
  setFilteredCategories,
  filteredCategories,
  setLoading,
  loading,
}: Props) => {
  const { register, watch, reset } = useForm();
  const searchQuery = watch("searchQuery") || "";

  const activeCategories = useMemo(
    () => categories.filter((category) => category?.status === "ACTIVE"),
    [categories]
  );

  const handleSearch = useCallback(
    debounce((query: string) => {
      if (query) {
        const filtered = activeCategories.filter((category) =>
          category?.name.toLowerCase().includes(query.toLowerCase())
        );
        setFilteredCategories(filtered);
      } else {
        setFilteredCategories(categories);
      }
      setLoading(false);
    }, 300),
    [activeCategories, categories, setFilteredCategories, setLoading]
  );

  useEffect(() => {
    setLoading(true);
    handleSearch(searchQuery.trim());

    return () => {
      handleSearch.cancel();
    };
  }, [searchQuery, handleSearch, setLoading]);

  const handleReset = useCallback(() => {
    reset({ searchQuery: "" });
    setFilteredCategories(categories);
  }, [reset, setFilteredCategories, categories]);

  const renderSkeletons = useMemo(
    () =>
      Array(6)
        .fill(0)
        .map((_item, idx) => (
          <Skeleton
            isLoaded={!loading}
            w='100%'
            height='50px'
            key={idx}
            h='110px'
            borderRadius={6}
          />
        )),
    [loading]
  );

  return (
    <Flex
      gap={4}
      w='100%'
      flexDirection='column'
    >
      <FormControl>
        <InputGroup>
          <InputLeftElement
            pointerEvents='none'
            color='gray.300'
          >
            <Search
              width={20}
              height={20}
            />
          </InputLeftElement>
          <Input
            {...register("searchQuery")}
            placeholder='Search for a loan product'
            borderRadius={6}
            data-testid='search-input'
          />
        </InputGroup>
      </FormControl>
      {loading ? (
        renderSkeletons
      ) : (
        <>
          {searchQuery && filteredCategories.length === 0 && (
            <Alert
              status='info'
              borderRadius={6}
              data-testid='search-no-results-alert'
            >
              <AlertIcon />
              <AlertDescription>
                {`No loan product matches your query.`}
                <Button
                  variant='link'
                  ml={1}
                  onClick={handleReset}
                  color='red.500'
                  data-testid='search-reset-alert-button'
                >
                  Reset
                </Button>
              </AlertDescription>
            </Alert>
          )}
          {searchQuery && filteredCategories.length > 0 && (
            <Button
              variant='link'
              ml={1}
              onClick={handleReset}
              color='red.500'
              w='fit-content'
              data-testid='search-reset-button'
            >
              Reset
            </Button>
          )}
        </>
      )}
    </Flex>
  );
};

export default SearchLoanCategory;
