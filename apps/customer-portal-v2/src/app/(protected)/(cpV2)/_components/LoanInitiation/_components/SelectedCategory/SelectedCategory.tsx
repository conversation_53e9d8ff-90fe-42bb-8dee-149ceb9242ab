"use client";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  ButtonGroup,
  Flex,
  Radio,
  RadioGroup,
  Text,
} from "@chakra-ui/react";
import {
  AmountEligibilityPayload,
  ApplicableTenorsResponse,
  ClientInfo,
  CustomerCreateApplicationInput,
  LoanCategory,
} from "src/__generated__/graphql";
import { formatAmount } from "src/utils";
import { LoanAmount } from "../LoanAmount";
import { LoanTenor } from "../LoanTenor";
import { useLoanInitiation } from "@/src/services";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { LoanInitiationForm } from "@/src/types/loanInitiationForm";
import { useParams, useRouter } from "next/navigation";
import { initiateApplication } from "@/src/app/actions";
import logger from "@/lib/logger";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";

type Props = {
  category: LoanCategory;
  loanInitiationData: ({
    amount,
    selectedCategoryId,
  }: {
    selectedCategoryId?: string | undefined;
    amount?: number | undefined;
  }) => Promise<{
    applicableTenor: ApplicableTenorsResponse[];
    eligibleAmount: AmountEligibilityPayload[];
    initiationError: {
      eligibleAmountError: string;
      applicableTenorError: string;
    };
  }>;
  clientData: ClientInfo | null;
};

const SelectedCategory = ({
  category,
  loanInitiationData,
  clientData,
}: Props) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath;

  const { error, setError } = useMessageTimer();
  const [isTenorValid, setIsTenorValid] = useState(true);

  const {
    selectedCategoryId,
    handleResetSelection,
    amount,
    setEligibleAmount,
    setIsLoanInitiationError,
    setIsLoanInitiationLoading,
    setLoanTenor,
    setInitiationLoading,
    initiationLoading,
    setShowTenor,
    showTenor,
    tenor,
    token,
    setTenor,
    validateCardDuration,
  } = useLoanInitiation();

  const getInitiationData = useCallback(async () => {
    try {
      setIsLoanInitiationLoading(true);
      const { eligibleAmount, applicableTenor, initiationError } =
        await loanInitiationData({
          selectedCategoryId,
          amount,
        });

      setEligibleAmount(eligibleAmount);
      setLoanTenor(applicableTenor);
      setIsLoanInitiationError({
        amountError: initiationError.eligibleAmountError,
        tenorError: initiationError.applicableTenorError,
      });
      setIsLoanInitiationLoading(false);
    } catch (error: any) {
      logger({ error });
      setIsLoanInitiationLoading(false);
    }
  }, [selectedCategoryId, amount]);

  const clientId = clientData?.clientId!;

  const form = useForm<LoanInitiationForm>({
    mode: "onChange",
  });
  const { reset } = form;

  useEffect(() => {
    if (selectedCategoryId || amount) {
      getInitiationData();
    }
  }, [selectedCategoryId, amount]);

  useEffect(() => {
    if (!showTenor) {
      setTenor("");
    }
  }, [showTenor]);

  useEffect(() => {
    if (tenor) {
      setIsTenorValid(validateCardDuration(tenor) === true);
    }
  }, [tenor]);

  const onSubmit = async () => {
    setError("");
    setShowTenor(true);
    const tenorValue = tenor?.split(" ");

    const durationType = () => {
      switch (tenorValue && tenorValue[1]) {
        case "days":
          return "DAILY";
        case "months":
          return "MONTHLY";
        case "weeks":
          return "WEEKLY";
        case "years":
          return "ANNUALLY";
        default:
          return "DAILY";
      }
    };
    const formData: CustomerCreateApplicationInput = {
      amount,
      loanCategoryId: selectedCategoryId,
      source: "WEB" as any,
      clientId,
      loanDuration: Number(tenorValue && tenorValue[0]),
      durationType: durationType() as any,
    };
    try {
      setInitiationLoading(true);
      const res = await initiateApplication({ formData, token });

      if (res?.data?.initiateApplication?.success) {
        const { applicationNumber } =
          res?.data?.initiateApplication?.application || {};

        const path = basePath
          ? `/${basePath}/application/${applicationNumber}/verify-email`
          : `/application/${applicationNumber}/verify-email`;

        router.push(path);
      }
      setInitiationLoading(false);
      if (res?.error) {
        setInitiationLoading(false);
        setError(res?.error);
      }
    } catch (error) {
      setInitiationLoading(false);
    }
  };
  return (
    <>
      {error && (
        <Alert
          status='error'
          borderRadius={6}
          data-testid='error-message-alert'
        >
          <AlertIcon />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <RadioGroup
        border='1px solid'
        borderColor='gray.200'
        borderRadius={6}
        w='100%'
        value={selectedCategoryId}
        data-testid='category-radio-group'
      >
        <Flex>
          <Radio
            w='100%'
            p={4}
            justifyContent='space-between'
            flexDirection='row-reverse'
            value={category?.id}
            isRequired
            isDisabled={initiationLoading}
          >
            <Box w='100%'>
              <Text
                textTransform='capitalize'
                fontWeight='bold'
              >
                {category?.name}
              </Text>
              <Text>{category?.description}</Text>
              <Text>{`Min: ${formatAmount(category?.minAmount || 0)}`}</Text>
              <Text>{`Max: ${formatAmount(category?.maxAmount || 0)}`}</Text>
            </Box>
          </Radio>
        </Flex>
      </RadioGroup>

      <Button
        onClick={() => {
          handleResetSelection();
          setShowTenor(false);
          reset();
        }}
        variant='link'
        colorScheme='customPrimary'
        _hover={{
          color: "customPrimary.600",
          textDecoration: "underline",
        }}
        disabled={initiationLoading}
        data-testid='change-loan-product-button'
      >
        Change Loan Product
      </Button>

      <LoanAmount category={category} />

      {amount && showTenor ? <LoanTenor /> : ""}

      {amount && selectedCategoryId && tenor && isTenorValid ? (
        <ButtonGroup
          justifyContent='flex-end'
          width='100%'
          my={8}
        >
          <Button
            width='100%'
            onClick={onSubmit}
            isDisabled={
              !amount || !selectedCategoryId || !tenor || !isTenorValid
            }
            isLoading={initiationLoading}
            data-testid='proceed-button'
          >
            Proceed
          </Button>
        </ButtonGroup>
      ) : (
        ""
      )}
    </>
  );
};

export default SelectedCategory;
