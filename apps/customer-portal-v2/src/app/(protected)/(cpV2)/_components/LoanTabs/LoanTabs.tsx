"use client";

import { Tabs, Tab<PERSON><PERSON>, Tab, Tab<PERSON>ane<PERSON>, Box, TabPanel } from "@chakra-ui/react";
import { useParams, useRouter, usePathname } from "next/navigation";
import React, { useEffect, useState, useRef, useCallback } from "react";

const LoanTabs = ({ children }: { children: React.ReactNode }) => {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const { id, basePath } = params || {};
  const tabListRef = useRef<HTMLDivElement>(null);
  const activeTabRef = useRef<HTMLButtonElement>(null);

  const [activeTabIndex, setActiveTabIndex] = useState(0);

  const repaymentUrl = basePath
    ? `/${basePath}/loans/${id}/repayment-schedule`
    : `/loans/${id}/repayment-schedule`;

  const transactionUrl = basePath
    ? `/${basePath}/loans/${id}/transactions`
    : `/loans/${id}/transactions`;

  const tabRoutes = [
    {
      path: repaymentUrl,
      label: "Repayment Schedule",
      segment: "repayment-schedule",
    },
    {
      path: transactionUrl,
      label: "Transactions",
      segment: "transactions",
    },
  ];

  const scrollToActiveTab = useCallback(() => {
    if (activeTabRef.current && tabListRef.current) {
      const tabList = tabListRef.current;
      const activeTab = activeTabRef.current;

      const tabListRect = tabList.getBoundingClientRect();
      const activeTabRect = activeTab.getBoundingClientRect();

      const scrollLeft =
        activeTab.offsetLeft - tabListRect.width / 2 + activeTabRect.width / 2;

      tabList.scrollTo({
        left: scrollLeft,
        behavior: "smooth",
      });
    }
  }, []);

  useEffect(() => {
    if (!pathname) return;

    const segments = pathname.split("/");
    const currentSegment = segments[segments.length - 1];

    const tabIndex = tabRoutes.findIndex(
      (route) => currentSegment === route.segment
    );

    if (tabIndex !== -1) {
      setActiveTabIndex(tabIndex);
    } else {
      setActiveTabIndex(0);
    }
  }, [pathname]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      scrollToActiveTab();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [activeTabIndex, scrollToActiveTab]);

  const handleTabChange = (index: number) => {
    setActiveTabIndex(index);
    router.push(tabRoutes[index].path);
  };

  return (
    <Box
      px={{ base: 4, md: 6 }}
      py={6}
      borderRadius={6}
      boxShadow='sm'
      bg='white'
      maxW='8xl'
      mb={4}
    >
      <Tabs
        index={activeTabIndex}
        onChange={handleTabChange}
        isLazy
      >
        <TabList
          ref={tabListRef}
          overflowX={{ base: "auto", md: "visible" }}
          overflowY='visible'
          css={{
            "&::-webkit-scrollbar": {
              display: "none",
            },
            "-ms-overflow-style": "none",
            "scrollbar-width": "none",
          }}
        >
          {tabRoutes.map((route, index) => (
            <Tab
              key={route.segment}
              ref={activeTabIndex === index ? activeTabRef : null}
              _selected={{
                borderBottom: { base: "4px solid", md: "2px solid" },
                borderColor: "blue.600",
                color: "blue.600",
                fontWeight: "semibold",
                bg: "none",
              }}
              _hover={{
                color: "blue.600",
              }}
              minW='max-content'
              transition='all 0.2s'
            >
              {route.label}
            </Tab>
          ))}
        </TabList>
        <TabPanels>
          {tabRoutes.map((route) => (
            <TabPanel
              key={route.segment}
              px={0}
              mx={0}
            >
              {children}
            </TabPanel>
          ))}
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default LoanTabs;
