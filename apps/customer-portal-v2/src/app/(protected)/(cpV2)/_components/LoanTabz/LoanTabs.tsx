"use client";
import { Viewer } from "@/__generated/graphql";
import { Box } from "@chakra-ui/react";
import { useParams } from "next/navigation";
import React from "react";
import { RepaymentTable } from "../RepaymentTable";
import { TransactionsTable } from "../TransactionsTable";

type Props = {
  viewer: Viewer;
  initialData: {
    transactions: any[];
    pageInfo: {
      hasNextPage: boolean;
      endCursor: string | null;
    };
    totalCount: number;
  } | null;
  error: string | null;
};

const LoanTabs = ({ viewer, initialData, error }: Props) => {
  const params = useParams();
  const { tabs } = params || {};

  return (
    <Box>
      {tabs === "repayment-schedule" ? (
        <RepaymentTable viewer={viewer} />
      ) : tabs === "transactions" ? (
        <TransactionsTable
          initialData={initialData}
          error={error}
        />
      ) : (
        "not found"
      )}
    </Box>
  );
};

export default LoanTabs;
