"use client";

import {
  Box,
  Button,
  ButtonGroup,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  StackDivider,
  Text,
  VStack,
  Link as ChakraLink,
  Alert,
  AlertIcon,
  AlertDescription,
  FormErrorMessage,
} from "@chakra-ui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import Link from "next/link";
import { useParams } from "next/navigation";
import { AuthFormValues, loginSchema } from "@/src/schema";
import { signIn } from "next-auth/react";
import { PasswordInput } from "@/components/PasswordInput";
import { isAbsolutePath } from "@/utils/index";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { ClientInfo } from "@/__generated/graphql";
import { getCpV2ClientInfo, loginRedirection } from "@/src/app/actions";
import { useEffect, useState } from "react";
import { FullPageLoader } from "@/components/FullPageLoader";

export default function LoginPage() {
  const { error, setError } = useMessageTimer();
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const { name, clientId, howToApplyVideoLink } = clientInfo || {};
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<AuthFormValues>({
    resolver: zodResolver(loginSchema),
    mode: "onChange",
  });

  const onSubmit = async (data: AuthFormValues) => {
    setError("");
    try {
      const result = await signIn("credentials", {
        ...data,
        clientId,
        action: "login",
        redirect: false,
      });

      if (result?.error) {
        setError(result?.error);
      } else {
        const path = basePath ? (basePath as string) : null;
        return await loginRedirection({
          basePath: path,
        });
      }
    } catch (error) {
      throw new Error("Signin failed");
    }
  };
  const signUpUrl = basePath ? `/${basePath}/sign-up` : `/sign-up`;
  const forgotPasswordUrl = basePath
    ? `/${basePath}/forgot-password`
    : `/forgot-password`;

  if (!clientInfo) return <FullPageLoader />;
  return (
    <VStack
      bg='white'
      borderRadius='lg'
      py={8}
      px={{ base: 0, md: 16 }}
      spacing={{ base: 2, md: 6 }}
      w={{ base: "full", md: "xl", lg: "4xl" }}
      my={{
        base: 16,
        md: 8,
      }}
    >
      <Heading
        as='h2'
        fontSize={{ base: "20px", md: "24px" }}
        my={4}
        textAlign='center'
        data-testid='title'
      >{`Welcome to ${name}`}</Heading>

      <Stack
        direction={{ base: "column", md: "row" }}
        spacing={6}
        divider={<StackDivider borderColor='gray.200' />}
        w='full'
      >
        <Box
          w='full'
          px={{ base: 4, md: 0 }}
        >
          <Heading
            as='h3'
            size='sm'
            my={4}
            textAlign={{ base: "center", md: "left" }}
          >
            Create Account
          </Heading>

          <Stack
            spacing={4}
            textAlign={{ base: "center", md: "left" }}
          >
            <Text>
              New to {name}? Create an account to start applying for a loan.
            </Text>
            <ButtonGroup
              justifyContent={{ base: "center", md: "left" }}
              mb={4}
            >
              <Button
                as={Link}
                w='fit-content'
                href={signUpUrl}
                disabled={isSubmitting}
                px={8}
                _hover={{
                  textDecoration: "none",
                  bg: "customBrand.600",
                }}
                _visited={{
                  color: "white",
                }}
                data-testid='sign-up-button'
              >
                Sign Up
              </Button>
            </ButtonGroup>
          </Stack>

          <Box textAlign={{ base: "center", md: "left" }}>
            {howToApplyVideoLink && (
              <Text as='span'>
                Want to know how to apply for loans? Click here to
                <ChakraLink
                  href={isAbsolutePath(howToApplyVideoLink)}
                  color='customPrimary.500'
                  isExternal
                  mx={1}
                  fontWeight='bold'
                  _visited={{
                    color: "customPrimary.500",
                  }}
                  _hover={{
                    color: "customPrimary.600",
                  }}
                  data-testid='how-to-apply-link'
                >
                  learn
                </ChakraLink>
                .
              </Text>
            )}
          </Box>
        </Box>

        <Box
          w='full'
          px={{ base: 4, md: 0 }}
        >
          <Heading
            as='h3'
            size='sm'
            my={4}
            textAlign={{ base: "center", md: "left" }}
          >
            Login
          </Heading>

          {error && (
            <Alert
              status='error'
              w='full'
              borderRadius={6}
              my={4}
              data-testid='error-alert'
            >
              <AlertIcon />
              <AlertDescription
                whiteSpace='pre-wrap'
                wordBreak='break-word'
              >
                {error}
              </AlertDescription>
            </Alert>
          )}

          <Stack
            as='form'
            spacing={4}
            w='full'
            onSubmit={handleSubmit(onSubmit)}
          >
            <FormControl isInvalid={!!errors.email}>
              <FormLabel>Email</FormLabel>
              <Input
                type='email'
                placeholder='Enter your email address'
                disabled={isSubmitting}
                data-testid='email-input'
                {...register("email")}
              />

              <FormErrorMessage data-testid='email-error-message'>
                {errors?.email?.message}
              </FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.password}>
              <FormLabel>Password</FormLabel>
              <PasswordInput
                register={register}
                placeholder='Enter your password'
                name='password'
                disabled={isSubmitting}
                data-testid='password-input'
              />
              <FormErrorMessage data-testid='password-error-message'>
                {errors?.password?.message}
              </FormErrorMessage>
            </FormControl>
            <ButtonGroup
              justifyContent='center'
              mt={2}
            >
              <Button
                type='submit'
                isLoading={isSubmitting}
                w='fit-content'
                px={8}
                data-testid='sign-in-button'
              >
                Sign In
              </Button>
            </ButtonGroup>
          </Stack>

          <Text
            mt={4}
            textAlign='center'
          >
            <ChakraLink
              as={Link}
              href={forgotPasswordUrl}
              passHref
              color='customPrimary.500'
              fontWeight='bold'
              _visited={{
                color: "customPrimary.500",
              }}
              _hover={{
                color: "customPrimary.600",
              }}
              data-testid='forgot-password-link'
            >
              Forgot password?
            </ChakraLink>
          </Text>
        </Box>
      </Stack>
    </VStack>
  );
}
