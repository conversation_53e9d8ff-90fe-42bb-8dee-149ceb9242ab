"use client";

import {
  Application,
  CompleteBanksStatementInput,
  CreateApplicationTraceInput,
  InitiateBanksStatementInput,
  Viewer,
} from "@/__generated/graphql";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  Center,
  Flex,
  Heading,
  ListItem,
  Stack,
  Text,
  UnorderedList,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Input,
} from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { MbsFormSchema, MbsFormType } from "@/src/schema";
import { PasswordInput } from "@/components/PasswordInput";
import { APPLICATION_STEPS } from "@/utils/constants";
import useMbs from "../../_services/Mbs/useMbs";

type Props = {
  viewer: Viewer;
  application: Application;
  applicationNumber: string;
  createAppTrace: ({
    page,
    comment,
    isDebug,
    metadata,
  }: {
    page: CreateApplicationTraceInput["page"];
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
  }) => Promise<any>;
  triggerOtp: ({
    isRetried,
  }: {
    isRetried?: InitiateBanksStatementInput["isRetried"];
  }) => Promise<
    | {
        success: boolean;
        error: any;
      }
    | undefined
  >;
  completeBankStatementRequest: ({
    password,
    skipStep,
    ticketNum,
  }: {
    password?: CompleteBanksStatementInput["password"];
    skipStep?: CompleteBanksStatementInput["skipStep"];
    ticketNum?: CompleteBanksStatementInput["ticketNum"];
  }) => Promise<
    | {
        error: any;
        data: null;
        success: boolean;
        redirect: boolean;
      }
    | {
        data: any;
        error: null;
        success: boolean;
        redirect: boolean;
      }
    | undefined
  >;
  requiresBankStatementUpload: boolean;
};

const Mbs = ({
  application,
  applicationNumber,
  viewer,
  completeBankStatementRequest,
  createAppTrace,
  triggerOtp,
  requiresBankStatementUpload,
}: Props) => {
  const router = useRouter();
  const {
    otpTimer,
    skipMbsTimer,
    useCountDown,
    handleTicketSubmit,
    handleSkip,
    error,
    success,
    setError,
    setSuccess,
    backToExternalMbs,
    getPath,
    redirectLoading,
    backLoading,
  } = useMbs({
    application,
    applicationNumber,
    viewer,
    completeBankStatementRequest,
    createAppTrace,
    isExternal: false,
  });
  const [isResending, setIsResending] = useState(false);

  const { requiredSteps } = application;

  const isExternalMbs = requiredSteps?.includes(
    APPLICATION_STEPS.completeExternalBankStatementRequest.name
  );

  useEffect(() => {
    // Only run if isExternalMbs is false
    if (!isExternalMbs) {
      const mbsOtpStatus = localStorage.getItem("mbsOtpRequested");
      if (!mbsOtpStatus) {
        sendInitialOtp();
      }
    }
  }, []);

  const sendInitialOtp = async () => {
    try {
      setError("");
      const result = await triggerOtp({
        isRetried: false,
      });

      if (result?.success) {
        setIsResending(false);
        localStorage.setItem("mbsOtpRequested", "true");
        setSuccess(`OTP sent successfully`);
        createAppTrace({
          page: "MBS Page",
          comment: "OTP sent successfully",
        });
      } else if (result?.error) {
        setIsResending(false);
        setError(result?.error || "Failed to send OTP");
      }
    } catch (error: any) {
      setIsResending(false);

      createAppTrace({
        page: "MBS Page",
        comment: error?.message || "Failed to send OTP",
        isDebug: true,
        metadata: {
          error: error?.message || "Failed to send OTP",
        },
      });
    }
  };

  const resendOtp = async () => {
    try {
      setError("");
      setIsResending(true);
      const result = await triggerOtp({ isRetried: true });

      if (result?.success) {
        setIsResending(false);
        localStorage.setItem("mbsOtpRequested", "true");
        setSuccess(`OTP resent successfully`);
        createAppTrace({
          page: "MBS Page",
          comment: "OTP resent successfully",
        });
      } else if (result?.error) {
        setIsResending(false);
        setError(result?.error || "Failed to send OTP");
      }
    } catch (error: any) {
      setIsResending(false);
      createAppTrace({
        page: "MBS Page",
        comment: "Failed to send OTP",
        isDebug: true,
        metadata: {
          error: error?.message || "Failed to send OTP",
        },
      });
    }
  };

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<MbsFormType>({
    mode: "onChange",
    resolver: zodResolver(MbsFormSchema),
  });

  const onSubmit = async (data: MbsFormType) => {
    try {
      setError("");
      const result = await handleTicketSubmit({
        ...data,
      });

      if (result?.success) {
        localStorage.removeItem("mbsOtpRequested");

        if (result?.redirect) {
          if (requiresBankStatementUpload) {
            const path = getPath({ path: "upload-statement" });
            router.push(path);
          } else {
            const path = getPath({ path: "breakdown" });
            router.push(path);
          }
        }

        reset();
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to verify email");
    }
  };

  const Countdown = () => {
    const timeLeft = useCountDown();
    return (
      <Box>
        Resend OTP in <span className='timer'>00:{timeLeft}s</span>
      </Box>
    );
  };

  const accountInfoData = [
    { label: "Account Number", value: application?.bankAccount?.accountNumber },
    { label: "Account Name", value: application?.bankAccount?.accountName },
    { label: "Bank", value: application?.bankAccount?.bank?.name },
  ];

  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={2}
        textAlign='center'
        data-testid='mbs-heading'
      >
        Enter the Ticket Number and OTP/Password sent by your bank. (Check
        Email/SMS)
      </Heading>

      {error && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='mbs-error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {error}
          </AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert
          status='success'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='mbs-success-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {success}
          </AlertDescription>
        </Alert>
      )}

      <Box
        textAlign='center'
        my={4}
        w='100%'
        bg='blackAlpha.100'
        borderRadius={6}
        p={4}
      >
        <Flex
          flexDir='column'
          mx='auto'
          w='fit-content'
          textAlign='left'
          data-testid='mbs-account-info'
        >
          <UnorderedList>
            {accountInfoData?.map((accountInfo, idx) => (
              <ListItem key={idx}>
                {`${accountInfo?.label}:  `}
                <Box
                  as='span'
                  fontWeight={700}
                >
                  {accountInfo?.value}
                </Box>
              </ListItem>
            ))}
          </UnorderedList>
        </Flex>
      </Box>

      <Stack
        spacing={6}
        pt={6}
        as='form'
        onSubmit={handleSubmit(onSubmit)}
      >
        <Stack spacing={4}>
          <FormControl isInvalid={!!errors?.ticketNum}>
            <FormLabel>Ticket Number</FormLabel>
            <Input
              placeholder='Enter ticket number'
              {...register("ticketNum")}
              disabled={isSubmitting}
              data-testid='mbs-ticket-num-input'
            />
            <FormErrorMessage
              fontSize='sm'
              data-testid='mbs-ticket-num-error'
            >
              {errors?.ticketNum?.message}
            </FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.password}>
            <FormLabel>Password</FormLabel>
            <PasswordInput
              register={register}
              placeholder='Enter password'
              name='password'
              disabled={isSubmitting}
              data-testid='mbs-password-input'
            />
            <FormErrorMessage data-testid='mbs-password-error'>
              {errors?.password?.message}
            </FormErrorMessage>
          </FormControl>
        </Stack>

        {!isExternalMbs &&
          (otpTimer ? (
            <Center data-testid='mbs-otp-timer'>
              <Countdown />
            </Center>
          ) : (
            <Center
              w='100%'
              display='flex'
              flexDirection='column'
            >
              <Text>Didn't get Ticket Number/OTP sent by your Bank?</Text>
              <Button
                variant='link'
                onClick={resendOtp}
                colorScheme='customPrimary'
                isLoading={isResending}
                loadingText='Sending...'
                fontWeight={600}
                textDecoration='none'
                _visited={{
                  color: "customPrimary.500",
                }}
                _hover={{
                  textDecoration: "underline",
                }}
                data-testid='mbs-resend-otp-button'
              >
                Resend OTP
              </Button>
            </Center>
          ))}

        <Flex
          justifyContent='flex-start'
          alignItems='center'
          w='100%'
          flexDir='column'
          gap={2}
        >
          <Button
            type='submit'
            w='full'
            isLoading={isSubmitting}
            px={8}
            data-testid='mbs-submit-button'
          >
            Proceed
          </Button>

          {!skipMbsTimer && (
            <Button
              onClick={handleSkip}
              variant='outline'
              isLoading={redirectLoading}
              colorScheme='customPrimary'
              w='full'
              px={8}
              mt={2}
              data-testid='mbs-skip-button'
            >
              Skip to Upload Bank Statement
            </Button>
          )}

          <Button
            onClick={backToExternalMbs}
            variant='outline'
            isLoading={backLoading}
            colorScheme='customPrimary'
            w='full'
            px={8}
            mt={2}
            data-testid='mbs-back-button'
          >
            Back
          </Button>
        </Flex>
      </Stack>
    </Box>
  );
};

export default Mbs;
