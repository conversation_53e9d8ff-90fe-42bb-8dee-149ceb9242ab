"use client";

import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";
import {
  Application,
  ClientInfo,
  CompleteExternalBankStatementRequestInput,
  CreateApplicationTraceInput,
  Viewer,
} from "@/__generated/graphql";
import { Box, Button, Flex, Heading, Text } from "@chakra-ui/react";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import useMbs from "../../_services/Mbs/useMbs";

type Props = {
  viewer: Viewer;
  application: Application;
  applicationNumber: string;
  createAppTrace: ({
    page,
    comment,
    isDebug,
    metadata,
  }: {
    page: CreateApplicationTraceInput["page"];
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
  }) => Promise<any>;
  externalBankStatementRequest: ({
    password,
    skipStep,
    ticketNum,
  }: {
    password?: CompleteExternalBankStatementRequestInput["password"];
    skipStep?: CompleteExternalBankStatementRequestInput["skipStep"];
    ticketNum?: CompleteExternalBankStatementRequestInput["ticketNum"];
  }) => Promise<
    | {
        error: any;
        data: null;
        success: boolean;
        redirect: boolean;
      }
    | {
        data: any;
        error: null;
        success: boolean;
        redirect: boolean;
      }
    | undefined
  >;

  requiresBankStatementUpload: boolean;
};

const MbsInstructions = ({
  application,
  applicationNumber,
  viewer,
  createAppTrace,
  externalBankStatementRequest,
}: Props) => {
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;
  const {
    customMbsDates,
    bankName,
    handleExternalSubmit,
    handleSkip,
    redirectLoading,
    redirectExternalLoading,
  } = useMbs({
    application,
    applicationNumber,
    viewer,
    createAppTrace,
    externalBankStatementRequest,
    isExternal: true,
  });

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const clientName = clientInfo?.name!;

  const instructions = [
    `Log in to ${bankName} internet banking on your computer or your smartphone`,
    'Check the Sidebar for "Send Statement to Third party" or "Statement Request"',
    `For Destination, please select ${clientName}.`,
    "For Role, please select Applicant.",

    `For Start Date, please select ${customMbsDates?.startDate}`,
    `For End Date, please select ${customMbsDates?.endDate}`,
    "For Applicant(s), please enter your Full Name",
    "Complete the rest of the form and submit.",
  ];

  if (!clientInfo) return <FullPageLoader />;
  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={2}
        textAlign='center'
        data-testid='mbs-instructions-heading'
      >
        Please follow the instructions below to retrieve your bank statement
      </Heading>
      <Text
        mb={6}
        textAlign='center'
      >
        Your bank may charge you separately for this statement
      </Text>

      <Flex
        w='100%'
        flexDir='column'
      >
        <Flex
          flexDir='column'
          gap={4}
          as='ol'
          px={6}
        >
          {instructions?.map((instruction, idx) => (
            <Text
              as='li'
              key={idx}
            >
              {instruction}
            </Text>
          ))}
        </Flex>
        <Text
          mt={8}
          fontSize='large'
          textAlign='center'
          fontWeight='bold'
        >
          Click Continue button below when you receive your ticket number and
          password via SMS
        </Text>
        <Flex
          justifyContent='flex-start'
          alignItems='center'
          w='100%'
          mt={8}
          flexDir='column'
          gap={6}
        >
          <Button
            onClick={handleExternalSubmit}
            isLoading={redirectExternalLoading}
            w='full'
            px={8}
            data-testid='mbs-instructions-continue-button'
          >
            Next
          </Button>

          <Button
            variant='outline'
            onClick={handleSkip}
            isLoading={redirectLoading}
            colorScheme='customPrimary'
            borderColor='customPrimary.500'
            color='customPrimary.500'
            w='full'
            px={{ base: 2, sm: 4, md: 8 }}
            py={{ base: 3, sm: 4 }}
            fontSize={{ base: "sm", sm: "md" }}
            whiteSpace='normal'
            textAlign='center'
            data-testid='mbs-instructions-skip-button'
          >
            Skip, I don't have mobile/internet banking
          </Button>
        </Flex>
      </Flex>
    </Box>
  );
};

export default MbsInstructions;
