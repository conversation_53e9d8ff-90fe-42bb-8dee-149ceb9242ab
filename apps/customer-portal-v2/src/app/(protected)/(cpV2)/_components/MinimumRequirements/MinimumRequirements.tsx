"use client";
import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Heading,
  Stack,
  Text,
  List,
  ListItem,
  ListIcon,
  Center,
} from "@chakra-ui/react";
import { useParams, useRouter } from "next/navigation";
import { CircleCheckBig } from "lucide-react";
import { ClientInfo } from "@/__generated/graphql";
import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";

const MinimumRequirements = () => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, [basePath]);

  const { name, kycConfiguration, useRemita } = clientInfo || {};
  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit = async () => {
    setIsSubmitting(true);

    const nextPath = basePath
      ? `/${basePath}/sign-up/verify-bvn`
      : `/sign-up/verify-bvn`;

    router.push(nextPath);
  };

  if (!clientInfo) return <FullPageLoader />;

  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={2}
        textAlign='center'
        data-testid='minimum-requirements-heading'
      >
        Minimum Requirement
      </Heading>
      <Text
        mb={6}
        textAlign='center'
      >
        You will need the following in order to successfully get a loan
      </Text>

      <Stack
        spacing={8}
        w='full'
      >
        {kycConfiguration?.minimumRequirements?.builders[0]?.options?.map(
          (requirement: { id: string; option: string }) => (
            <List
              spacing={2}
              key={requirement?.id}
              justifyContent='center'
              alignItems='center'
            >
              <ListItem>
                <ListIcon
                  as={CircleCheckBig}
                  color='customPrimary.500'
                  w={5}
                  h={5}
                  mr={6}
                  size='md'
                />
                {requirement?.option}
              </ListItem>
            </List>
          )
        )}

        <Center textAlign='center'>
          {useRemita ? (
            <p>
              By clicking on Continue, I consent to {name} obtaining information
              from relevant third parties as may be necessary, on my personal
              details, identification details, employment details, financial
              details, loan details and other related details including my Bank
              Verification Number (Data), to make a decision on my loan
              application. I also consent to the scheduled payments and loan
              amount to be deducted from my salary at source before credit to
              any of my bank accounts. I further consent that any outstanding
              loans shall be recovered automatically from any bank accounts
              linked to my Data, whether or not disclosed in my loan
              application, in the case of default.
            </p>
          ) : (
            <p>
              By clicking on Continue, I consent to {name} obtaining information
              from relevant third parties as may be necessary to make a decision
              on my loan application.
            </p>
          )}
        </Center>
        <Button
          type='submit'
          isLoading={isSubmitting}
          onClick={onSubmit}
          w='full'
          px={8}
          data-testid='continue-button'
        >
          Continue
        </Button>
      </Stack>
    </Box>
  );
};

export default MinimumRequirements;
