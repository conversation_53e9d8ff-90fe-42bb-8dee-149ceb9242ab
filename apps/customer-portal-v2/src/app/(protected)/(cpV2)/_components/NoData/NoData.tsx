"use client";
import { Box, Center, Heading, VStack } from "@chakra-ui/react";
import { FileX } from "lucide-react";
import React from "react";

const NoData = () => {
  return (
    <Box
      maxW='8xl'
      bg='white'
      p={8}
      px={{ base: 6, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Center>
        <VStack
          spacing={8}
          my={4}
        >
          <FileX
            width='60px'
            height='60px'
          />
          <Heading
            textAlign='center'
            color='gray.400'
            fontSize={{ base: "lg", lg: "2xl" }}
          >
            You have no active loan
          </Heading>
        </VStack>
      </Center>
    </Box>
  );
};

export default NoData;
