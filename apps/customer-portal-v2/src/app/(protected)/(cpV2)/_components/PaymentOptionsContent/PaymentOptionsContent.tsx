"use client";

import {
  Application,
  ApplicationBankStageInput,
  Bank,
  BankAccountType,
  ClientInfo,
  CreateAccountBankInput,
  CreateApplicationTraceInput,
  Viewer,
} from "@/__generated/graphql";
import { Stack } from "@chakra-ui/react";
import React from "react";
import usePaymentService from "../../_services/PaymentService/usePaymentService";
import useUploadBankStatement from "../../_services/UploadBankStatements/useUploadBankStatement";
import { AddBank } from "./_components";

type Props = {
  application: Application;
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  viewer: Viewer;
  clientInfo: ClientInfo | null;
  addBank: ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  setBank: ({
    applicationId,
    accountBankId,
  }: {
    applicationId: ApplicationBankStageInput["applicationId"];
    accountBankId: ApplicationBankStageInput["accountBankId"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  getResolvedAccountNumber: ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  banks: Bank[];
  uploadAction: (formData: FormData) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
  >;
};

const PaymentOptionsContent = ({
  application,
  clientInfo,
  createAppTrace,
  viewer,
  addBank,
  getResolvedAccountNumber,
  setBank,
  banks,
  uploadAction,
}: Props) => {
  const {
    isLoading,
    resolveAccountNumber,
    handleAddBankDetails,
    handleSetApplicationBank,
    success,
    error,
    setError,
  } = usePaymentService({
    application,
    clientInfo,
    viewer,
    createAppTrace,
    setBank,
    addBank,
    getResolvedAccountNumber,
  });

  const {
    uploadBankStatementFn,
    error: bankStatementError,
    setError: setBankStatementError,
    success: successBankStatement,
  } = useUploadBankStatement({
    createAppTrace,
    uploadAction,
  });

  const externalBankStatementTenor = clientInfo?.externalBankStatementTenor;

  return (
    <Stack spacing={18}>
      <AddBank
        application={application}
        banks={banks}
        isLoading={isLoading}
        handleAddBankDetails={handleAddBankDetails}
        resolveAccountNumber={resolveAccountNumber}
        handleSetApplicationBank={handleSetApplicationBank}
        success={success}
        error={error}
        setError={setError}
        externalBankStatementTenor={externalBankStatementTenor}
        uploadBankStatementFn={uploadBankStatementFn}
        bankStatementError={bankStatementError}
        setBankStatementError={setBankStatementError}
        successBankStatement={successBankStatement}
      />
    </Stack>
  );
};

export default PaymentOptionsContent;
