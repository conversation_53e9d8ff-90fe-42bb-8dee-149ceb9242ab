"use client";

import {
  Application,
  ApplicationStatusEnum,
  Bank,
  ClientInfo,
} from "@/__generated/graphql";
import {
  Box,
  Flex,
  Heading,
  Stack,
  Text,
  VStack,
  Center,
  Radio,
  RadioGroup,
} from "@chakra-ui/react";
import Image from "next/image";
import React from "react";
import { bankMetadata } from "@/utils/bankMetadata";
import EditBank from "./EditBank";
import { AddBankFormProps } from "@/src/types/addBankForm";
import { Landmark } from "lucide-react";

type Props = {
  application: Application;
  banks: Bank[];
  handleAddBankDetails: (
    data: Pick<AddBankFormProps, "accountNumber" | "bankId" | "accountName">
  ) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  resolveAccountNumber: (
    data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountType">
  ) => Promise<any>;
  isLoading: boolean;
  error: string;
  success: string;
  setError: React.Dispatch<React.SetStateAction<string>>;
  externalBankStatementTenor: ClientInfo["externalBankStatementTenor"];
  uploadBankStatementFn: ({
    bankStatement,
    bankId,
    password,
  }: {
    bankStatement: any;
    bankId: string;
    password: string;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  bankStatementError: string;
  setBankStatementError: React.Dispatch<React.SetStateAction<string>>;
  successBankStatement: string;
  handleSetApplicationBank?: ({ bankId }: { bankId: string }) => Promise<void>;
};

const AddBank = ({
  application,
  banks,
  handleAddBankDetails,
  resolveAccountNumber,
  isLoading,
  error,
  success,
  setError,
  externalBankStatementTenor,
  uploadBankStatementFn,
  bankStatementError,
  setBankStatementError,
  successBankStatement,
  handleSetApplicationBank,
}: Props) => {
  const initialData = application?.bankAccount!;

  const mergeBankData = () => {
    if (!initialData) return null;

    const matchingBank = bankMetadata.find((bankMeta) => {
      const bankName = initialData.bank?.name?.toLowerCase().trim();
      const metaName = bankMeta?.name?.toLowerCase().trim();

      // Non-strict matching: check if either name contains the other
      return (
        bankName &&
        metaName &&
        (bankName.includes(metaName) ||
          metaName.includes(bankName) ||
          bankName.replace(/\s+/g, "").includes(metaName.replace(/\s+/g, "")) ||
          metaName.replace(/\s+/g, "").includes(bankName.replace(/\s+/g, "")))
      );
    });

    return {
      ...initialData,
      bank: {
        ...initialData.bank,
        logoUrl: matchingBank?.logoUrl || "",
      },
    };
  };
  const bank = mergeBankData();

  const logoUrl = bank?.bank?.logoUrl!;
  const status = application?.status?.name;

  return (
    <Stack
      p={{ base: 1, lg: 4 }}
      direction={{ base: "column", lg: "row" }}
      spacing={{ base: 4, lg: 14 }}
    >
      <VStack
        w={{ base: "fit-content", lg: "300px" }}
        alignItems='flex-start'
        spacing={0.5}
      >
        <Heading size={{ base: "sm", lg: "md" }}>Bank Accounts</Heading>
        <Text color='gray.500'>This is used for loan disbursements.</Text>
      </VStack>
      <Box
        display={{ base: "flex", lg: "block" }}
        justifyContent={{ base: "center", lg: "flex-start" }}
        pb={4}
      >
        {bank ? (
          <RadioGroup>
            <Stack spacing={4}>
              {bank ? (
                <Flex
                  w={{ base: "290px", xl: "320px" }}
                  borderRadius={6}
                  border='1px solid #D6DDEB'
                  px={4}
                  py={2}
                  flexDirection='column'
                >
                  <Flex
                    alignItems='center'
                    justifyContent='space-between'
                    mb={4}
                    w='full'
                  >
                    {status === ApplicationStatusEnum.Pending ? (
                      <Radio
                        colorScheme='green'
                        border='1px solid black'
                        isChecked
                      >
                        <Text
                          fontSize='sm'
                          fontWeight='normal'
                        >
                          Active Account
                        </Text>
                      </Radio>
                    ) : (
                      <Text
                        fontSize='sm'
                        fontWeight='normal'
                      >
                        Active Account
                      </Text>
                    )}

                    {status === ApplicationStatusEnum.Pending ? (
                      <EditBank
                        banks={banks}
                        handleAddBankDetails={handleAddBankDetails}
                        resolveAccountNumber={resolveAccountNumber}
                        isLoading={isLoading}
                        error={error}
                        success={success}
                        setError={setError}
                        isEdit={true}
                        bank={bank}
                        application={application}
                        externalBankStatementTenor={externalBankStatementTenor}
                        uploadBankStatementFn={uploadBankStatementFn}
                        bankStatementError={bankStatementError}
                        setBankStatementError={setBankStatementError}
                        successBankStatement={successBankStatement}
                        handleSetApplicationBank={handleSetApplicationBank}
                      />
                    ) : null}
                  </Flex>

                  <Flex
                    gap={2}
                    alignItems='center'
                    color='gray.400'
                  >
                    {logoUrl ? (
                      <Image
                        src={logoUrl}
                        alt='bank-logo'
                        width={40}
                        height={40}
                        style={{ objectFit: "contain" }}
                      />
                    ) : (
                      <Center
                        boxSize={10}
                        border='1px solid'
                        p={1}
                        borderRadius={6}
                      >
                        <Landmark size={40} />
                      </Center>
                    )}

                    <Flex flexDirection='column'>
                      <Text
                        fontSize='sm'
                        wordBreak='break-word'
                      >
                        {!logoUrl
                          ? `${bank?.accountNumber} - ${bank?.bank?.name}`
                          : bank?.accountNumber}
                      </Text>
                      <Text color='black'>{bank?.accountName}</Text>
                    </Flex>
                  </Flex>
                </Flex>
              ) : null}
            </Stack>
          </RadioGroup>
        ) : status === ApplicationStatusEnum.Pending ? (
          <EditBank
            banks={banks}
            handleAddBankDetails={handleAddBankDetails}
            resolveAccountNumber={resolveAccountNumber}
            isLoading={isLoading}
            error={error}
            success={success}
            setError={setError}
            isEdit={false}
            bank={bank}
            application={application}
            externalBankStatementTenor={externalBankStatementTenor}
            uploadBankStatementFn={uploadBankStatementFn}
            bankStatementError={bankStatementError}
            setBankStatementError={setBankStatementError}
            successBankStatement={successBankStatement}
            handleSetApplicationBank={handleSetApplicationBank}
          />
        ) : null}
      </Box>
    </Stack>
  );
};

export default AddBank;
