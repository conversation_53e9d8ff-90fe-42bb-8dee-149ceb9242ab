"use client";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  useDisclosure,
  Text,
  FormControl,
  Alert,
  AlertIcon,
  AlertDescription,
  Flex,
  IconButton,
  Radio,
  RadioGroup,
  Stack,
  Box,
  useToast,
} from "@chakra-ui/react";
import { Controller, useForm } from "react-hook-form";
import { Edit2 } from "lucide-react";
import { NumericInput, Select, Input } from "@/components/forms";
import { AddBankFormProps } from "@/src/types/addBankForm";
import {
  Application,
  Bank,
  BankAccountDetails,
  BankAccountType,
  ClientInfo,
} from "@/__generated/graphql";
import UploadBankStatement from "./UploadBankStatement";

type Props = {
  banks: Bank[];
  handleAddBankDetails: (
    data: Pick<AddBankFormProps, "accountNumber" | "bankId" | "accountName">
  ) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  resolveAccountNumber: (
    data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountType">
  ) => Promise<any>;
  isLoading: boolean;
  error: string;
  success: string;
  setError: React.Dispatch<React.SetStateAction<string>>;
  bank: any;
  isEdit: boolean;
  application: Application;
  externalBankStatementTenor?: ClientInfo["externalBankStatementTenor"];
  uploadBankStatementFn?: ({
    bankStatement,
    bankId,
    password,
  }: {
    bankStatement: any;
    bankId: string;
    password: string;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  bankStatementError?: string;
  setBankStatementError?: React.Dispatch<React.SetStateAction<string>>;
  successBankStatement?: string;
  handleSetApplicationBank?: ({ bankId }: { bankId: string }) => Promise<void>;
};

const EditBank = ({
  banks,
  handleAddBankDetails,
  resolveAccountNumber,
  isLoading,
  error,
  success,
  setError,
  bank,
  isEdit,
  application,
  externalBankStatementTenor,
  uploadBankStatementFn,
  bankStatementError,
  setBankStatementError,
  successBankStatement,
  handleSetApplicationBank,
}: Props) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [selectedBankAccountId, setSelectedBankAccountId] =
    useState<string>("");
  const [showAddBankForm, setShowAddBankForm] = useState(false);

  const {
    handleSubmit,
    control,
    watch,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<AddBankFormProps>();

  const closeModal = () => {
    reset();
    setError("");
    setShowAddBankForm(false);
    setSelectedBankAccountId("");
    onClose();
  };

  const onOpenModal = () => {
    setError("");

    const defaultBank = application?.account?.bankAccounts?.find(
      (account) => account?.id === application?.bankAccount?.id
    );
    if (defaultBank) {
      setSelectedBankAccountId(defaultBank.id);

      setTimeout(() => {
        const defaultBankElement = document.querySelector(
          `input[value="${defaultBank.id}"]`
        );
        if (defaultBankElement) {
          defaultBankElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, 100);
    }
    onOpen();
  };

  const [resolveBank, setResolveBank] = useState<BankAccountDetails | null>(
    null
  );
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [updatedBank, setUpdatedBank] = useState<any>(null);

  const bankId = watch("bankId");
  const accountNumber = watch("accountNumber");
  const accountType = watch("accountType");

  const accountName =
    resolveBank && !Array.isArray(resolveBank)
      ? resolveBank?.accountName
      : undefined;

  // Handle bank selection
  const handleBankSelection = async (bankAccountId: string) => {
    setSelectedBankAccountId(bankAccountId);

    if (handleSetApplicationBank && bankAccountId) {
      try {
        await handleSetApplicationBank({ bankId: bankAccountId });
        toast({
          title: "Bank account updated successfully",
          status: "success",
          duration: 3000,
          isClosable: true,
          position: "top-right",
        });
        closeModal();
        setShowUploadModal(true);
      } catch (error: any) {
        setError(error?.message);
        setShowUploadModal(false);
      }
    }
  };

  const onSubmit = async (data: AddBankFormProps) => {
    const newBank = {
      ...bank,
      accountName,
      accountNumber: data?.accountNumber,
      bank: banks?.find((b) => b?.id === data?.bankId),
    };
    setUpdatedBank(newBank);

    if (application?.hasPdfBankStatement || !uploadBankStatementFn) {
      const payload = {
        accountNumber: data.accountNumber,
        bankId: data.bankId,
        accountName: accountName!,
      };
      const result = await handleAddBankDetails(payload);

      if (result?.error) {
        setError(result.error);
      } else {
        toast({
          title: "Bank account added and updated successfully",
          status: "success",
          duration: 3000,
          isClosable: true,
          position: "top-right",
        });
        closeModal();
        setShowUploadModal(true);
      }
    }
  };

  const bankAccountType = accountType || BankAccountType.Personal;

  useEffect(() => {
    if (accountNumber?.length === 10 && bankId && bankAccountType) {
      const fetchAccountDetails = async () => {
        const res = await resolveAccountNumber({
          bankId,
          accountNumber,
          accountType: bankAccountType,
        });
        setResolveBank(res?.resolveAccountNumber);
      };

      fetchAccountDetails();
    }
  }, [bankId, accountNumber, bankAccountType, resolveAccountNumber]);

  return (
    <>
      {isEdit ? (
        <IconButton
          aria-label='Edit'
          variant='ghost'
          color='gray.500'
          colorScheme='gray'
          icon={
            <Flex
              alignItems='center'
              gap={1}
            >
              <Edit2 size={14} />
              <Text>Edit</Text>
            </Flex>
          }
          onClick={onOpenModal}
          p={2}
        />
      ) : (
        <Button
          px={8}
          mt={6}
          colorScheme='customPrimary'
          onClick={onOpenModal}
        >
          Add Bank
        </Button>
      )}

      {/* Bank Selection and Add Bank Modal */}
      {!showUploadModal && (
        <Modal
          isOpen={isOpen}
          onClose={closeModal}
          size='lg'
        >
          <ModalOverlay />
          <ModalContent
            pos='fixed'
            transform='translate(-50%, -50%)'
          >
            <ModalHeader>
              {isEdit ? "Select or Edit Bank" : "Select or Add Bank"}
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody
              px={{ base: 2, md: 6 }}
              pb={4}
            >
              {error && (
                <Alert
                  status='error'
                  w='full'
                  my={4}
                  borderRadius={6}
                >
                  <AlertIcon />
                  <AlertDescription
                    whiteSpace='pre-wrap'
                    wordBreak='break-word'
                  >
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {/* Existing Bank Accounts Section */}
              {application?.account?.bankAccounts &&
                application.account.bankAccounts.length > 0 && (
                  <Box mb={6}>
                    <Text
                      fontWeight='semibold'
                      mb={3}
                    >
                      Select from existing bank accounts:
                    </Text>
                    <FormControl
                      as='div'
                      gap={4}
                      display='flex'
                      flexDirection='column'
                      maxH='200px'
                      overflowY='auto'
                      border='1px solid'
                      borderColor='gray.200'
                      p={{
                        base: 1,
                        md: 4,
                      }}
                      borderRadius={6}
                    >
                      <RadioGroup
                        onChange={handleBankSelection}
                        value={selectedBankAccountId}
                      >
                        <Stack spacing={3}>
                          {application.account.bankAccounts.map(
                            (bankAccount) => (
                              <Flex
                                key={bankAccount?.id}
                                border='1px solid'
                                borderColor={
                                  selectedBankAccountId === bankAccount?.id
                                    ? "customBrand.400"
                                    : "gray.200"
                                }
                                borderRadius={6}
                                _hover={{
                                  borderColor: "customBrand.400",
                                }}
                              >
                                <Radio
                                  value={bankAccount?.id}
                                  w='100%'
                                  p={{
                                    base: 2,
                                    md: 4,
                                  }}
                                  isDisabled={isLoading || showAddBankForm}
                                >
                                  <Flex
                                    width='100%'
                                    justifyContent='space-between'
                                    alignItems='center'
                                    ml={4}
                                  >
                                    <Stack spacing={1}>
                                      <Text
                                        textTransform='capitalize'
                                        fontWeight='bold'
                                      >
                                        {bankAccount?.accountName} -{" "}
                                        {bankAccount?.accountNumber}
                                      </Text>
                                      <Text color='gray.600'>
                                        {bankAccount?.bank?.name}
                                        {application?.bankAccount?.id ===
                                          bankAccount?.id && (
                                          <Text
                                            as='span'
                                            fontWeight='bold'
                                            ml={2}
                                          >
                                            (Default)
                                          </Text>
                                        )}
                                      </Text>
                                    </Stack>
                                  </Flex>
                                </Radio>
                              </Flex>
                            )
                          )}
                        </Stack>
                      </RadioGroup>
                    </FormControl>
                  </Box>
                )}

              {/* Add New Bank Account Section */}
              <Button
                size='sm'
                variant='link'
                colorScheme='customPrimary'
                onClick={() => setShowAddBankForm(!showAddBankForm)}
                mb={4}
              >
                {showAddBankForm ? "Hide Form" : "Add New Bank"}
              </Button>
              {showAddBankForm && (
                <Box>
                  <Flex
                    justify='space-between'
                    align='center'
                    mb={4}
                  >
                    <Text fontWeight='semibold'>Add new bank account:</Text>
                  </Flex>

                  <FormControl as='form'>
                    <Flex
                      gap={4}
                      flexDir='column'
                    >
                      <Controller
                        name='accountType'
                        control={control}
                        render={({ field }) => (
                          <Select
                            {...field}
                            label='Account Type'
                            autoFocus
                            isDisabled={isLoading || isSubmitting}
                            isInvalid={!!errors?.accountType}
                            defaultValue='personal'
                          >
                            <option value='personal'>Personal</option>
                            <option value='business'>Business</option>
                          </Select>
                        )}
                      />

                      <Controller
                        name='bankId'
                        control={control}
                        render={({ field }) => (
                          <Select
                            {...field}
                            placeholder='--Select a Bank--'
                            label='Bank'
                            isDisabled={isLoading || isSubmitting}
                            isInvalid={!!errors?.bankId}
                            errorMessage={errors?.bankId?.message as string}
                          >
                            {banks?.map((data, idx) => (
                              <option
                                key={idx}
                                value={data?.id}
                              >
                                {data?.name}
                              </option>
                            ))}
                          </Select>
                        )}
                      />

                      <Flex
                        gap={6}
                        flexDir='column'
                      >
                        <Controller
                          name='accountNumber'
                          control={control}
                          render={({ field }) => (
                            <NumericInput
                              allowLeadingZeros
                              thousandSeparator={false}
                              {...field}
                              label='Account Number'
                              maxLength={10}
                              placeholder='Enter your account number'
                              isInvalid={!!errors?.accountNumber}
                              errorMessage={
                                errors?.accountNumber?.message as string
                              }
                              disabled={isLoading || isSubmitting}
                            />
                          )}
                        />
                        {accountName &&
                          accountNumber?.length === 10 &&
                          bankId &&
                          bankAccountType && (
                            <Input
                              value={accountName}
                              borderRadius={6}
                              label='Account Name'
                              isDisabled
                              isLoading={isLoading}
                            />
                          )}
                      </Flex>

                      <Text
                        mb={6}
                        size='sm'
                      >
                        <b>Note:</b> Please select the <b>Personal</b> account
                        type for personal loans and the <b>Business</b> account
                        type for business loans.
                      </Text>

                      <Button
                        onClick={handleSubmit(onSubmit)}
                        isLoading={isSubmitting || isLoading}
                        w='full'
                        px={8}
                        colorScheme='customPrimary'
                      >
                        Add Bank Account
                      </Button>
                    </Flex>
                  </FormControl>
                </Box>
              )}
            </ModalBody>
          </ModalContent>
        </Modal>
      )}

      {/* Upload Bank Statement Modal */}
      {!application?.hasPdfBankStatement &&
        showUploadModal &&
        uploadBankStatementFn && (
          <UploadBankStatement
            applicationBank={updatedBank}
            application={application}
            banks={banks}
            externalBankStatementTenor={externalBankStatementTenor}
            uploadBankStatementFn={uploadBankStatementFn}
            bankStatementError={bankStatementError || ""}
            setBankStatementError={setBankStatementError || (() => {})}
            successBankStatement={successBankStatement || ""}
            handleAddBankDetails={handleAddBankDetails}
            setBankError={setError}
            errorBank={error || ""}
            successBank={success || ""}
          />
        )}
    </>
  );
};

export default EditBank;
