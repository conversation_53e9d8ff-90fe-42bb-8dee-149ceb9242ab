"use client";
import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON>dal,
  <PERSON>dal<PERSON>verlay,
  <PERSON>dalContent,
  ModalHeader,
  ModalBody,
  useDisclosure,
  useToast,
  Box,
  Text,
  Button,
  Flex,
  Alert,
  AlertIcon,
  AlertDescription,
  FormControl,
  FormLabel,
  UnorderedList,
  ListItem,
  Stack,
  FormHelperText,
  FormErrorMessage,
  Input,
} from "@chakra-ui/react";
import { Controller, useForm } from "react-hook-form";
import { Select } from "@/components/forms";
import { Application, Bank, ClientInfo } from "@/__generated/graphql";
import { FormSchema, FormValues } from "@/src/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { format, subMonths } from "date-fns";
import { bytesToSize, getAttributrFromProduct } from "@/utils/index";
import { AddBankFormProps } from "@/src/types/addBankForm";

type Props = {
  banks: Bank[];
  applicationBank: any;
  application: Application;
  externalBankStatementTenor: ClientInfo["externalBankStatementTenor"];
  uploadBankStatementFn: ({
    bankStatement,
    bankId,
    password,
  }: {
    bankStatement: any;
    bankId: string;
    password: string;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  bankStatementError: string;
  setBankStatementError: React.Dispatch<React.SetStateAction<string>>;
  setBankError: React.Dispatch<React.SetStateAction<string>>;
  successBankStatement: string;
  handleAddBankDetails: (
    data: Pick<AddBankFormProps, "accountNumber" | "bankId" | "accountName">
  ) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  errorBank: string;
  successBank: string;
};

interface AccountInfo {
  label: string;
  value: string | undefined;
}

const UploadBankStatement = ({
  banks,
  applicationBank,
  application,
  externalBankStatementTenor,
  uploadBankStatementFn,
  bankStatementError,
  setBankStatementError,
  successBankStatement,
  handleAddBankDetails,
  setBankError,
  errorBank,
}: Props) => {
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const setError = setBankStatementError || setBankError;
  const error = bankStatementError || errorBank;
  const success = successBankStatement;

  const {
    handleSubmit,
    control,
    register,
    trigger,
    setValue,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    mode: "onChange",
    resolver: zodResolver(FormSchema),
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { accountName, accountNumber, bank } = applicationBank || {};
  const { id, name } = bank || {};
  const tenorValue = Number(externalBankStatementTenor || 3);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setValue("bankStatement", file);
      trigger("bankStatement");
    }
  };

  const namesSet = new Set(["allowBankSelectionOnPdfUpload"]);
  const loanCategoryAttributes =
    application?.loanCategory?.loanCategoryAttributes!;
  const applicationAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });
  const allowBankSelectionOnPdfUpload: boolean =
    applicationAttributes?.allowBankSelectionOnPdfUpload;

  const onSubmit = async (data: FormValues) => {
    try {
      const { bankId, bankStatement, password } = data || {};

      const payload = {
        bankId: id,
        accountName,
        accountNumber,
        bankStatement: {
          validity: true,
          file: bankStatement!,
          bankId: bankId! || id!,
          password: password || "",
        },
      };

      const editBankRes = await handleAddBankDetails({
        accountNumber: payload.accountNumber,
        bankId: payload.bankId,
        accountName: payload.accountName,
      });

      if (editBankRes?.error) {
        throw new Error(`Bank update failed: ${editBankRes.error}`);
      }

      if (uploadBankStatementFn) {
        const res = await uploadBankStatementFn({
          bankId: payload.bankStatement.bankId,
          password: payload.bankStatement.password,
          bankStatement: payload.bankStatement,
        });

        if (res?.error) {
          throw new Error(`Upload failed: ${res.error}`);
        }
      }
    } catch (error: any) {
      setError(error.message);
    }
  };

  const accountInfoData: AccountInfo[] = useMemo(
    () => [
      { label: "Account Number", value: accountNumber },
      { label: "Account Name", value: accountName },
      { label: "Bank", value: name },
      {
        label: "Expected Duration",
        value: `${format(subMonths(new Date(), tenorValue), "MMM d, yyyy")} to ${format(
          new Date(),
          "MMM d, yyyy"
        )}`,
      },
    ],
    [applicationBank, tenorValue]
  );

  const closeModal = () => {
    reset();
    setError("");
    onClose();
  };

  useEffect(() => {
    onOpen();
  }, [onOpen]);

  useEffect(() => {
    register("bankStatement");
    register("bankId");
  }, [register]);

  useEffect(() => {
    if (success) {
      toast({
        title: "Bank Account Added",
        description:
          "Bank account added and bank statement submitted successfully",
        status: "success",
        duration: 5000,
        isClosable: true,
        position: "top-right",
      });
    }
  }, [success, toast]);

  const renderAccountInfo = () => (
    <Box
      textAlign='center'
      my={4}
      w='100%'
      bg='blackAlpha.100'
      borderRadius={6}
      p={4}
    >
      <Text>Upload a PDF bank statement for the account details below:</Text>
      <Flex
        flexDir='column'
        mx='auto'
        w='fit-content'
        textAlign='left'
      >
        <UnorderedList>
          {accountInfoData?.map((accountInfo, index) => (
            <ListItem key={index}>
              {accountInfo.label}:{" "}
              <Box
                as='span'
                fontWeight={700}
              >
                {accountInfo.value}
              </Box>
            </ListItem>
          ))}
        </UnorderedList>
      </Flex>
    </Box>
  );

  const renderBankSelection = () => (
    <FormControl isInvalid={!!errors?.bankId}>
      <FormHelperText fontSize='md'>
        If uploading for a different bank, please select the bank below.
      </FormHelperText>
      <Controller
        name='bankId'
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            placeholder='--Select a Bank--'
            label='Bank'
            isInvalid={!!errors?.bankId}
            errorMessage={errors?.bankId?.message}
          >
            {banks?.map((data, idx) => (
              <option
                key={idx}
                value={data?.id}
              >
                {data?.name}
              </option>
            ))}
          </Select>
        )}
      />
    </FormControl>
  );

  const renderFileUpload = () => (
    <FormControl
      isInvalid={!!errors.bankStatement}
      mb={4}
    >
      <Flex
        alignItems='left'
        flexDir='column'
        w='100%'
      >
        <FormLabel>Bank Statement Upload</FormLabel>
        <Box
          flex='1'
          w='100%'
          display='flex'
          flexDir='column'
        >
          <Box
            border='1px dashed'
            h='220px'
            w='100%'
            cursor='pointer'
            position='relative'
            overflow='hidden'
            borderRadius={6}
            borderColor={!!errors.bankStatement ? "red.500" : "gray.300"}
            _hover={{
              borderColor: !!errors.bankStatement ? "red.600" : "gray.400",
              bg: "gray.50",
            }}
          >
            <Input
              type='file'
              accept='application/pdf'
              position='absolute'
              top={0}
              left={0}
              height='100%'
              width='100%'
              opacity={0}
              cursor='pointer'
              onChange={handleFileChange}
              zIndex={2}
            />

            <Flex
              h='100%'
              alignItems='center'
              justifyContent='center'
              position='relative'
              zIndex={1}
              pointerEvents='none'
            >
              {selectedFile ? (
                <Flex
                  border='1px solid'
                  height='80%'
                  borderRadius={6}
                  width='50%'
                  p={4}
                  flexDirection='column'
                  alignItems='center'
                  justifyContent='center'
                  borderColor={!!errors.bankStatement ? "red.500" : "gray.300"}
                >
                  <Text
                    isTruncated
                    maxWidth='90%'
                  >
                    {selectedFile.name}
                  </Text>
                  <Text>{bytesToSize(selectedFile.size)}</Text>
                </Flex>
              ) : (
                <Flex
                  flexDirection='column'
                  alignItems='center'
                  justifyContent='center'
                  textAlign='center'
                >
                  <svg
                    width={80}
                    height={80}
                    viewBox='0 0 36 36'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M21 3H9a3 3 0 00-3 3v24a3 3 0 003 3h18a3 3 0 003-3V12l-9-9z'
                      stroke='#6E798F'
                      strokeWidth={1.5}
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                    <path
                      d='M24 19.5H12M24 25.5H12M15 13.5h-3'
                      stroke='#6E798F'
                      strokeWidth={1.5}
                      strokeLinecap='round'
                      strokeLinejoin='round'
                    />
                  </svg>
                  <Text
                    mt={2}
                    color='gray.600'
                    fontSize='sm'
                  >
                    Click to upload PDF file
                  </Text>
                </Flex>
              )}
            </Flex>
          </Box>
          <FormHelperText>
            Upload PDF file and file size should not exceed 10MB
          </FormHelperText>
          <FormErrorMessage>{errors?.bankStatement?.message}</FormErrorMessage>
        </Box>
      </Flex>
    </FormControl>
  );

  const renderPasswordInput = () => (
    <FormControl isInvalid={!!errors?.password}>
      <FormLabel>Password</FormLabel>
      <Input
        placeholder='Enter bank statement password'
        maxLength={6}
        {...register("password")}
      />
      <FormHelperText my={2}>
        Please enter document password (if any)
      </FormHelperText>
      <FormErrorMessage>{errors?.password?.message}</FormErrorMessage>
    </FormControl>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={closeModal}
      size='xl'
      isCentered
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Upload Bank Statement</ModalHeader>
        <ModalBody pb={8}>
          {error && (
            <Alert
              status='error'
              w='full'
              my={4}
              borderRadius={6}
            >
              <AlertIcon />
              <AlertDescription
                whiteSpace='pre-wrap'
                wordBreak='break-word'
              >
                {error}
              </AlertDescription>
            </Alert>
          )}

          <Box w='full'>
            <Text textAlign='center'>
              {`Please upload your bank statement for the last ${tenorValue} months`}
            </Text>

            {renderAccountInfo()}

            <Stack
              as='form'
              spacing={4}
              w='full'
              onSubmit={handleSubmit(onSubmit)}
            >
              {allowBankSelectionOnPdfUpload ? renderBankSelection() : null}
              {renderFileUpload()}
              {renderPasswordInput()}
              <Button
                type='submit'
                isLoading={isSubmitting}
                w='full'
                px={8}
              >
                Upload Statement
              </Button>
            </Stack>
          </Box>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default UploadBankStatement;
