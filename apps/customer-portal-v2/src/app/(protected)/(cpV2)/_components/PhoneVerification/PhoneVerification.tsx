"use client";
import { NumericInput } from "@/components/forms";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { PhoneOTPType, PhoneOTPValidationSchema } from "@/src/schema";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Stack,
  Text,
} from "@chakra-ui/react";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

type Props = {
  applicationNumber: string;
  triggerOtp: () => Promise<
    | {
        success: boolean;
        error?: undefined;
      }
    | {
        success: boolean;
        error: any;
      }
    | undefined
  >;
  confirmPhone: ({ code }: { code: string }) => Promise<
    | {
        success: boolean;
        redirect: boolean;
        error?: undefined;
      }
    | {
        success: boolean;
        redirect?: undefined;
        error?: undefined;
      }
    | {
        success: boolean;
        error: any;
        redirect?: undefined;
      }
    | undefined
  >;
  createAppTrace: ({
    page,
    comment,
  }: {
    page: string;
    comment: string;
  }) => Promise<any>;
  maskedPhone: string;
  bvnPhone: string;
};

const PhoneVerification = ({
  applicationNumber,
  confirmPhone,
  createAppTrace,
  triggerOtp,
  maskedPhone,
  bvnPhone,
}: Props) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath;

  const { error, setError, setSuccess, success } = useMessageTimer();
  const [isResending, setIsResending] = useState(false);

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<PhoneOTPType>({
    resolver: zodResolver(PhoneOTPValidationSchema),
    mode: "onChange",
  });

  useEffect(() => {
    const phoneOtpStatus = localStorage.getItem("phoneOtpRequested");
    if (!phoneOtpStatus) {
      sendInitialOtp();
    }
  }, []);

  const sendInitialOtp = async () => {
    try {
      setError("");
      const result = await triggerOtp();
      if (result?.success) {
        localStorage.setItem("phoneOtpRequested", "true");
        setSuccess(`OTP sent to 0${bvnPhone} successfully`);
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to send OTP");
    }
  };

  const resendOtp = async () => {
    try {
      setError("");
      setIsResending(true);
      const result = await triggerOtp();

      if (result?.success) {
        localStorage.setItem("phoneOtpRequested", "true");
        setSuccess(`OTP resent to 0${bvnPhone} successfully`);
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to resend OTP");
    } finally {
      setIsResending(false);
    }
  };

  const onSubmit = async (data: PhoneOTPType) => {
    try {
      setError("");
      const result = await confirmPhone({
        code: data?.code.trim(),
      });

      if (result?.success) {
        localStorage.removeItem("phoneOtpRequested");

        if (result.redirect) {
          if (createAppTrace) {
            await createAppTrace({
              page: "Phone Verification",
              comment: `Phone verification completed (${maskedPhone})`,
            });
          }
          const path = basePath
            ? `/${basePath}/application/${applicationNumber}`
            : `/application/${applicationNumber}`;
          router.push(path);
        }

        reset();
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to verify email");
    }
  };

  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={2}
        textAlign='center'
        data-testid='phone-verification-heading'
      >
        Phone Number Verification
      </Heading>
      <Text
        mb={6}
        textAlign='center'
        data-testid='phone-verification-description'
      >
        {`Please enter verification code sent to`} We have sent a verification
        code to the phone number (<b>{maskedPhone}</b>) linked to your bvn via{" "}
        <b>SMS</b>. Please enter the 6-digit code below.
      </Text>

      {error && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='phone-verification-error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {error}
          </AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert
          status='success'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='phone-verification-success-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {success}
          </AlertDescription>
        </Alert>
      )}

      <Stack
        as='form'
        spacing={8}
        w='full'
        onSubmit={handleSubmit(onSubmit)}
      >
        <FormControl isInvalid={!!errors?.code}>
          <FormLabel>Verification Code</FormLabel>
          <Controller
            name='code'
            control={control}
            defaultValue=''
            render={({ field }) => (
              <NumericInput
                maxLength={6}
                allowLeadingZeros
                placeholder='Enter Verification Code'
                thousandSeparator={false}
                value={field.value || ""}
                onChange={field.onChange}
                disabled={isResending || isSubmitting}
                onBlur={field.onBlur}
                isInvalid={!!errors?.code?.message}
                autoFocus
                data-testid='phone-verification-code-input'
              />
            )}
          />
          <FormErrorMessage
            fontSize='sm'
            data-testid='phone-verification-code-error-message'
          >
            {errors?.code?.message}
          </FormErrorMessage>
        </FormControl>

        <Flex
          justifyContent='center'
          alignItems='center'
          flexDirection='column'
        >
          <Text
            textAlign='center'
            mb={2}
          >
            Didn't get OTP?
          </Text>
          <Flex alignItems='center'>
            <Button
              variant='link'
              onClick={resendOtp}
              mx={1}
              isLoading={isResending}
              loadingText='Sending...'
              disabled={isResending}
              colorScheme='customPrimary'
              _hover={{
                textDecoration: "underline",
                color: "customPrimary.600",
              }}
              data-testid='phone-verification-resend-otp-button'
            >
              Resend OTP
            </Button>
          </Flex>

          <Text
            as='span'
            size='xs'
            my={4}
            textAlign='center'
          >
            <b>NOTE:</b> We've sent the code! Please check your phone's SMS
            settings to ensure SMS filtering is turned off. Also, double-check
            if any apps might be blocking or filtering messages from us.
          </Text>
        </Flex>

        <Button
          type='submit'
          isLoading={isSubmitting}
          w='full'
          px={8}
          data-testid='phone-verification-submit-button'
        >
          Next
        </Button>
      </Stack>
    </Box>
  );
};

export default PhoneVerification;
