"use client";
import {
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Link,
  Stack,
  Text,
  Alert,
  Flex,
  Checkbox,
  FormErrorMessage,
} from "@chakra-ui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, Controller } from "react-hook-form";
import { signIn } from "next-auth/react";
import { useParams, useRouter } from "next/navigation";
import { createRegisterSchema, RegisterFormValues } from "@/src/schema";
import { PasswordInput } from "@/components/PasswordInput";
import { isAbsolutePath } from "@/utils/index";
import { NumericInput } from "@/components/forms";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { ClientInfo } from "@/__generated/graphql";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { useEffect, useState } from "react";
import { FullPageLoader } from "@/components/FullPageLoader";

export default function RegisterPage() {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;
  const { error, setError } = useMessageTimer();
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const {
    name,
    clientId,
    howToApplyVideoLink,
    privacyPolicyUrl,
    termsAndConditionsUrl,
  } = clientInfo || {};

  const registerSchema = createRegisterSchema({
    needsTerms: !!termsAndConditionsUrl,
    needsPrivacyPolicy: !!privacyPolicyUrl,
  });
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
  } = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    mode: "onChange",
  });

  const onSubmit = async (data: RegisterFormValues) => {
    setError("");

    try {
      const result = await signIn("credentials", {
        email: data?.email,
        phone: data?.phoneNumber.substring(1),
        password: data?.password,
        clientId,
        action: "register",
        redirect: false,
      });

      if (result?.error) {
        setError(result?.error);
      } else {
        const path = basePath
          ? `/${basePath}/sign-up/minimum-requirements`
          : `/sign-up/minimum-requirements`;
        router.push(path);
      }
    } catch (error: any) {
      throw new Error("Registration failed");
    }
  };

  if (!clientInfo) return <FullPageLoader />;
  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      borderRadius='lg'
      py={8}
      px={{ base: 4, md: 8 }}
      my={8}
    >
      <Heading
        as='h2'
        fontSize={{ base: "20px", md: "24px" }}
        mb={2}
        textAlign='center'
        data-testid='title'
      >{`Welcome to ${name}`}</Heading>
      <Text
        mb={6}
        textAlign='center'
      >
        New to {name}? Create an account to start applying for loans.
      </Text>
      {error && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {error}
          </AlertDescription>
        </Alert>
      )}

      <Stack
        spacing={6}
        pt={6}
        as='form'
        onSubmit={handleSubmit(onSubmit)}
      >
        <Stack spacing={4}>
          <FormControl isInvalid={!!errors.email}>
            <FormLabel>Email</FormLabel>
            <Input
              type='email'
              placeholder='Enter your email address'
              {...register("email")}
              disabled={isSubmitting}
              data-testid='email-input'
            />
            <FormErrorMessage
              fontSize='sm'
              data-testid='email-error'
            >
              {errors?.email?.message}
            </FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.phoneNumber}>
            <FormLabel mb='-2px'>Phone Number</FormLabel>
            <Controller
              name='phoneNumber'
              control={control}
              defaultValue=''
              render={({ field }) => (
                <NumericInput
                  maxLength={11}
                  allowLeadingZeros
                  placeholder='Enter your phone number'
                  thousandSeparator={false}
                  value={field.value || ""}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  isInvalid={!!errors?.phoneNumber?.message}
                  isPhone
                  disabled={isSubmitting}
                  data-testid='phone-input'
                />
              )}
            />
            <FormErrorMessage
              fontSize='sm'
              data-testid='phone-error'
            >
              {errors?.phoneNumber?.message}
            </FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.password}>
            <FormLabel>Password</FormLabel>
            <PasswordInput
              register={register}
              placeholder='Enter your password'
              name='password'
              disabled={isSubmitting}
              data-testid='password-input'
            />

            <FormErrorMessage
              fontSize='sm'
              data-testid='password-error'
            >
              {errors?.password?.message}
            </FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.confirmPassword}>
            <FormLabel>Confirm Password</FormLabel>

            <PasswordInput
              register={register}
              placeholder='Confirm your password'
              name='confirmPassword'
              disabled={isSubmitting}
              data-testid='confirm-password-input'
            />

            <FormErrorMessage
              fontSize='sm'
              data-testid='confirm-password-error'
            >
              {errors?.confirmPassword?.message}
            </FormErrorMessage>
          </FormControl>

          {termsAndConditionsUrl && (
            <FormControl isInvalid={!!errors.terms}>
              <FormLabel
                htmlFor='terms'
                m={0}
              >
                <Flex
                  alignItems='flex-start'
                  gap={2}
                >
                  <Checkbox
                    id='terms'
                    {...register("terms")}
                    isInvalid={!!errors.terms}
                    mt={1}
                    disabled={isSubmitting}
                    data-testid='terms-checkbox'
                  />

                  <Text
                    whiteSpace='pre-wrap'
                    as='span'
                    data-testid='terms-text'
                  >
                    I agree to the
                    <Text as='span'>
                      <Link
                        href={isAbsolutePath(termsAndConditionsUrl)}
                        color='customPrimary.500'
                        isExternal
                        fontWeight='bold'
                        _visited={{
                          color: "customPrimary.500",
                        }}
                        _hover={{
                          color: "customPrimary.600",
                        }}
                        mx={1}
                        data-testid='terms-link'
                      >
                        Terms and Conditions
                      </Link>
                      of this loan.
                    </Text>
                  </Text>
                </Flex>
              </FormLabel>

              <FormErrorMessage
                fontSize='sm'
                data-testid='terms-error'
              >
                {errors?.terms?.message}
              </FormErrorMessage>
            </FormControl>
          )}

          {privacyPolicyUrl && (
            <FormControl isInvalid={!!errors.privacyPolicy}>
              <FormLabel
                htmlFor='policy'
                m={0}
              >
                <Flex
                  alignItems='flex-start'
                  gap={2}
                >
                  <Checkbox
                    id='policy'
                    {...register("privacyPolicy")}
                    isInvalid={!!errors.privacyPolicy}
                    mt={1}
                    disabled={isSubmitting}
                    data-testid='privacy-policy-checkbox'
                  />

                  <Text
                    whiteSpace='pre-wrap'
                    as='span'
                    data-testid='privacy-policy-text'
                  >
                    We are NDPR Compliant. By using this form, you agree to the
                    storage and usage of your data by Originate Credit in
                    accordance with our
                    <Text as='span'>
                      <Link
                        href={isAbsolutePath(privacyPolicyUrl)}
                        color='customPrimary.500'
                        isExternal
                        fontWeight='bold'
                        _visited={{
                          color: "customPrimary.500",
                        }}
                        _hover={{
                          color: "customPrimary.600",
                        }}
                        ml={1}
                        data-testid='privacy-policy-link'
                      >
                        Privacy Policy
                      </Link>
                      .
                    </Text>
                  </Text>
                </Flex>
              </FormLabel>

              <FormErrorMessage
                fontSize='sm'
                data-testid='privacy-policy-error'
              >
                {errors?.privacyPolicy?.message}
              </FormErrorMessage>
            </FormControl>
          )}

          <Button
            type='submit'
            isLoading={isSubmitting}
            my={6}
            data-testid='register-button'
          >
            Start Application
          </Button>
        </Stack>
      </Stack>

      {howToApplyVideoLink && (
        <Flex
          alignItems='center'
          justifyContent='center'
          gap={2}
        >
          <Text
            whiteSpace='pre-wrap'
            as='span'
            textAlign='center'
          >
            Want to know how to apply for loans? Click here to
            <Text as='span'>
              <Link
                href={isAbsolutePath(howToApplyVideoLink)}
                color='customPrimary.500'
                isExternal
                fontWeight='bold'
                _visited={{
                  color: "customPrimary.500",
                }}
                _hover={{
                  color: "customPrimary.600",
                }}
                ml={1}
                data-testid='how-to-apply-link'
              >
                learn
              </Link>
              .
            </Text>
          </Text>
        </Flex>
      )}
    </Box>
  );
}
