"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ing,
  RadioGroup,
  VStack,
  Flex,
  Alert,
  AlertIcon,
  AlertDescription,
  Text,
  Stack,
} from "@chakra-ui/react";
import React, { useEffect, useRef, useState } from "react";
import { Controller, FormProvider, useForm } from "react-hook-form";
import {
  DebitCard,
  RepaymentTypeList,
  PayWithDifferentMethod,
} from "./_components";
import {
  Application,
  ClientInfo,
  CreatePaystackCardRepaymentInput,
  CreatePaystackReferenceRepaymentInput,
  GenerateAddCardReferenceInput,
  Viewer,
} from "@/__generated/graphql";
import {
  formatAmount,
  getAttributrFromProduct,
  parseNumericValue,
} from "@/utils/index";
import { NumericInput } from "@/components/forms";
import * as LucideIcons from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";
import useRepaymentService from "../../_services/Repayments/useRepaymentService";

const { CheckCircle, Frown, TriangleAlert } = LucideIcons;

type Props = {
  generateByPayStackRef: (params: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => Promise<
    { data: any; error: null } | { data: null; error: any } | undefined
  >;
  viewer: Viewer;
  makePaystackCardRepayment: (params: {
    amount: CreatePaystackCardRepaymentInput["amount"];
  }) => Promise<
    { data: any; error: null } | { data: null; error: any } | undefined
  >;
  makePaystackRefRepayment: (params: {
    paystackReference: CreatePaystackReferenceRepaymentInput["paystackReference"];
  }) => Promise<
    { data: any; error: null } | { data: null; error: any } | undefined
  >;
  getByPayStackRef: (params: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => Promise<
    { data: any; error: null } | { data: null; error: any } | undefined
  >;
};

export type RepaymentFormValues = {
  repaymentTypeId: string;
  defaultCard: string;
  repaymentAmount?: number;
};

const RepayLoan = ({
  viewer,
  generateByPayStackRef,
  makePaystackCardRepayment,
  makePaystackRefRepayment,
  getByPayStackRef,
}: Props) => {
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;
  const refs = useRef<{ [key: string]: HTMLElement | null }>({});

  const latestApplication = viewer?.account?.applications
    ?.nodes?.[0] as Application;

  const defaultCard = viewer?.account?.cards?.find(
    (card) => card?.isDefault === true
  );

  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const methods = useForm<RepaymentFormValues>({
    mode: "onChange",
    defaultValues: {
      repaymentTypeId: "repayFull",
      defaultCard: defaultCard?.id!,
    },
  });
  const {
    control,
    watch,
    handleSubmit,
    setValue,
    reset,
    formState: { errors, isSubmitting: isSending },
  } = methods;

  const {
    cards,
    repaymentTypes,
    loan,
    loading,
    selectedCard,
    setPaymentType,
    paymentType,
    handlePaystackWidgetTrigger,
    isSubmitting,
    error,
    getPayStackRefData,
    reference,
    makeManualRepayment,
    repaymentComplete,
    amountPaid,
  } = useRepaymentService({
    viewer,
    generateByPayStackRef,
    makePaystackCardRepayment,
    makePaystackRefRepayment,
    methods,
    getByPayStackRef,
    clientInfo,
  });

  useEffect(() => {
    if (error && typeof window !== "undefined") {
      window.scrollTo(0, 0);
    }
  }, [error]);

  const onSubmit = async () => {
    const res = await makeManualRepayment();
    if (res?.data) {
      reset();
    }
  };

  const selectedTypeId = watch("repaymentTypeId");
  const isLoading = loading || isSending || isSubmitting;
  const namesSet = new Set(["noCardImplementation"]);
  const loanCategoryAttributes =
    latestApplication?.loanCategory?.loanCategoryAttributes!;
  const applicationAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const noCardImplementation = !!applicationAttributes?.noCardImplementation;
  const color = clientInfo?.clientTheme?.primaryColor!;

  useEffect(() => {
    if (defaultCard?.id) {
      const selectedElement = refs.current[defaultCard?.id];
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }
  }, [defaultCard?.id]);

  const dashboardUrl = basePath ? `/${basePath}/dashboard` : `/dashboard`;

  if (noCardImplementation) {
    return (
      <Box
        p={6}
        borderRadius='md'
        boxShadow='sm'
        bg='white'
        w={{ base: "full", md: "2xl" }}
      >
        <VStack
          gap={8}
          mt={8}
        >
          <TriangleAlert
            width={90}
            height={90}
            stroke='#FF0000'
          />
          <Text
            fontSize='lg'
            fontWeight='bold'
          >
            Sorry, you're not allowed to access this page.
          </Text>
          <Button
            as={Link}
            mt={4}
            colorScheme='customBrand'
            w='full'
            href={dashboardUrl}
            _hover={{
              color: "white",
              textDecoration: "none",
              bg: "customBrand.600",
            }}
            _visited={{
              color: "white",
            }}
          >
            Go to your Dashboard
          </Button>
        </VStack>
      </Box>
    );
  }

  if (repaymentComplete) {
    return (
      <Box
        p={6}
        borderRadius='md'
        boxShadow='sm'
        bg='white'
        w={{ base: "full", md: "2xl" }}
      >
        <Heading
          as='h2'
          fontSize='24px'
          mb={6}
          textAlign='center'
        >
          Payment Successful
        </Heading>
        <VStack
          gap={8}
          mt={16}
        >
          <CheckCircle
            width={90}
            height={90}
            stroke='#008000'
          />
          <Text
            fontSize='lg'
            fontWeight='bold'
          >
            {amountPaid
              ? `You have successfully repaid ${formatAmount(amountPaid)}`
              : "You have successfully made a repayment"}
          </Text>
          <Button
            as={Link}
            mt={4}
            colorScheme='customBrand'
            w='full'
            href={dashboardUrl}
            _hover={{
              color: "white",
              textDecoration: "none",
              bg: "customBrand.600",
            }}
            _visited={{
              color: "white",
            }}
          >
            Go to your Dashboard
          </Button>
        </VStack>
      </Box>
    );
  }

  if (!loan) {
    return (
      <Box
        p={6}
        borderRadius='md'
        boxShadow='sm'
        bg='white'
        w={{ base: "full", md: "2xl" }}
      >
        <Heading
          as='h2'
          fontSize='24px'
          mb={6}
          textAlign='center'
        >
          {"Make Repayment"}
        </Heading>
        <VStack
          gap={8}
          mt={16}
        >
          <Frown
            width={90}
            height={90}
            stroke={color}
          />
          <Text
            fontSize='lg'
            fontWeight='bold'
          >
            You currently do not have an active loan.
          </Text>
          <Button
            as={Link}
            mt={4}
            colorScheme='customBrand'
            w='full'
            href={dashboardUrl}
            _hover={{
              color: "white",
              textDecoration: "none",
              bg: "customBrand.600",
            }}
            _visited={{
              color: "white",
            }}
          >
            Go to your Dashboard
          </Button>
        </VStack>
      </Box>
    );
  }

  if (!clientInfo) return <FullPageLoader />;

  return (
    <Box
      p={6}
      borderRadius='md'
      boxShadow='sm'
      bg='white'
      w={{ base: "full", md: "2xl" }}
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={6}
        textAlign='center'
      >
        Make Repayment
      </Heading>

      {error && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {error}
          </AlertDescription>
        </Alert>
      )}

      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <VStack
            spacing={4}
            mt={3}
            align='stretch'
          >
            <Heading
              as='h3'
              fontSize='17px'
            >
              Choose Repayment Amount
            </Heading>

            {repaymentTypes.map((repaymentType) => (
              <RepaymentTypeList
                key={repaymentType.id}
                repaymentType={repaymentType}
                loading={isLoading}
                isSelected={selectedTypeId === repaymentType?.id}
              />
            ))}

            {selectedTypeId === "repayCustom" && (
              <Controller
                name='repaymentAmount'
                control={control}
                rules={{
                  required: "Amount is required",
                  validate: (value) => {
                    if (!value) return "Please enter an valid amount";
                    if (value > loan?.fullAmount)
                      return `Maximum repayment amount must be equal to or less than ${formatAmount(loan?.fullAmount - loan?.amountPaid!)}`;
                    return true;
                  },
                }}
                render={({ field }) => (
                  <NumericInput
                    {...field}
                    label='Amount'
                    isAmountInput
                    errorMessage={errors?.repaymentAmount?.message}
                    isInvalid={!!errors?.repaymentAmount}
                    onChange={(e) =>
                      field.onChange(parseNumericValue(e.target.value))
                    }
                    disabled={isLoading}
                  />
                )}
              />
            )}

            <Heading
              as='h3'
              fontSize='17px'
              mt={6}
            >
              Choose Card
            </Heading>

            <RadioGroup
              value={selectedCard}
              onChange={(value) => setValue("defaultCard", value)}
              isDisabled={isLoading}
            >
              <Flex
                gap={4}
                flexWrap='wrap'
              >
                <Stack
                  maxH='250px'
                  overflowY='auto'
                  border='1px solid'
                  borderColor='gray.200'
                  p={4}
                  borderRadius={6}
                >
                  {cards?.map((card) => (
                    <DebitCard
                      cardDetails={card!}
                      key={card?.id}
                      setPaymentType={setPaymentType}
                      refs={refs}
                      defaultCardId={defaultCard?.id!}
                    />
                  ))}
                </Stack>
                <PayWithDifferentMethod setPaymentType={setPaymentType} />
              </Flex>
            </RadioGroup>

            {!loading && !reference && (
              <Alert
                status='error'
                w='full'
                my={4}
                borderRadius={6}
              >
                <AlertIcon />
                <AlertDescription
                  whiteSpace='pre-wrap'
                  wordBreak='break-word'
                >
                  There was an error generating payment reference.
                  <Button
                    variant='link'
                    ml={2}
                    color='red.500'
                    onClick={getPayStackRefData}
                  >
                    Please, retry
                  </Button>
                </AlertDescription>
              </Alert>
            )}

            {paymentType === "payWtDiffMethod" ? (
              <Button
                mt={4}
                colorScheme='blue'
                isLoading={isLoading}
                w='full'
                isDisabled={!reference}
                onClick={handlePaystackWidgetTrigger}
              >
                Make Payment
              </Button>
            ) : (
              <Button
                mt={4}
                colorScheme='blue'
                type='submit'
                isLoading={isLoading}
                w='full'
              >
                Make Payment
              </Button>
            )}
          </VStack>
        </form>
      </FormProvider>
    </Box>
  );
};

export default RepayLoan;
