"use client";

import { Box, Flex, Text, Radio, Icon } from "@chakra-ui/react";
import { useFormContext } from "react-hook-form";
import MasterCard from "@/components/Icons/MasterCard";
import VisaCard from "@/components/Icons/VisaCard";
import VerveCard from "@/components/Icons/VerveCard";
import { Card } from "@/__generated/graphql";
import React from "react";

interface DebitCardProps {
  cardDetails: Card;
  setPaymentType: (type: string) => void;
  refs: React.MutableRefObject<{
    [key: string]: HTMLElement | null;
  }>;
  defaultCardId: string;
}

const CARD_ICONS = {
  master: MasterCard,
  visa: VisaCard,
  verve: VerveCard,
} as const;

const DebitCard = ({
  cardDetails,
  setPaymentType,
  refs,
  defaultCardId,
}: DebitCardProps) => {
  const { maskedPan, expiryDate, type, id, isDefault } = cardDetails ?? {};
  const { register, setValue, watch } = useFormContext();
  const selectedCard = watch("defaultCard");

  const CardIcon =
    type && type in CARD_ICONS
      ? CARD_ICONS[type as keyof typeof CARD_ICONS]
      : undefined;

  const handleClick = () => {
    setPaymentType(id);
    setValue("defaultCard", id);
  };

  const isSelected = selectedCard === id;

  return (
    <Box
      p={4}
      borderWidth={1}
      borderRadius={6}
      cursor='pointer'
      onClick={handleClick}
      w={{ base: "100%", md: "350px" }}
      bgColor={isSelected ? "customPrimary.600" : "customPrimary.500"}
      as='label'
      htmlFor={id}
    >
      <Flex
        justifyContent='space-between'
        mb={4}
      >
        <Radio
          value={id}
          id={id}
          {...register("defaultCard")}
          colorScheme='customBrand'
          border='3px solid white'
          isChecked={isSelected}
          ref={(el) => {
            if (defaultCardId) {
              refs.current[defaultCardId] = el;
            }
          }}
        >
          <Text
            color='white'
            fontWeight='600'
            textTransform='uppercase'
          >
            Repayment Card {isDefault ? "(Default)" : ""}
          </Text>
        </Radio>
      </Flex>

      <Flex
        gap={2}
        alignItems='center'
        mb={4}
      >
        <Flex
          gap={{ base: 3, md: 8 }}
          alignItems='center'
          color='white'
          fontSize='lg'
        >
          {Array.from({ length: 3 }, (_, setIndex) => (
            <Flex
              key={`set-${setIndex}`}
              gap={{ base: 1, md: 2 }}
            >
              {Array.from({ length: 4 }, (_, i) => (
                <Box
                  key={`dot-${setIndex}-${i}`}
                  w={2}
                  h={2}
                  borderRadius='full'
                  bg='white'
                />
              ))}
            </Flex>
          ))}
          <Text fontWeight='600'>{maskedPan?.slice(-4)}</Text>
        </Flex>
      </Flex>

      <Flex
        justifyContent='space-between'
        alignItems='center'
      >
        {expiryDate && (
          <Text
            fontSize='md'
            color='white'
            fontWeight='600'
          >
            Expires:{" "}
            <Text
              as='span'
              ml={2}
            >
              {expiryDate.replace("-", "/")}
            </Text>
          </Text>
        )}
        <Icon
          as={CardIcon}
          w={{ base: 14, md: 20, lg: 24 }}
          h={{ base: 10, md: 12 }}
        />
      </Flex>
    </Box>
  );
};
export default DebitCard;
