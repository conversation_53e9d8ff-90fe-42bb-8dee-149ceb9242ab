"use client";

import { Box, Text, Radio, Icon, VStack } from "@chakra-ui/react";
import { useFormContext } from "react-hook-form";
import { PlusCircleIcon } from "lucide-react";

interface PayWithDifferentMethodProps {
  setPaymentType: (type: string) => void;
}

const PayWithDifferentMethod = ({
  setPaymentType,
}: PayWithDifferentMethodProps) => {
  const {
    formState: { touchedFields, errors },
    setValue,
    register,
    watch,
  } = useFormContext();

  const selectedCard = watch("defaultCard");
  const isSelected = selectedCard === "payWtDiffMethod";
  const isDisabled = Boolean(touchedFields.amount && errors.amount);

  const handleClick = () => {
    if (!isDisabled) {
      setPaymentType("payWtDiffMethod");
      setValue("defaultCard", "payWtDiffMethod");
    }
  };

  return (
    <Box
      p={4}
      borderWidth={1}
      borderRadius={6}
      _hover={{ borderColor: isDisabled ? "gray.200" : "blue.300" }}
      cursor={isDisabled ? "not-allowed" : "pointer"}
      onClick={handleClick}
      opacity={isDisabled ? 0.5 : 1}
      w={{ base: "100%", md: "350px" }}
      bgColor={isSelected ? "customBrand.600" : "customBrand.500"}
      color='white'
      as='label'
      htmlFor='payWtDiffMethod'
    >
      <Radio
        value='payWtDiffMethod'
        id='payWtDiffMethod'
        colorScheme='customPrimary'
        isDisabled={isDisabled}
        isChecked={isSelected}
        {...register("defaultCard")}
        border='3px solid white'
      />
      <VStack
        gap={4}
        mb={6}
      >
        <Icon
          as={PlusCircleIcon}
          boxSize={6}
        />
        <Text fontWeight='medium'>Pay with a different method</Text>
      </VStack>
    </Box>
  );
};

export default PayWithDifferentMethod;
