"use client";
import { Flex, Radio, RadioGroup, Text } from "@chakra-ui/react";
import { Controller, useFormContext } from "react-hook-form";
import { formatAmount } from "src/utils";
import { RepaymentType } from "../../../../_services";
import { RepaymentFormValues } from "../../RepayLoan";

type RepaymentTypeListProps = {
  repaymentType: RepaymentType;
  loading: boolean;
  isSelected: boolean;
};

const RepaymentTypeList = ({
  repaymentType,
  loading,
  isSelected,
}: RepaymentTypeListProps) => {
  const { control, setValue } = useFormContext<RepaymentFormValues>();

  const handleSelect = () => {
    setValue("repaymentTypeId", repaymentType.id);
  };

  return (
    <Controller
      name='repaymentTypeId'
      control={control}
      render={({ field }) => (
        <RadioGroup
          {...field}
          onChange={(selectedValue) => {
            field.onChange(selectedValue);
          }}
          border='1px solid'
          borderColor={isSelected ? "customBrand.400" : "gray.200"}
          borderRadius={6}
          w='100%'
          lineHeight='18px'
          _hover={{
            borderColor: "customBrand.400",
          }}
          onClick={handleSelect}
        >
          <Flex>
            <Radio
              value={repaymentType.id}
              w='100%'
              p={3}
              flexDirection='row'
              isChecked={isSelected}
              isDisabled={loading}
            >
              <Text fontWeight='semibold'>
                {repaymentType.amount > 0
                  ? `${repaymentType.name} - ${formatAmount(repaymentType.amount)}`
                  : repaymentType.name}
              </Text>
            </Radio>
          </Flex>
        </RadioGroup>
      )}
    />
  );
};

export default RepaymentTypeList;
