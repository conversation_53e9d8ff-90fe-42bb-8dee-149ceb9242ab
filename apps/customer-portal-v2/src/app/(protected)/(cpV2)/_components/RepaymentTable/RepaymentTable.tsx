"use client";

import { <PERSON><PERSON><PERSON>, Repayment, Viewer } from "@/__generated/graphql";
import { Flex, Stack, useMediaQuery } from "@chakra-ui/react";
import React, { Suspense } from "react";
import { ListItem } from "./_components/ListItem";
import { ListItemSkeleton } from "./_components/ListItemSkeleton";
import { ListRow } from "./_components/ListRow";
import { LoanApplicationsEmpty } from "./_components/LoanApplicationsEmpty";

const RepaymentTable = ({ viewer }: { viewer: Viewer }) => {
  const [isMeduimDevice] = useMediaQuery("(min-width: 1024px)");
  const currentLoan = viewer?.account?.portfolios?.nodes?.find(
    (status: any) => status.name !== "CLOSED"
  ) as Portfolio;

  const repayments = currentLoan?.repayments as Repayment[];
  const dataCount = repayments?.length;

  return (
    <Stack
      spacing={0}
      flex={1}
      border='1px solid'
      borderColor='gray.200'
      borderRadius={6}
      minH='max-content'
    >
      <>
        {isMeduimDevice && (
          <ListRow isHeader>
            <Flex ml={{ base: 0, md: 6 }}>Status</Flex>
            <Flex>Principal</Flex>
            <Flex>Interest</Flex>
            <Flex>Repayment Amount</Flex>
            <Flex>Due Date</Flex>
          </ListRow>
        )}
        <Suspense
          fallback={
            <Stack>
              {[...Array(5)].map((_, i) => (
                <ListItemSkeleton key={i} />
              ))}
            </Stack>
          }
        >
          <Stack spacing={0}>
            {dataCount === 0 ? (
              <LoanApplicationsEmpty />
            ) : (
              <>
                {repayments?.map((repayment) => (
                  <ListItem
                    key={repayment?.id}
                    repayment={repayment}
                  />
                ))}
              </>
            )}
          </Stack>
        </Suspense>
      </>
    </Stack>
  );
};

export default RepaymentTable;
