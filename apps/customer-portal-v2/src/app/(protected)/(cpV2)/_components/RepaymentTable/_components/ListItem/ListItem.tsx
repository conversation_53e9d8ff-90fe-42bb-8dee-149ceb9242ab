"use client";
import { formatAmount } from "@/utils/index";
import { Repayment } from "@/__generated/graphql";
import {
  Badge,
  Flex,
  Text,
  ThemeTypings,
  useDisclosure,
  useMediaQuery,
} from "@chakra-ui/react";
import { format } from "date-fns";
import React from "react";
import { ListRow } from "../ListRow";

type ListItemProps = {
  repayment: Repayment;
};
const ListItem = ({ repayment }: ListItemProps) => {
  const renderStatus = (
    status: string
  ): { label: string; color: ThemeTypings["colorSchemes"] } => {
    switch (status) {
      case "PAID":
        return { label: "Paid", color: "green" };
      case "SUSPENDED":
        return { label: "Suspended", color: "yellow" };
      case "PENDING":
        return { label: "Pending", color: "blue" };
      case "CANCELLED":
        return { label: "Cancelled", color: "red" };
      default:
        return { label: "Pending", color: "blue" };
    }
  };

  const { onOpen, onClose } = useDisclosure();
  const [isSmallDevice] = useMediaQuery("(max-width: 768px)");

  return (
    <Flex>
      <ListRow>
        {/* Status */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          ml={{ base: 0, lg: 6 }}
          _before={
            isSmallDevice
              ? {
                  content: "'Status'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Badge
            h={6}
            px={2}
            fontSize='xs'
            w='fit-content'
            display='flex'
            variant='solid'
            textAlign='center'
            alignItems='center'
            borderRadius={6}
            justifyContent='center'
            color={`${renderStatus(repayment?.status?.name)?.color}.500`}
            bg={`${renderStatus(repayment?.status?.name)?.color}.100`}
          >
            {renderStatus(repayment?.status?.name)?.label}
          </Badge>
        </Flex>

        {/* Principal */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Principal'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>{formatAmount(repayment?.principalPortion!)}</Text>
        </Flex>

        {/* Interest */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Interest'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>{formatAmount(repayment?.interestPortion!)}</Text>
        </Flex>

        {/* Repayment Amount */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Repayment Amount'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>{formatAmount(repayment?.outstandingPayment!)}</Text>
        </Flex>

        {/* Due Date */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Due Date'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>
            {(repayment?.dueDate &&
              format(repayment?.dueDate, "MMM d, yyyy")) ||
              "N/A"}
          </Text>
        </Flex>
      </ListRow>
    </Flex>
  );
};
export default ListItem;
