"use client";

import { Flex, Skeleton, SkeletonProps, useMediaQuery } from "@chakra-ui/react";
import React from "react";
import { ListRow } from "../ListRow";

const ListItemSkeleton = () => {
  const [isSmallDevice] = useMediaQuery("(max-width: 767px)");

  return (
    <ListRow>
      <Flex
        alignItems='center'
        justifyContent={{ base: "space-between", lg: "center" }}
        _before={
          isSmallDevice
            ? {
                content: "'Status'",
                color: "gray.600",
                fontSize: "sm",
                fontWeight: "medium",
              }
            : undefined
        }
      >
        <Flex
          flex={1}
          flexDir='column'
          alignItems={{ base: "flex-end", md: "flex-start" }}
        >
          <CustomSkeleton
            h={6}
            w={{ base: "30%", md: 16 }}
          />
        </Flex>
      </Flex>

      <Flex
        alignItems='center'
        justifyContent={{ base: "space-between", lg: "left" }}
        _before={
          isSmallDevice
            ? {
                content: "'Principal'",
                color: "gray.600",
                fontSize: "sm",
                fontWeight: "medium",
              }
            : undefined
        }
      >
        <Flex
          gap={1}
          flex={1}
          flexDir='column'
          alignItems={{ base: "flex-end", md: "flex-start" }}
        >
          <CustomSkeleton
            h={4}
            w={{ base: "30%", md: "60%" }}
          />
        </Flex>
      </Flex>

      <Flex
        alignItems='center'
        justifyContent={{ base: "space-between", lg: "left" }}
        _before={
          isSmallDevice
            ? {
                content: "'Interest'",
                color: "gray.600",
                fontSize: "sm",
                fontWeight: "medium",
              }
            : undefined
        }
      >
        <Flex
          gap={1}
          flex={1}
          flexDir='column'
          alignItems={{ base: "flex-end", md: "flex-start" }}
        >
          <CustomSkeleton
            h={4}
            w={{ base: "30%", md: "60%" }}
          />
        </Flex>
      </Flex>

      <Flex
        alignItems='center'
        justifyContent={{ base: "space-between", lg: "left" }}
        _before={
          isSmallDevice
            ? {
                content: "'Repayment Amount'",
                color: "gray.600",
                fontSize: "sm",
                fontWeight: "medium",
              }
            : undefined
        }
      >
        <Flex
          gap={1}
          flex={1}
          flexDir='column'
          alignItems={{ base: "flex-end", md: "flex-start" }}
        >
          <CustomSkeleton
            h={4}
            w={{ base: "30%", md: "60%" }}
          />
        </Flex>
      </Flex>

      <Flex
        alignItems='center'
        justifyContent={{ base: "space-between", lg: "right" }}
        _before={
          isSmallDevice
            ? {
                content: "'Due Date'",
                color: "gray.600",
                fontSize: "sm",
                fontWeight: "medium",
              }
            : undefined
        }
      >
        <Flex
          flex={1}
          flexDir='column'
          gap={1}
          alignItems={{ base: "flex-end", md: "flex-start" }}
        >
          <CustomSkeleton
            h={4}
            w={{ base: "30%", md: "60%" }}
          />
        </Flex>
      </Flex>
    </ListRow>
  );
};
export default ListItemSkeleton;

const CustomSkeleton = (props: SkeletonProps) => (
  <Skeleton {...props}>
    {props.children ? <>{props.children}</> : <Flex opacity={0}>skeleton</Flex>}
  </Skeleton>
);
