"use client";

import React, { useState } from "react";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Heading,
  Stack,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Button,
  Text,
  Flex,
  Center,
  Link as ChakraLink,
} from "@chakra-ui/react";
import { ResetPasswordInput } from "@/__generated/graphql";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { resetPasswordSchema, ResetPasswordType } from "@/src/schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { PasswordInput } from "@/components/PasswordInput";
import Link from "next/link";
import { BadgeCheck } from "lucide-react";
import { useParams } from "next/navigation";

type Props = {
  resetPassword: ({
    password,
  }: {
    password: ResetPasswordInput["password"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
  >;
};
const ResetPasswordPage = ({ resetPassword }: Props) => {
  const { error, setError } = useMessageTimer();
  const [onSuccess, setOnSuccess] = useState(false);
  const params = useParams();
  const basePath = params?.basePath;

  const ResetPasswordSchema = resetPasswordSchema();
  const signUpUrl = basePath ? `/${basePath}/sign-up` : `/sign-up`;
  const baseUrl = basePath ? `/${basePath}` : `/`;

  const {
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ResetPasswordType>({
    resolver: zodResolver(ResetPasswordSchema),
    mode: "onChange",
  });

  const onSubmit = async (data: ResetPasswordType) => {
    setError("");
    try {
      const { password } = data;
      const res = await resetPassword({ password: password.trim() });

      if (res?.error) {
        setError(res?.error);
      }

      if (res?.data?.resetPassword?.ok) {
        setOnSuccess(true);
        reset();
      }
    } catch (error: any) {
      setError(error?.message);
    }
  };
  return (
    <Box
      w={{ base: "100vw", md: "xl" }}
      bg='white'
      borderRadius='lg'
      py={8}
      px={{ base: 4, md: 8 }}
    >
      <Heading
        as='h2'
        fontSize='24px'
        mb={4}
        textAlign='center'
        data-testid='title'
      >
        {onSuccess ? "" : "Choose a new Password"}
      </Heading>

      {onSuccess ? (
        <SuccessPage />
      ) : (
        <>
          {error && (
            <Alert
              status='error'
              w='full'
              my={4}
              borderRadius={6}
              data-testid='error-alert'
            >
              <AlertIcon />
              <AlertDescription
                whiteSpace='pre-wrap'
                wordBreak='break-word'
              >
                {error}
              </AlertDescription>
            </Alert>
          )}

          <Stack
            spacing={4}
            pt={{ base: 4, md: 6 }}
            as='form'
            onSubmit={handleSubmit(onSubmit)}
            w='full'
          >
            <Stack spacing={4}>
              <FormControl isInvalid={!!errors.password}>
                <FormLabel>Password</FormLabel>
                <PasswordInput
                  register={register}
                  placeholder='Enter your password'
                  name='password'
                  disabled={isSubmitting}
                  data-testid='password-input'
                />

                <FormErrorMessage
                  fontSize='sm'
                  data-testid='password-error'
                >
                  {errors?.password?.message}
                </FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.confirmPassword}>
                <FormLabel>Confirm Password</FormLabel>

                <PasswordInput
                  register={register}
                  placeholder='Confirm your password'
                  name='confirmPassword'
                  disabled={isSubmitting}
                  data-testid='confirm-password-input'
                />

                <FormErrorMessage
                  fontSize='sm'
                  data-testid='confirm-password-error'
                >
                  {errors?.confirmPassword?.message}
                </FormErrorMessage>
              </FormControl>

              <Button
                type='submit'
                isLoading={isSubmitting}
                data-testid='reset-password-button'
              >
                Reset Password
              </Button>
            </Stack>
          </Stack>

          <Flex
            mt={4}
            justifyContent='center'
            alignItems='center'
          >
            Don't have an account?
            <Text mx={2}>
              <ChakraLink
                as={Link}
                href={signUpUrl}
                passHref
                fontWeight='bold'
                _visited={{
                  color: "customPrimary.500",
                }}
                _hover={{
                  color: "customPrimary.600",
                }}
                color='customPrimary.500'
                data-testid='sign-up-link'
              >
                Sign Up
              </ChakraLink>
            </Text>
          </Flex>
        </>
      )}
      <Text
        mx={2}
        textAlign='center'
        mt={4}
      >
        <ChakraLink
          as={Link}
          href={baseUrl}
          passHref
          fontWeight='bold'
          _visited={{
            color: "customPrimary.500",
          }}
          _hover={{
            color: "customPrimary.600",
          }}
          color='customPrimary.500'
          data-testid='back-to-sign-in-link'
        >
          Back to Sign In
        </ChakraLink>
      </Text>
    </Box>
  );
};

export default ResetPasswordPage;

const SuccessPage = () => {
  return (
    <Stack
      spacing={4}
      textAlign='center'
      pt={4}
    >
      <Heading
        fontSize='20px'
        mb={4}
        textAlign='center'
      >
        Congratulations!
      </Heading>
      <Center>
        <BadgeCheck
          width='80px'
          height='80px'
          stroke='#9DD858'
        />
      </Center>

      <Text fontWeight='semibold'>Password Reset Complete</Text>
    </Stack>
  );
};
