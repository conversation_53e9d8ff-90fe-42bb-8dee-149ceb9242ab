"use client";

import { Tabs, TabList, Tab, TabPanels, Box, TabPanel } from "@chakra-ui/react";
import { useParams, usePathname, useRouter } from "next/navigation";
import React, { useState, useEffect, useRef, useCallback } from "react";

const SettingsTabs = ({
  children,
  tabContents,
}: {
  children: React.ReactNode;
  tabContents?: React.ReactNode[];
}) => {
  const pathname = usePathname();
  const params = useParams();
  const router = useRouter();
  const { basePath } = params || {};
  const tabListRef = useRef<HTMLDivElement>(null);
  const activeTabRef = useRef<HTMLButtonElement>(null);
  const isNavigatingRef = useRef(false);

  const tabs = ["Profile", "Payment Options", "Documents", "Change Password"];

  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const scrollToActiveTab = useCallback(() => {
    if (activeTabRef.current && tabListRef.current) {
      const tabList = tabListRef.current;
      const activeTab = activeTabRef.current;

      const tabListRect = tabList.getBoundingClientRect();
      const activeTabRect = activeTab.getBoundingClientRect();

      const scrollLeft =
        activeTab.offsetLeft - tabListRect.width / 2 + activeTabRect.width / 2;

      tabList.scrollTo({
        left: scrollLeft,
        behavior: "smooth",
      });
    }
  }, []);

  // Update active tab index based on current pathname
  useEffect(() => {
    if (!pathname || isNavigatingRef.current) return;

    const segments = pathname.split("/");
    const currentSegment = segments[segments.length - 1];

    // Check tabs for matching segment
    const tabIndex = tabs.findIndex(
      (tab) => currentSegment === tab.trim().toLowerCase().replace(/\s+/g, "-")
    );

    if (tabIndex !== -1) {
      setActiveTabIndex(tabIndex);
    } else {
      // Default to first tab if no match found
      setActiveTabIndex(0);
    }

    // Clear transition state when pathname changes
    setIsTransitioning(false);
  }, [pathname, tabs]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      scrollToActiveTab();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [activeTabIndex, scrollToActiveTab]);

  const handleTabChange = (index: number) => {
    // Set flag to prevent pathname effect from overriding our immediate update
    isNavigatingRef.current = true;
    setIsTransitioning(true);

    // Immediately update the active tab index for instant visual feedback
    setActiveTabIndex(index);

    const tabName = tabs[index].trim().toLowerCase().replace(/\s+/g, "-");
    const path = basePath
      ? `/${basePath}/settings/${tabName}`
      : `/settings/${tabName}`;

    // Use replace instead of push for faster navigation
    router.replace(path);

    // Reset the flag after navigation
    setTimeout(() => {
      isNavigatingRef.current = false;
    }, 50); // Reduced timeout
  };

  return (
    <Box
      p={{ base: 4, md: 6 }}
      borderRadius={6}
      boxShadow='sm'
      bg='white'
      maxW='8xl'
      mb={4}
    >
      <Tabs
        index={activeTabIndex}
        onChange={handleTabChange}
        isLazy={false} // Disable lazy loading for instant switching
      >
        <TabList
          ref={tabListRef}
          overflowX={{ base: "auto", lg: "visible" }}
          overflowY='visible'
          css={{
            "&::-webkit-scrollbar": { display: "none" },
            "-ms-overflow-style": "none",
            "scrollbar-width": "none",
          }}
        >
          {tabs.map((tab, index) => (
            <Tab
              key={tab}
              ref={activeTabIndex === index ? activeTabRef : null}
              _selected={{
                borderBottom: { base: "4px solid", lg: "2px solid" },
                borderColor: "blue.600",
                color: "blue.600",
                fontWeight: "semibold",
              }}
              _hover={{ color: "blue.600" }}
              minW='max-content'
              transition='all 0.2s'
            >
              {tab}
            </Tab>
          ))}
        </TabList>

        <TabPanels>
          {tabs.map((tab, index) => (
            <TabPanel
              key={tab}
              px={0}
              mx={0}
            >
              {/* Render specific content for each tab if provided, otherwise fallback to children */}
              {tabContents && tabContents[index]
                ? tabContents[index]
                : children}
            </TabPanel>
          ))}
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default SettingsTabs;
