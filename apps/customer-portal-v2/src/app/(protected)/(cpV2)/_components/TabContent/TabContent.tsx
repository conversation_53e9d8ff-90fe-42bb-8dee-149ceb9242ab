"use client";

import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";
import { generateChakraScale } from "@/utils/index";
import { overrides } from "@/utils/theme";
import {
  Application,
  ApplicationBankStageInput,
  ApplicationStatusEnum,
  Bank,
  BankAccountType,
  ClientInfo,
  CreateAccountBankInput,
  CreateApplicationTraceInput,
  Policy,
  Viewer,
} from "@/__generated/graphql";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  ButtonGroup,
  ChakraProviderProps,
  extendTheme,
  Heading,
  Stack,
  Text,
  VStack,
  withDefaultColorScheme,
} from "@chakra-ui/react";
import { OriginateFormGeneratorV2 } from "@indicina1/originate-form-builder";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import useUpdateCustomForm from "../../_services/CustomForm/useUpdateCustomForm";
import { DocumentsUploadContent } from "../DocumentsUploadContent";
import { PaymentOptionsContent } from "../PaymentOptionsContent";

type TabContentProps = {
  currentTab: any;
  tabSlug: string;
  activeFormTabs: Policy["applicationForm"];
  currentTabIndex: number;
  secondaryColor: string;
  defaultFormData: Application["customApplicationForm"];
  application: Application;
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  viewer: Viewer;
  addBank: ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  setBank: ({
    applicationId,
    accountBankId,
  }: {
    applicationId: ApplicationBankStageInput["applicationId"];
    accountBankId: ApplicationBankStageInput["accountBankId"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  getResolvedAccountNumber: ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  banks: Bank[];
  uploadAction: (formData: FormData) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
  >;
};

const TabContent = ({
  currentTab,
  tabSlug,
  activeFormTabs,
  currentTabIndex,
  secondaryColor,
  defaultFormData,
  application,
  createAppTrace,
  viewer,
  addBank,
  banks,
  getResolvedAccountNumber,
  setBank,
  uploadAction,
}: TabContentProps) => {
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const customColorScale = generateChakraScale(
    secondaryColor! || clientInfo?.clientTheme?.secondaryColor!
  );
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;
  const {
    defaultFormValues,
    error,
    success,
    handleSubmitForm,
    setError,
    isSubmitting,
  } = useUpdateCustomForm({
    defaultValues: defaultFormData,
    application,
    currentStepName: currentTab?.name!,
    createAppTrace,
  });

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const theme: ChakraProviderProps["theme"] = extendTheme(
    {
      ...overrides,
      colors: {
        customBrand: secondaryColor ? customColorScale : overrides.colors?.blue,
      },
      components: {
        Input: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },

        Select: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },

        Textarea: {
          baseStyle: {
            borderRadius: 6,
          },
        },
        NumberInput: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },
        Checkbox: {
          baseStyle: {
            control: {
              borderRadius: 4,
            },
          },
        },
        Radio: {
          baseStyle: {
            control: {
              borderRadius: 10,
            },
          },
        },
        Button: {
          baseStyle: {
            borderRadius: 6,
          },
          variants: {
            outline: {
              borderRadius: 6,
            },
          },
        },
      },
    },
    withDefaultColorScheme({
      colorScheme: secondaryColor ? "customBrand" : "blue",
    })
  );
  const onSubmit = async (data: any) => {
    try {
      await handleSubmitForm({ data });
    } catch (error: any) {
      setError(error?.message || "An error occurred while submitting the form");
    }
  };

  const status = application?.status?.name;

  return (
    <>
      {!clientInfo ? (
        <FullPageLoader />
      ) : currentTab ? (
        <Stack
          direction={{ base: "column", lg: "row" }}
          spacing={{ base: 4, lg: 20, xl: 32 }}
        >
          <VStack
            whiteSpace='nowrap'
            w='max-content'
            alignItems='flex-start'
            spacing={0.5}
          >
            <Heading size={{ base: "sm", lg: "md" }}>
              {currentTab?.name}
            </Heading>
            <Text color='gray.500'>{`Update your ${currentTab?.name}`}</Text>
          </VStack>
          <Box w={{ base: "100%", lg: "65%", xl: "55%" }}>
            {error && (
              <Alert
                status='error'
                w='full'
                my={4}
                borderRadius={6}
              >
                <AlertIcon />

                <AlertDescription
                  whiteSpace='pre-wrap'
                  wordBreak='break-word'
                >
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert
                status='success'
                w='full'
                my={4}
                borderRadius={6}
              >
                <AlertIcon />

                <AlertDescription
                  whiteSpace='pre-wrap'
                  wordBreak='break-word'
                >
                  {success}
                </AlertDescription>
              </Alert>
            )}
            <OriginateFormGeneratorV2
              key={tabSlug}
              tabs={activeFormTabs}
              onSubmit={onSubmit}
              step={currentTabIndex}
              theme={theme}
              defaultFormValues={defaultFormValues}
              kyc={true}
              disabled={
                isSubmitting || status !== ApplicationStatusEnum.Pending
              }
            >
              {status === ApplicationStatusEnum.Pending ? (
                <ButtonGroup
                  justifyContent='flex-end'
                  w='full'
                >
                  <Button
                    type='submit'
                    px={8}
                    isDisabled={
                      isSubmitting || status !== ApplicationStatusEnum.Pending
                    }
                    isLoading={isSubmitting}
                  >
                    Update
                  </Button>
                </ButtonGroup>
              ) : null}
            </OriginateFormGeneratorV2>
          </Box>
        </Stack>
      ) : tabSlug === "payment-options" ? (
        <PaymentOptionsContent
          application={application}
          viewer={viewer}
          clientInfo={clientInfo}
          createAppTrace={createAppTrace}
          addBank={addBank}
          banks={banks}
          getResolvedAccountNumber={getResolvedAccountNumber}
          setBank={setBank}
          uploadAction={uploadAction}
        />
      ) : (
        <DocumentsUploadContent />
      )}
    </>
  );
};

export default TabContent;
