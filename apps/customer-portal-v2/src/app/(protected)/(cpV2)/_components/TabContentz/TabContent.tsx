"use client";

import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";
import {
  Bank,
  BankAccountType,
  CardReferenceStatusInput,
  ChangePasswordInput,
  ClientInfo,
  CreateAccountBankInput,
  SetDefaultAccountBankInput,
  SetDefaultCardInput,
  SupportingDocumentTypes,
  Viewer,
} from "@/__generated/graphql";
import { Box } from "@chakra-ui/react";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import {
  ChangePassword,
  Documents,
  PaymentOptionsContent,
  Profile,
} from "./_components";

const TabContent = ({
  viewer,
  triggerOtp,
  confirmEmail,
  getAddCardRef,
  getCardRefStatus,
  setDefaultCard,
  setDefaultBank,
  getSupportingDocuments,
  getCdnFile,
  changePassword,
  banks,
  addBank,
  getResolvedAccountNumber,
}: {
  banks: Bank[];
  addBank: ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  getCdnFile: ({ key, bucket }: { key: string; bucket: string }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  viewer: Viewer;
  triggerOtp: () => Promise<
    | {
        success: boolean;
        error: any;
      }
    | undefined
  >;
  getSupportingDocuments: ({
    filterBy,
  }: {
    filterBy: SupportingDocumentTypes;
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
  >;
  confirmEmail: ({ code }: { code: string }) => Promise<
    | {
        success: boolean;
        error?: undefined;
        redirect?: undefined;
      }
    | {
        success: boolean;
        error: any;
        redirect?: undefined;
      }
    | {
        success: boolean;
        redirect: boolean;
        error?: undefined;
      }
    | undefined
  >;
  getCardRefStatus: ({
    reference,
  }: {
    reference: CardReferenceStatusInput["reference"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  getAddCardRef: () => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  setDefaultCard: ({
    cardId,
  }: {
    cardId: SetDefaultCardInput["cardId"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  setDefaultBank: ({
    accountBankId,
  }: {
    accountBankId: SetDefaultAccountBankInput["accountBankId"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  changePassword: ({ input }: { input: ChangePasswordInput }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  getResolvedAccountNumber: ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
}) => {
  const params = useParams();
  const basePath = params?.basePath as string;
  const tabSlug = params?.tabSlug as string;
  const path = basePath ?? null;
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  return (
    <>
      {!clientInfo ? (
        <FullPageLoader />
      ) : (
        <Box>
          {tabSlug === "profile" ? (
            <Profile
              viewer={viewer}
              triggerOtp={triggerOtp}
              confirmEmail={confirmEmail}
              clientInfo={clientInfo}
            />
          ) : tabSlug === "payment-options" ? (
            <PaymentOptionsContent
              clientInfo={clientInfo}
              viewer={viewer}
              getCardRefStatus={getCardRefStatus}
              getAddCardRef={getAddCardRef}
              setDefaultCard={setDefaultCard}
              setDefaultBank={setDefaultBank}
              banks={banks}
              addBank={addBank}
              getResolvedAccountNumber={getResolvedAccountNumber}
            />
          ) : tabSlug === "documents" ? (
            <Documents
              getSupportingDocuments={getSupportingDocuments}
              getCdnFile={getCdnFile}
            />
          ) : tabSlug === "change-password" ? (
            <ChangePassword changePassword={changePassword} />
          ) : (
            "not found"
          )}
        </Box>
      )}
    </>
  );
};
export default TabContent;
