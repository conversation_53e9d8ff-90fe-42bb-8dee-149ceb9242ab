import { PasswordInput } from "@/components/PasswordInput";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  ButtonGroup,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Stack,
  Text,
  VStack,
} from "@chakra-ui/react";
import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChangePasswordFormData, changePasswordSchema } from "@/src/schema";
import { ChangePasswordInput } from "@/__generated/graphql";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";

const ChangePassword = ({
  changePassword,
}: {
  changePassword: ({ input }: { input: ChangePasswordInput }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
}) => {
  const { setError, setSuccess, success, error } = useMessageTimer({
    duration: 7000,
  });

  const {
    register,
    reset,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
  });

  const onSubmit = async (data: ChangePasswordFormData) => {
    try {
      const response = await changePassword({
        input: {
          oldPassword: data.currentPassword,
          newPassword: data.newPassword,
        },
      });

      if (response?.error) {
        setError(response.error);
      } else if (response?.data) {
        setSuccess("Password changed successfully");
        reset();
      }
    } catch (error: any) {
      setError(error?.message || "An error occurred while changing password");
    }
  };

  return (
    <Stack
      p={{ base: 1, lg: 4 }}
      direction={{ base: "column", lg: "row" }}
      spacing={{ base: 4, lg: 24 }}
    >
      <VStack
        w={{ base: "fit-content", lg: "300px" }}
        alignItems='flex-start'
        spacing={0.5}
      >
        <Heading size={{ base: "sm", lg: "md" }}>Password Details</Heading>
        <Text color='gray.500'>Update your password details</Text>
      </VStack>

      <Box w={{ base: "100%", lg: "65%", xl: "55%" }}>
        {error && (
          <Alert
            status='error'
            variant='solid'
            w='full'
            my={4}
            borderRadius={6}
          >
            <AlertIcon />
            <AlertDescription
              whiteSpace='pre-wrap'
              wordBreak='break-word'
              textTransform='capitalize'
            >
              {error}
            </AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert
            status='success'
            variant='solid'
            w='full'
            my={4}
            borderRadius={6}
          >
            <AlertIcon />
            <AlertDescription
              whiteSpace='pre-wrap'
              wordBreak='break-word'
              textTransform='capitalize'
            >
              {success}
            </AlertDescription>
          </Alert>
        )}
        <Stack
          spacing={6}
          as='form'
          onSubmit={handleSubmit(onSubmit)}
        >
          <FormControl isInvalid={!!errors.currentPassword}>
            <FormLabel>Current Password</FormLabel>
            <PasswordInput
              register={register}
              placeholder='Enter your current password'
              name='currentPassword'
              disabled={isSubmitting}
            />
            <FormErrorMessage>
              {errors?.currentPassword?.message}
            </FormErrorMessage>
          </FormControl>
          <FormControl isInvalid={!!errors.newPassword}>
            <FormLabel>New Password</FormLabel>
            <PasswordInput
              register={register}
              placeholder='Enter your new password'
              name='newPassword'
              disabled={isSubmitting}
            />
            <FormErrorMessage>{errors?.newPassword?.message}</FormErrorMessage>
          </FormControl>
          <FormControl isInvalid={!!errors.confirmPassword}>
            <FormLabel>Confirm New Password</FormLabel>
            <PasswordInput
              register={register}
              placeholder='Confirm your new password'
              name='confirmPassword'
              disabled={isSubmitting}
            />
            <FormErrorMessage>
              {errors?.confirmPassword?.message}
            </FormErrorMessage>
          </FormControl>
          <ButtonGroup
            justifyContent='flex-end'
            w='full'
            mt={4}
          >
            <Button
              type='submit'
              isLoading={isSubmitting}
            >
              Change Password
            </Button>
          </ButtonGroup>
        </Stack>
      </Box>
    </Stack>
  );
};

export default ChangePassword;
