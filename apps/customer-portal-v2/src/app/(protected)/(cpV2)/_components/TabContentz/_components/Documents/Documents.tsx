"use client";
import React, { useEffect, useState, useCallback } from "react";
import { useForm, Controller } from "react-hook-form";
import { Select } from "@/components/forms";
import {
  Box,
  Button,
  Flex,
  Heading,
  Link,
  Stack,
  Text,
  VStack,
  Alert,
  AlertIcon,
  Skeleton,
} from "@chakra-ui/react";
import { SupportingDocumentTypes } from "@/__generated/graphql";

interface FormData {
  filterBy: SupportingDocumentTypes;
}

interface Document {
  id: string;
  documentName: string;
  file?: {
    url?: string;
    key?: string;
    bucket?: string;
  };
}

interface ApiResponse<T> {
  data: T | null;
  error: any;
}

type DocumentsProps = {
  getSupportingDocuments: ({
    filterBy,
  }: {
    filterBy: SupportingDocumentTypes;
  }) => Promise<ApiResponse<Document[]>>;
  getCdnFile: ({
    key,
    bucket,
  }: {
    key: string;
    bucket: string;
  }) => Promise<
    ApiResponse<{ getPresignedUrlFromS3: { dataUrl: string } }> | undefined
  >;
};

const Documents: React.FC<DocumentsProps> = ({
  getSupportingDocuments,
  getCdnFile,
}) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingDocumentId, setLoadingDocumentId] = useState<string | null>(
    null
  );

  const { control, watch } = useForm<FormData>({
    defaultValues: {
      filterBy: SupportingDocumentTypes.All,
    },
  });

  const selectedFilter = watch("filterBy");

  const fetchDocuments = useCallback(
    async (filterBy: SupportingDocumentTypes) => {
      setLoading(true);
      setError(null);

      try {
        const result = await getSupportingDocuments({ filterBy });

        if (result?.error) {
          setError(
            typeof result.error === "string"
              ? result.error
              : "Failed to fetch documents"
          );
          setDocuments([]);
        } else {
          setDocuments(result?.data || []);
        }
      } catch (err) {
        setError("Failed to fetch documents");
        setDocuments([]);
      } finally {
        setLoading(false);
      }
    },
    [getSupportingDocuments]
  );

  const handleViewDocument = async (document: Document) => {
    if (!document?.file?.key || !document?.file?.bucket) {
      setError("Document file information is missing");
      return;
    }

    setLoadingDocumentId(document?.id);
    setError(null);

    try {
      const result = await getCdnFile({
        key: document.file.key,
        bucket: document.file.bucket,
      });

      if (result?.data?.getPresignedUrlFromS3?.dataUrl) {
        const url = result.data.getPresignedUrlFromS3.dataUrl;
        const newWindow = window.open();
        if (newWindow) {
          newWindow.location.href = url;
        }
        setLoadingDocumentId(null);
      } else if (result?.error) {
        setError("Failed to generate document URL");
        setLoadingDocumentId(null);
      } else {
        setError("Unable to access document");
        setLoadingDocumentId(null);
      }
    } catch (err) {
      setError("Failed to load document");
      setLoadingDocumentId(null);
    }
  };

  useEffect(() => {
    fetchDocuments(selectedFilter);
  }, [selectedFilter, fetchDocuments]);

  return (
    <Stack
      p={{ base: 1, lg: 4 }}
      direction={{ base: "column", lg: "row" }}
      spacing={{ base: 4, lg: 24 }}
    >
      <VStack
        w={{ base: "fit-content", lg: "300px" }}
        alignItems='flex-start'
        spacing={0.5}
      >
        <Heading size={{ base: "sm", lg: "md" }}>Documents uploaded</Heading>
        <Text color='gray.500'>View all your uploaded documents</Text>
      </VStack>

      <Box w={{ base: "100%", lg: "65%", xl: "55%" }}>
        <Flex
          w='full'
          mb={4}
          justifyContent='space-between'
        >
          <Box></Box>
          <Controller
            name='filterBy'
            control={control}
            render={({ field }) => (
              <Select
                w='fit-content'
                value={field.value}
                onChange={field.onChange}
                ml='auto'
              >
                {Object.entries(SupportingDocumentTypes).map(([key, value]) => (
                  <option
                    key={value}
                    value={value}
                  >
                    {key}
                  </option>
                ))}
              </Select>
            )}
          />
        </Flex>

        {error && (
          <Alert
            status='error'
            mb={4}
          >
            <AlertIcon />
            {error}
          </Alert>
        )}

        {!loading && !error && documents.length === 0 && (
          <Box
            border='1px solid #D6DDEB'
            px={6}
            py={8}
            borderRadius={6}
            textAlign='center'
          >
            <Text color='gray.500'>No documents found</Text>
          </Box>
        )}

        {loading && (
          <Stack spacing={4}>
            {Array.from({ length: 4 }, (_, i) => (
              <Flex
                key={i}
                alignItems='center'
                justifyContent='space-between'
                border='1px solid #D6DDEB'
                px={4}
                py={2}
                borderRadius={6}
              >
                <Skeleton
                  w='60%'
                  h={6}
                />
                <Skeleton
                  w='20%'
                  h={8}
                />
              </Flex>
            ))}
          </Stack>
        )}

        {!loading && !error && documents.length > 0 && (
          <Stack spacing={4}>
            {documents.map((document) => (
              <Flex
                key={document.id}
                alignItems='center'
                justifyContent='space-between'
                border='1px solid #D6DDEB'
                px={4}
                py={2}
                borderRadius={6}
              >
                <Text
                  fontWeight='medium'
                  isTruncated
                  textTransform='capitalize'
                  w='70%'
                  title={document.documentName}
                >
                  {document.documentName}
                </Text>

                <Button
                  colorScheme='gray'
                  color='blue.700'
                  px={8}
                  onClick={() => handleViewDocument(document)}
                  isLoading={loadingDocumentId === document.id}
                  disabled={
                    !document.file?.url &&
                    (!document.file?.key || !document.file?.bucket)
                  }
                  _visited={{
                    color: "blue.700",
                  }}
                >
                  View
                </Button>
              </Flex>
            ))}
          </Stack>
        )}
      </Box>
    </Stack>
  );
};

export default Documents;
