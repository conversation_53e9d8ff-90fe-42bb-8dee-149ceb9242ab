"use client";

import {
  Bank,
  BankAccountType,
  CardReferenceStatusInput,
  ClientInfo,
  CreateAccountBankInput,
  SetDefaultAccountBankInput,
  SetDefaultCardInput,
  Viewer,
} from "@/__generated/graphql";
import { Stack } from "@chakra-ui/react";
import React from "react";
import usePaymentService from "../../../../_services/PaymentServices/usePaymentService";
import { AddBank, AddCard } from "./_components";

type Props = {
  getCardRefStatus: ({
    reference,
  }: {
    reference: CardReferenceStatusInput["reference"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  getAddCardRef: () => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  viewer: Viewer;
  clientInfo: ClientInfo | null;
  setDefaultCard: ({
    cardId,
  }: {
    cardId: SetDefaultCardInput["cardId"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  setDefaultBank: ({
    accountBankId,
  }: {
    accountBankId: SetDefaultAccountBankInput["accountBankId"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  banks: Bank[];
  addBank: ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  getResolvedAccountNumber: ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
};

const PaymentOptionsContent = ({
  clientInfo,
  getAddCardRef,
  viewer,
  getCardRefStatus,
  setDefaultCard,
  setDefaultBank,
  addBank,
  banks,
  getResolvedAccountNumber,
}: Props) => {
  const {
    reference,
    isSubmitting,
    handlePaystackWidgetTrigger,
    setDefaultCardAction,
    setDefaultBankAction,
    isLoading,
    handleAddBankDetails,
    error,
    setError,
    resolveAccountNumber,
  } = usePaymentService({
    clientInfo,
    viewer,
    getCardRefStatus,
    getAddCardRef,
    setDefaultCard,
    setDefaultBank,
    getResolvedAccountNumber,
    addBank,
  });

  return (
    <Stack spacing={18}>
      <AddCard
        isLoading={isSubmitting}
        viewer={viewer}
        reference={reference}
        isSubmitting={isSubmitting}
        handlePaystackWidgetTrigger={handlePaystackWidgetTrigger}
        setDefaultCardAction={setDefaultCardAction}
      />
      <AddBank
        viewer={viewer}
        setDefaultBankAction={setDefaultBankAction}
        isLoading={isLoading}
        banks={banks}
        handleAddBankDetails={handleAddBankDetails}
        error={error}
        setError={setError}
        resolveAccountNumber={resolveAccountNumber}
      />
    </Stack>
  );
};
export default PaymentOptionsContent;
