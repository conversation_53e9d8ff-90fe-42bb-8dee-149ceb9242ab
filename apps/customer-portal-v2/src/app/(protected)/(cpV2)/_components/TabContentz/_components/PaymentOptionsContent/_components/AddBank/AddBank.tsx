"use client";

import {
  Bank,
  SetDefaultAccountBankInput,
  Viewer,
} from "@/__generated/graphql";
import {
  Box,
  Flex,
  Heading,
  Stack,
  Text,
  VStack,
  Center,
  Radio,
  RadioGroup,
  useToast,
  Grid,
  GridItem,
} from "@chakra-ui/react";
import Image from "next/image";
import React, { useCallback } from "react";
import { bankMetadata } from "@/utils/bankMetadata";
import { Landmark } from "lucide-react";
import { useForm } from "react-hook-form";
import { AddBankFormProps } from "@/src/types/addBankForm";
import AddBankModal from "./AddBankModal";

type Props = {
  viewer: Viewer;
  setDefaultBankAction: ({
    accountBankId,
  }: {
    accountBankId: SetDefaultAccountBankInput["accountBankId"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  isLoading: boolean;
  banks: Bank[];
  handleAddBankDetails: (
    data: Pick<AddBankFormProps, "accountNumber" | "bankId" | "accountName">
  ) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  error: string;
  resolveAccountNumber: (
    data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountType">
  ) => Promise<any>;
  setError: React.Dispatch<React.SetStateAction<string>>;
};

const AddBank = ({
  viewer,
  setDefaultBankAction,
  isLoading,
  banks,
  handleAddBankDetails,
  error,
  resolveAccountNumber,
  setError,
}: Props) => {
  const toast = useToast();

  const bankAccounts = viewer?.account?.bankAccounts || [];
  const defaultBank = bankAccounts.find((account) => account?.isDefault);

  const mergeBankDataWithLogo = (bankAccount: any) => {
    const matchingBank = bankMetadata.find((bankMeta) => {
      const bankName = bankAccount.bank?.name?.toLowerCase().trim();
      const metaName = bankMeta?.name?.toLowerCase().trim();

      // Non-strict matching: check if either name contains the other
      return (
        bankName &&
        metaName &&
        (bankName.includes(metaName) ||
          metaName.includes(bankName) ||
          bankName.replace(/\s+/g, "").includes(metaName.replace(/\s+/g, "")) ||
          metaName.replace(/\s+/g, "").includes(bankName.replace(/\s+/g, "")))
      );
    });

    return {
      ...bankAccount,
      bank: {
        ...bankAccount.bank,
        logoUrl: matchingBank?.logoUrl || "",
      },
    };
  };

  const banksWithLogos = bankAccounts.map(mergeBankDataWithLogo);

  const defaultBankId = defaultBank?.id || "";

  const { watch, setValue, register } = useForm({
    mode: "onChange",
    defaultValues: {
      defaultBank: defaultBankId,
    },
  });

  const selectedBank = watch("defaultBank");

  const handleDefaultBankChange = useCallback(
    async (value: string) => {
      try {
        setValue("defaultBank", value);
        const res = await setDefaultBankAction({ accountBankId: value });

        if (res?.data?.setDefaultAccountBank) {
          toast({
            title: "Success",
            description: "Default bank account updated successfully",
            status: "success",
            duration: 5000,
            isClosable: true,
            position: "top-right",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          status: "error",
          duration: 5000,
          isClosable: true,
          position: "top-right",
        });
        setValue("defaultBank", defaultBankId);
      }
    },
    [setValue, defaultBankId, toast]
  );

  return (
    <Stack
      p={{ base: 1, lg: 4 }}
      direction={{ base: "column", lg: "row" }}
      spacing={{ base: 4, xl: 24 }}
    >
      <VStack
        minW={{ base: "fit-content", lg: "300px" }}
        alignItems='flex-start'
        spacing={0.5}
      >
        <Heading size={{ base: "sm", lg: "md" }}>Bank Accounts</Heading>
        <Text color='gray.500'>This is used for loan disbursements.</Text>
      </VStack>
      {banks?.length > 0 && (
        <Flex flexDirection='column'>
          <Box w='full'>
            <RadioGroup
              value={selectedBank}
              onChange={handleDefaultBankChange}
              isDisabled={isLoading}
            >
              <Grid
                templateColumns={{
                  base: "repeat(1, 1fr)",
                  md: "repeat(2, 1fr)",
                  xl: "repeat(3, 1fr)",
                }}
                gap={4}
              >
                {banksWithLogos?.map((bank) => (
                  <GridItem
                    key={bank?.id}
                    w='100%'
                  >
                    <Flex
                      borderRadius={6}
                      border='1px solid #D6DDEB'
                      p={4}
                      flexDirection='column'
                      h='full'
                    >
                      <Flex
                        alignItems='center'
                        justifyContent='space-between'
                        mb={4}
                        w='full'
                      >
                        <Radio
                          value={bank?.id}
                          colorScheme='green'
                          border='1px solid black'
                        >
                          <Text
                            fontSize='sm'
                            fontWeight='normal'
                          >
                            {bank?.isDefault
                              ? "Default Account"
                              : bank?.status === "enabled"
                                ? "Active Account"
                                : null}
                          </Text>
                        </Radio>
                      </Flex>

                      <Flex
                        gap={2}
                        alignItems='center'
                        color='gray.400'
                      >
                        {bank?.bank?.logoUrl ? (
                          <Image
                            src={bank?.bank?.logoUrl}
                            alt='bank-logo'
                            width={40}
                            height={40}
                            style={{ objectFit: "contain" }}
                          />
                        ) : (
                          <Center
                            boxSize={10}
                            border='1px solid'
                            p={1}
                            borderRadius={6}
                          >
                            <Landmark size={40} />
                          </Center>
                        )}

                        <Flex flexDirection='column'>
                          <Text
                            fontSize='sm'
                            wordBreak='break-word'
                          >
                            {!bank?.bank?.logoUrl
                              ? `${bank?.accountNumber} - ${bank?.bank?.name}`
                              : bank?.accountNumber}
                          </Text>
                          <Text color='black'>{bank?.accountName}</Text>
                        </Flex>
                      </Flex>
                    </Flex>
                  </GridItem>
                ))}
              </Grid>
            </RadioGroup>
          </Box>
          <AddBankModal
            banks={banks}
            handleAddBankDetails={handleAddBankDetails}
            isLoading={isLoading}
            error={error}
            setError={setError}
            resolveAccountNumber={resolveAccountNumber}
          />
        </Flex>
      )}
    </Stack>
  );
};

export default AddBank;
