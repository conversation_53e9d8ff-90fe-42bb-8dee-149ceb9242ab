"use client";
import React, { useEffect, useState } from "react";
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  useDisclosure,
  Text,
  FormControl,
  Alert,
  AlertIcon,
  AlertDescription,
  Flex,
  Box,
  useToast,
} from "@chakra-ui/react";
import { Controller, useForm } from "react-hook-form";
import { NumericInput, Select, Input } from "@/components/forms";
import { AddBankFormProps } from "@/src/types/addBankForm";
import {
  Bank,
  BankAccountDetails,
  BankAccountType,
} from "@/__generated/graphql";
import { useRouter } from "next/navigation";

type Props = {
  banks: Bank[];
  handleAddBankDetails: (
    data: Pick<AddBankFormProps, "accountNumber" | "bankId" | "accountName">
  ) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  resolveAccountNumber: (
    data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountType">
  ) => Promise<any>;
  isLoading: boolean;
  error: string;
  setError: React.Dispatch<React.SetStateAction<string>>;
};

const AddBankModal = ({
  banks,
  handleAddBankDetails,
  resolveAccountNumber,
  isLoading,
  error,
  setError,
}: Props) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const router = useRouter();

  const {
    handleSubmit,
    control,
    watch,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<AddBankFormProps>();

  const closeModal = () => {
    reset();
    setError("");
    onClose();
  };

  const onOpenModal = () => {
    setError("");
    onOpen();
  };

  const [resolveBank, setResolveBank] = useState<BankAccountDetails | null>(
    null
  );

  const bankId = watch("bankId");
  const accountNumber = watch("accountNumber");
  const accountType = watch("accountType");

  const accountName =
    resolveBank && !Array.isArray(resolveBank)
      ? resolveBank?.accountName
      : undefined;

  const onSubmit = async (data: AddBankFormProps) => {
    const payload = {
      accountNumber: data.accountNumber,
      bankId: data.bankId,
      accountName: accountName!,
    };
    const result = await handleAddBankDetails(payload);

    if (result?.error) {
      setError(result.error);
    } else {
      toast({
        title: "Bank account added successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top-right",
      });
      closeModal();
      router.refresh();
    }
  };

  const bankAccountType = accountType || BankAccountType.Personal;

  useEffect(() => {
    if (accountNumber?.length === 10 && bankId && bankAccountType) {
      const fetchAccountDetails = async () => {
        const res = await resolveAccountNumber({
          bankId,
          accountNumber,
          accountType: bankAccountType,
        });
        setResolveBank(res?.resolveAccountNumber);
      };

      fetchAccountDetails();
    }
  }, [bankId, accountNumber, bankAccountType, resolveAccountNumber]);

  return (
    <>
      <Button
        px={8}
        mt={6}
        colorScheme='customPrimary'
        onClick={onOpenModal}
        w={{ base: "full", md: "fit-content" }}
      >
        Add New Bank
      </Button>

      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        size='lg'
      >
        <ModalOverlay />
        <ModalContent
          pos='fixed'
          transform='translate(-50%, -50%)'
        >
          <ModalHeader>Add Bank</ModalHeader>
          <ModalCloseButton />
          <ModalBody
            px={{ base: 2, md: 6 }}
            pb={4}
          >
            {error && (
              <Alert
                status='error'
                w='full'
                my={4}
                borderRadius={6}
              >
                <AlertIcon />
                <AlertDescription
                  whiteSpace='pre-wrap'
                  wordBreak='break-word'
                >
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <Box>
              <FormControl as='form'>
                <Flex
                  gap={4}
                  flexDir='column'
                >
                  <Controller
                    name='accountType'
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        label='Account Type'
                        autoFocus
                        isDisabled={isLoading || isSubmitting}
                        isInvalid={!!errors?.accountType}
                        defaultValue='personal'
                      >
                        <option value='personal'>Personal</option>
                        <option value='business'>Business</option>
                      </Select>
                    )}
                  />

                  <Controller
                    name='bankId'
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        placeholder='--Select a Bank--'
                        label='Bank'
                        isDisabled={isLoading || isSubmitting}
                        isInvalid={!!errors?.bankId}
                        errorMessage={errors?.bankId?.message as string}
                      >
                        {banks?.map((data, idx) => (
                          <option
                            key={idx}
                            value={data?.id}
                          >
                            {data?.name}
                          </option>
                        ))}
                      </Select>
                    )}
                  />

                  <Flex
                    gap={6}
                    flexDir='column'
                  >
                    <Controller
                      name='accountNumber'
                      control={control}
                      render={({ field }) => (
                        <NumericInput
                          allowLeadingZeros
                          thousandSeparator={false}
                          {...field}
                          label='Account Number'
                          maxLength={10}
                          placeholder='Enter your account number'
                          isInvalid={!!errors?.accountNumber}
                          errorMessage={
                            errors?.accountNumber?.message as string
                          }
                          disabled={isLoading || isSubmitting}
                        />
                      )}
                    />
                    {accountName &&
                      accountNumber?.length === 10 &&
                      bankId &&
                      bankAccountType && (
                        <Input
                          value={accountName}
                          borderRadius={6}
                          label='Account Name'
                          isDisabled
                          isLoading={isLoading}
                        />
                      )}
                  </Flex>

                  <Text
                    mb={6}
                    size='sm'
                  >
                    <b>Note:</b> Please select the <b>Personal</b> account type
                    for personal loans and the <b>Business</b> account type for
                    business loans.
                  </Text>

                  <Button
                    onClick={handleSubmit(onSubmit)}
                    isLoading={isSubmitting || isLoading}
                    w='full'
                    px={8}
                    colorScheme='customPrimary'
                  >
                    Add Bank Account
                  </Button>
                </Flex>
              </FormControl>
            </Box>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default AddBankModal;
