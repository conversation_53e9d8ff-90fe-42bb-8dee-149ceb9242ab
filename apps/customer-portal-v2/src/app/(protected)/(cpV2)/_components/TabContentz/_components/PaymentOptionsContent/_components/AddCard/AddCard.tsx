"use client";
import { Viewer } from "@/__generated/graphql";
import {
  Box,
  Stack,
  Text,
  VStack,
  Heading,
  Button,
  Flex,
  Icon,
  Radio,
  RadioGroup,
  useToast,
} from "@chakra-ui/react";
import React, { useCallback, useRef } from "react";
import { useForm } from "react-hook-form";
import MasterCard from "@/components/Icons/MasterCard";
import VisaCard from "@/components/Icons/VisaCard";
import VerveCard from "@/components/Icons/VerveCard";

type AddCardProps = {
  isLoading: boolean;
  reference: string;
  isSubmitting: boolean;
  handlePaystackWidgetTrigger: () => void;
  viewer: Viewer;
  setDefaultCardAction: ({ cardId }: { cardId: string }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
};

const CARD_ICONS = {
  master: MasterCard,
  visa: VisaCard,
  verve: VerveCard,
} as const;

const AddCard = ({
  isLoading,
  handlePaystackWidgetTrigger,
  isSubmitting,
  reference,
  viewer,
  setDefaultCardAction,
}: AddCardProps) => {
  const refs = useRef<{ [key: string]: HTMLElement | null }>({});
  const toast = useToast();

  const cards = viewer?.account?.cards || [];

  const defaultCard = cards?.find((card) => card?.isDefault === true);
  const defaultCardId = defaultCard?.id;

  const { watch, setValue, register } = useForm({
    mode: "onChange",
    defaultValues: {
      defaultCard: defaultCardId!,
    },
  });

  const selectedCard = watch("defaultCard");

  const handleDefaultCardChange = useCallback(
    async (value: string) => {
      try {
        setValue("defaultCard", value);
        const result = await setDefaultCardAction({ cardId: value });

        if (result?.error) {
          toast({
            title: "Error",
            description: result.error.message || "Failed to set default card",
            status: "error",
            duration: 5000,
            isClosable: true,
            position: "top-right",
          });
          setValue("defaultCard", defaultCardId || "");
        } else if (result?.data) {
          toast({
            title: "Success",
            description: "Default card updated successfully",
            status: "success",
            duration: 3000,
            isClosable: true,
            position: "top-right",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          status: "error",
          duration: 5000,
          isClosable: true,
          position: "top-right",
        });
        setValue("defaultCard", defaultCardId || "");
      }
    },
    [setValue, setDefaultCardAction, defaultCardId, toast]
  );
  return (
    <Stack
      p={{ base: 1, lg: 4 }}
      direction={{ base: "column", lg: "row" }}
      spacing={{ base: 4, lg: 24 }}
    >
      <VStack
        w={{ base: "fit-content", lg: "300px" }}
        alignItems='flex-start'
        spacing={0.5}
      >
        <Heading size={{ base: "sm", lg: "md" }}>Cards</Heading>
        <Text color='gray.500'>
          Your repayments can be charged to your active card.
        </Text>
      </VStack>
      <Box>
        <RadioGroup
          value={selectedCard}
          onChange={handleDefaultCardChange}
          isDisabled={isLoading}
        >
          <Flex
            gap={4}
            justifyContent={{ base: "center", md: "flex-start" }}
          >
            <Stack>
              <Flex
                flexDirection={{ base: "column", md: "row" }}
                gap={4}
                flexWrap='wrap'
                justifyContent={{ base: "center", md: "flex-start" }}
              >
                {cards?.map((card) => {
                  const CardIcon =
                    card?.type && card?.type in CARD_ICONS
                      ? CARD_ICONS[card?.type as keyof typeof CARD_ICONS]
                      : undefined;
                  const isSelected = selectedCard === card?.id;

                  return (
                    <Box
                      key={card?.id}
                      position='relative'
                      w={{ base: "290px", xl: "320px" }}
                      h='200px'
                      borderRadius='2xl'
                      overflow='hidden'
                      boxShadow='2xl'
                    >
                      {/* Gradient backgrounds */}
                      <Box
                        position='absolute'
                        inset={0}
                        bgGradient='linear(to-br, #8B5CF6, #7C3AED, #3B82F6)'
                      />
                      <Box
                        position='absolute'
                        inset={0}
                        bgGradient='linear(to-r, #A855F7, transparent, #2563EB)'
                        opacity={0.6}
                      />
                      <Box
                        position='absolute'
                        inset={0}
                        bgGradient='radial(transparent, #9333EA33, #1D4ED84D)'
                      />

                      {/* Card content */}
                      <Box
                        position='relative'
                        h='full'
                        py={4}
                        px={{ base: 4, xl: 6 }}
                        display='flex'
                        flexDirection='column'
                        justifyContent='space-between'
                        color='white'
                      >
                        <Flex
                          justifyContent='space-between'
                          mb={4}
                        >
                          <Radio
                            value={card?.id}
                            id={card?.id}
                            {...register("defaultCard")}
                            colorScheme='customBrand'
                            border='3px solid white'
                            isChecked={isSelected}
                            ref={(el) => {
                              if (defaultCardId) {
                                refs.current[defaultCardId] = el;
                              }
                            }}
                          >
                            <Text
                              color='white'
                              fontWeight='600'
                              textTransform='uppercase'
                            >
                              Repayment Card{" "}
                              {card?.isDefault ? "(Default)" : ""}
                            </Text>
                          </Radio>
                        </Flex>

                        {/* Card number section */}
                        <Flex
                          flex={1}
                          align='center'
                        >
                          <Flex
                            gap={{ base: 6, md: 4 }}
                            alignItems='center'
                            color='white'
                            fontSize={{ base: "lg", xl: "xl" }}
                            fontWeight='light'
                            letterSpacing='wider'
                          >
                            {Array.from({ length: 3 }, (_, setIndex) => (
                              <Flex
                                key={`set-${setIndex}`}
                                gap={{ base: 1, md: 2 }}
                              >
                                {Array.from({ length: 4 }, (_, i) => (
                                  <Box
                                    key={`dot-${setIndex}-${i}`}
                                    w={2}
                                    h={2}
                                    borderRadius='full'
                                    bg='white'
                                  />
                                ))}
                              </Flex>
                            ))}
                            <Text fontWeight='600'>
                              {card?.maskedPan?.slice(-4)}
                            </Text>
                          </Flex>
                        </Flex>

                        {/* Expiry and card type */}
                        <Flex
                          align='flex-end'
                          justify='space-between'
                        >
                          <Text
                            fontSize='md'
                            fontWeight='medium'
                          >
                            {card?.expiryDate.replace("-", "/")}
                          </Text>
                          <Icon
                            as={CardIcon}
                            w={{ base: 14, md: 20, lg: 24 }}
                            h={{ base: 10, md: 12 }}
                          />
                        </Flex>
                      </Box>
                    </Box>
                  );
                })}
              </Flex>
            </Stack>
          </Flex>
        </RadioGroup>

        <Button
          isDisabled={!reference}
          isLoading={isSubmitting}
          px={8}
          mt={6}
          onClick={handlePaystackWidgetTrigger}
          colorScheme='customPrimary'
        >
          Add New Card
        </Button>
      </Box>
    </Stack>
  );
};

export default AddCard;
