import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Button,
  Box,
  Text,
  Alert,
  AlertIcon,
  AlertDescription,
  Stack,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Flex,
  useToast,
} from "@chakra-ui/react";
import { Controller, useForm } from "react-hook-form";
import { NumericInput } from "@/components/forms";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { EmailOTPType, EmailOTPValidationSchema } from "@/src/schema";
import { useRouter } from "next/navigation";

const EmailVerify = ({
  email,
  triggerOtp,
  confirmEmail,
}: {
  email: string;
  triggerOtp: () => Promise<
    | {
        success: boolean;
        error: any;
      }
    | undefined
  >;
  confirmEmail: ({ code }: { code: string }) => Promise<
    | {
        success: boolean;
        error?: any;
        redirect?: boolean;
      }
    | undefined
  >;
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const router = useRouter();

  const { error, setError, setSuccess, success } = useMessageTimer();
  const [isResending, setIsResending] = useState(false);

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<EmailOTPType>({
    resolver: zodResolver(EmailOTPValidationSchema),
    mode: "onChange",
  });

  const resendOtp = async () => {
    try {
      setError("");
      setIsResending(true);
      const result = await triggerOtp();

      if (result?.success) {
        setSuccess(`OTP resent to ${email} successfully`);
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to resend OTP");
    } finally {
      setIsResending(false);
    }
  };

  const closeModal = () => {
    reset();
    onClose();
  };
  const onSubmit = async (data: EmailOTPType) => {
    try {
      setError("");
      const result = await confirmEmail({
        code: data?.workEmailVerificationCode.trim(),
      });

      if (result?.success) {
        if (result.redirect) {
          closeModal();
          toast({
            title: "Email verified successfully",
            status: "success",
            duration: 5000,
            isClosable: true,
            position: "top-right",
          });
          router.refresh();
        }
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to verify email");
    }
  };

  const handleOpenModal = async () => {
    setError("");
    setSuccess("");
    reset();

    onOpen();

    try {
      const result = await triggerOtp();
      if (result?.success) {
        setSuccess(`OTP sent to ${email} successfully`);
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to send OTP");
    }
  };
  return (
    <>
      <Button
        variant='link'
        colorScheme='customPrimary'
        onClick={handleOpenModal}
      >
        Verify email
      </Button>

      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size='xl'
        isCentered
      >
        <ModalOverlay />
        <ModalContent
          pos='fixed'
          top='20%'
          transform='translate(-50%, -50%)'
        >
          <ModalHeader>Verify your e-mail</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <Box>
              {error && (
                <Alert
                  status='error'
                  w='full'
                  my={4}
                  borderRadius={6}
                >
                  <AlertIcon />
                  <AlertDescription
                    whiteSpace='pre-wrap'
                    wordBreak='break-word'
                  >
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert
                  status='success'
                  w='full'
                  my={4}
                  borderRadius={6}
                >
                  <AlertIcon />
                  <AlertDescription
                    whiteSpace='pre-wrap'
                    wordBreak='break-word'
                  >
                    {success}
                  </AlertDescription>
                </Alert>
              )}

              <Stack
                as='form'
                spacing={8}
                w='full'
                onSubmit={handleSubmit(onSubmit)}
              >
                <FormControl isInvalid={!!errors?.workEmailVerificationCode}>
                  <FormLabel>Verification Code</FormLabel>
                  <Controller
                    name='workEmailVerificationCode'
                    control={control}
                    defaultValue=''
                    render={({ field }) => (
                      <NumericInput
                        maxLength={6}
                        allowLeadingZeros
                        placeholder='Enter Verification Code'
                        thousandSeparator={false}
                        value={field.value || ""}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        isInvalid={!!errors?.workEmailVerificationCode?.message}
                        autoFocus
                      />
                    )}
                  />
                  <FormErrorMessage fontSize='sm'>
                    {errors?.workEmailVerificationCode?.message}
                  </FormErrorMessage>
                </FormControl>

                <Flex
                  justifyContent='center'
                  alignItems='center'
                  flexDirection='column'
                >
                  <Text
                    textAlign='center'
                    mb={2}
                  >
                    Didn't get OTP?
                  </Text>
                  <Flex alignItems='center'>
                    <Button
                      variant='link'
                      onClick={resendOtp}
                      mx={1}
                      isLoading={isResending}
                      loadingText='Sending...'
                      disabled={isResending}
                      colorScheme='customPrimary'
                      fontWeight='bold'
                      _hover={{
                        color: "customPrimary.600",
                        textDecoration: "underline",
                      }}
                    >
                      Resend OTP
                    </Button>
                  </Flex>
                </Flex>

                <Button
                  type='submit'
                  isLoading={isSubmitting}
                  w='full'
                  px={8}
                >
                  Verify Email
                </Button>
              </Stack>
            </Box>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};
export default EmailVerify;
