"use client";

import { NumericInput } from "@/components/forms";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  ButtonGroup,
  extendTheme,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  VStack,
  withDefaultColorScheme,
} from "@chakra-ui/react";
import React, { useState } from "react";
import { ClientInfo, Viewer } from "@/__generated/graphql";
import EmailVerify from "./EmailVerify";
import { OriginateFormGeneratorV2 } from "@indicina1/originate-form-builder";
import { generateChakraScale } from "@/utils/index";
import { overrides } from "@/utils/theme";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { useSession } from "next-auth/react";
import { updateForm } from "@/src/app/actions";
import { useRouter } from "next/navigation";
import { ChakraProviderProps } from "@chakra-ui/react";

const Profile = ({
  viewer,
  triggerOtp,
  confirmEmail,
  clientInfo,
}: {
  viewer: Viewer;
  clientInfo: ClientInfo | null;
  triggerOtp: () => Promise<{ success: boolean; error: any } | undefined>;
  confirmEmail: ({ code }: { code: string }) => Promise<
    | {
        success: boolean;
        error?: any;
        redirect?: boolean;
      }
    | undefined
  >;
}) => {
  const { error, setError, setSuccess, success } = useMessageTimer({
    duration: 7000,
  });
  const [isLoading, setIsLoading] = useState(false);
  const session = useSession();

  const token = session?.data?.accessToken!;
  const router = useRouter();
  const customColorScale = generateChakraScale(
    clientInfo?.clientTheme?.secondaryColor || ""
  );

  const theme: ChakraProviderProps["theme"] = extendTheme(
    {
      ...overrides,
      colors: {
        customBrand: clientInfo?.clientTheme?.secondaryColor
          ? customColorScale
          : overrides.colors?.blue,
      },
      components: {
        Input: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },

        Select: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },

        Textarea: {
          baseStyle: {
            borderRadius: 6,
          },
        },
        NumberInput: {
          baseStyle: {
            field: {
              borderRadius: 6,
            },
          },
        },
        Checkbox: {
          baseStyle: {
            control: {
              borderRadius: 4,
            },
          },
        },
        Radio: {
          baseStyle: {
            control: {
              borderRadius: 10,
            },
          },
        },
        Button: {
          baseStyle: {
            borderRadius: 6,
          },
          variants: {
            outline: {
              borderRadius: 6,
            },
          },
        },
      },
    },
    withDefaultColorScheme({
      colorScheme: clientInfo?.clientTheme?.secondaryColor
        ? "customBrand"
        : "blue",
    })
  );

  const onSubmit = async (data: any) => {
    setError("");
    setSuccess("");

    try {
      setIsLoading(true);

      const res = await updateForm(
        JSON.parse(JSON.stringify({ ...data, token }))
      );

      if (res?.data?.updateUserData?.success) {
        const successMessage =
          res.data.updateUserData.message || "Profile updated successfully!";
        setSuccess(successMessage);

        if (typeof window !== "undefined") {
          window.scrollTo({ top: 0, behavior: "smooth" });
        }
        router.refresh();
      }

      if (res?.error) {
        setIsLoading(false);
        setError(res.error);

        if (typeof window !== "undefined") {
          window.scrollTo({ top: 0, behavior: "smooth" });
        }
      }

      setIsLoading(false);
    } catch (error: any) {
      setIsLoading(false);
      const errorMessage = error?.message || "An error occurred";
      setError(errorMessage);

      if (typeof window !== "undefined") {
        window.scrollTo({ top: 0, behavior: "smooth" });
      }
    }
  };

  const removeFileBuilders = (obj: any): any => {
    if (typeof obj !== "object" || obj === null) return obj;

    if (Array.isArray(obj)) {
      return obj.map((item) => removeFileBuilders(item));
    }

    const cleaned = { ...obj };

    if (Array.isArray(cleaned.builders)) {
      cleaned.builders = cleaned.builders
        .filter((builder: any) => builder?.type !== "file")
        .map(removeFileBuilders);
    }

    for (const key in cleaned) {
      if (cleaned.hasOwnProperty(key) && key !== "builders") {
        cleaned[key] = removeFileBuilders(cleaned[key]);
      }
    }

    return cleaned;
  };

  const cleanedData = removeFileBuilders([clientInfo?.kycConfiguration?.form]);

  const defaultValues = {
    firstName: viewer?.me?.firstName || "",
    lastName: viewer?.me?.lastName || "",
    phoneNumber: viewer?.me?.phone ? `0${viewer?.me?.phone}` : "",
    email: viewer?.me?.email || "",
  };

  return (
    <Stack>
      <Stack
        py={4}
        direction={{ base: "column", lg: "row" }}
        spacing={{ base: 4, xl: 20 }}
      >
        <VStack
          w={{ base: "fit-content", lg: "300px" }}
          alignItems='flex-start'
          spacing={0.5}
        >
          <Heading size={{ base: "sm", lg: "md" }}>User Details</Heading>
          <Text color='gray.500'>Update your details</Text>
        </VStack>
        <Box w={{ base: "100%", lg: "65%", xl: "55%" }}>
          {error && (
            <Alert
              status='error'
              borderRadius={6}
              mb={4}
            >
              <AlertIcon />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert
              status='success'
              borderRadius={6}
              mb={4}
            >
              <AlertIcon />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <Stack
            spacing={6}
            pt={6}
          >
            <Stack
              direction={{ base: "column", lg: "row" }}
              spacing={4}
            >
              <FormControl>
                <FormLabel>First Name</FormLabel>
                <Input
                  type='text'
                  placeholder='Enter your first name'
                  defaultValue={defaultValues?.firstName}
                  disabled
                />
              </FormControl>

              <FormControl>
                <FormLabel>Last Name</FormLabel>
                <Input
                  type='text'
                  placeholder='Enter your last name'
                  defaultValue={defaultValues?.lastName}
                  disabled
                />
              </FormControl>
            </Stack>
            <FormControl>
              <FormLabel>
                <Flex
                  alignItems='center'
                  justifyContent='space-between'
                >
                  <Text>Email</Text>
                  {!viewer?.me?.isEmailConfirmed && (
                    <EmailVerify
                      triggerOtp={triggerOtp}
                      confirmEmail={confirmEmail}
                      email={viewer?.me?.email || ""}
                    />
                  )}
                </Flex>
              </FormLabel>
              <Input
                type='email'
                placeholder='Enter your email address'
                defaultValue={defaultValues?.email}
                disabled
              />
            </FormControl>

            <FormControl>
              <FormLabel mb='-2px'>Phone Number</FormLabel>

              <NumericInput
                maxLength={11}
                allowLeadingZeros
                defaultValue={defaultValues?.phoneNumber}
                placeholder='Enter your phone number'
                thousandSeparator={false}
                isPhone
                disabled
              />
            </FormControl>
            <Box mt='-30px'>
              <OriginateFormGeneratorV2
                onSubmit={onSubmit}
                step={0}
                tabs={cleanedData}
                defaultFormValues={viewer?.me?.kycInformation?.data}
                theme={theme}
                kyc={true}
                disabled={isLoading}
              >
                <ButtonGroup
                  justifyContent='right'
                  w='full'
                >
                  <Button
                    type='submit'
                    isLoading={isLoading}
                    colorScheme='customBrand'
                    px={6}
                  >
                    Update Profile
                  </Button>
                </ButtonGroup>
              </OriginateFormGeneratorV2>
            </Box>
          </Stack>
        </Box>
      </Stack>
    </Stack>
  );
};

export default Profile;
