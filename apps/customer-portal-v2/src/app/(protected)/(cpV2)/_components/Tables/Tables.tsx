"use client";

import React, { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { SearchIcon } from "lucide-react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Box,
  Divider,
  Flex,
  FormControl,
  Heading,
  Input,
  InputGroup,
  InputLeftElement,
  Stack,
} from "@chakra-ui/react";
import useDebounce from "@/src/hooks/useDebounce";
import { fetchMoreApplications } from "@/src/app/actions";
import { LoanHistoryTable } from "../LoanHistoryTable";

const searchSchema = z.object({
  search: z.string().optional(),
});

type SearchFormValues = z.infer<typeof searchSchema>;

interface TablesProps {
  initialData: any | null;
  searchParams: any;
  error: string | null;
}

const Tables = ({ initialData, searchParams, error }: TablesProps) => {
  const router = useRouter();
  const urlSearchParams = useSearchParams();
  const pathname = usePathname();
  const [data, setData] = useState(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [currentError, setCurrentError] = useState(error);
  const [allItems, setAllItems] = useState<any[]>(
    initialData?.applications?.edges || []
  );
  const [hasNextPage, setHasNextPage] = useState(
    initialData?.applications?.pageInfo?.hasNextPage || false
  );
  const [endCursor, setEndCursor] = useState(
    initialData?.applications?.pageInfo?.endCursor || null
  );

  const applications = data?.applications;
  const totalCount = applications?.totalCount || 0;

  const { register, watch } = useForm<SearchFormValues>({
    resolver: zodResolver(searchSchema),
    defaultValues: { search: searchParams?.search || "" },
  });

  const searchValue = watch("search");
  const debouncedSearch = useDebounce(searchValue, 500);

  const createQueryString = useCallback(
    (params: Record<string, string>) => {
      const newParams = new URLSearchParams(urlSearchParams.toString());

      Object.entries(params).forEach(([name, value]) => {
        if (value && value.trim()) {
          newParams.set(name, value.trim());
        } else {
          newParams.delete(name);
        }
      });

      return newParams.toString();
    },
    [urlSearchParams]
  );

  const handleSearch = useCallback(
    (value: string) => {
      const queryString = createQueryString({ search: value });
      const newUrl = queryString ? `${pathname}?${queryString}` : pathname;

      router.push(newUrl);
    },
    [pathname, router, createQueryString]
  );

  const loadMoreData = useCallback(async () => {
    if (!hasNextPage || isLoadingMore || !endCursor) {
      return;
    }

    setIsLoadingMore(true);
    setCurrentError(null);

    try {
      const currentSearch = searchParams?.search || "";
      const res = await fetchMoreApplications(endCursor, currentSearch);

      if (res?.error) {
        setCurrentError(res.error);
      } else if (res?.data) {
        const newData = res?.data?.newData;
        const newEdges = res?.data?.newEdges || [];

        if (newEdges.length > 0) {
          setAllItems((prev) => {
            const existingIds = new Set(prev.map((item) => item.node.id));
            const uniqueNewEdges = newEdges.filter(
              (edge: any) => !existingIds.has(edge.node.id)
            );
            return [...prev, ...uniqueNewEdges];
          });
        }

        setHasNextPage(newData?.applications?.pageInfo?.hasNextPage || false);
        setEndCursor(newData?.applications?.pageInfo?.endCursor || null);
      }
    } catch (err) {
      setCurrentError(
        err instanceof Error
          ? err.message
          : "An error occurred while loading more data"
      );
    } finally {
      setIsLoadingMore(false);
    }
  }, [
    hasNextPage,
    isLoadingMore,
    endCursor,
    fetchMoreApplications,
    searchParams?.search,
  ]);

  useEffect(() => {
    const currentSearch = searchParams?.search || "";
    if (debouncedSearch !== currentSearch) {
      setIsLoading(true);
      handleSearch(debouncedSearch!);
    }
  }, [debouncedSearch, handleSearch, searchParams?.search]);

  useEffect(() => {
    setData(initialData);
    setCurrentError(error);
    setAllItems(initialData?.applications?.edges || []);
    setHasNextPage(initialData?.applications?.pageInfo?.hasNextPage);
    setEndCursor(initialData?.applications?.pageInfo?.endCursor || null);
    setIsLoading(false);
  }, [initialData, error]);

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(searchValue || "");
  };

  return (
    <Box
      py={6}
      px={{ base: 0, md: 6 }}
      borderRadius={6}
      boxShadow='sm'
      bg='white'
      maxW='8xl'
      mb={4}
    >
      <Stack spacing={3}>
        <Flex
          justify='space-between'
          align='center'
          flexDir={{ base: "column", sm: "row" }}
          gap={4}
          w='full'
          px={{ base: 4, md: 0 }}
        >
          <Heading
            size='sm'
            w='full'
          >
            Loan History
          </Heading>

          <FormControl
            as='form'
            maxW={{ base: "full", sm: "300px" }}
            onSubmit={handleFormSubmit}
          >
            <InputGroup>
              <InputLeftElement color='gray.400'>
                <SearchIcon size={18} />
              </InputLeftElement>
              <Input
                placeholder='Search by Loan ID'
                type='search'
                {...register("search")}
                colorScheme='customBrand'
              />
            </InputGroup>
          </FormControl>
        </Flex>

        <Divider />

        <LoanHistoryTable
          data={allItems}
          dataCount={totalCount}
          isLoading={isLoading}
          isLoadingMore={isLoadingMore}
          dataError={currentError || ""}
          isFiltered={!!searchParams?.search}
          hasNextPage={totalCount > allItems?.length}
          onLoadMore={loadMoreData}
        />
      </Stack>
    </Box>
  );
};

export default Tables;
