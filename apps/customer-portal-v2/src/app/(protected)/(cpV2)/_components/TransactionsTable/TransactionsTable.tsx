"use client";

import { fetchMoreTransactions } from "@/src/app/actions";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Button,
  Flex,
  Stack,
  useMediaQuery,
} from "@chakra-ui/react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { ListItem } from "./_components/ListItem";
import { ListItemSkeleton } from "./_components/ListItemSkeleton";
import { ListRow } from "./_components/ListRow";
import { LoanApplicationsEmpty } from "./_components/LoanApplicationsEmpty";

interface TransactionsTableProps {
  initialData: {
    transactions: any[];
    pageInfo: {
      hasNextPage: boolean;
      endCursor: string | null;
    };
    totalCount: number;
  } | null;
  error: string | null;
}

const TransactionsTable = ({ initialData, error }: TransactionsTableProps) => {
  const [isMeduimDevice] = useMediaQuery("(min-width: 1024px)");
  const [data, setData] = useState(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [currentError, setCurrentError] = useState(error);
  const [allItems, setAllItems] = useState<any[]>(
    initialData?.transactions || []
  );
  const totalCount = data?.totalCount || 0;
  const hasNextPage = totalCount > allItems?.length;
  const [endCursor, setEndCursor] = useState(
    initialData?.pageInfo?.endCursor || null
  );
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const loadMoreData = useCallback(async () => {
    if (!hasNextPage || isLoadingMore || !endCursor) {
      return;
    }

    setIsLoadingMore(true);
    setCurrentError(null);

    try {
      const res = await fetchMoreTransactions(endCursor);

      if (res?.error) {
        setCurrentError(res.error);
      } else if (res?.data) {
        const newData = res?.data?.newData;
        const newEdges = res?.data?.newEdges || [];

        if (newEdges.length > 0) {
          setAllItems((prev) => {
            const existingIds = new Set(prev.map((item) => item.node.id));
            const uniqueNewEdges = newEdges.filter(
              (edge: any) => !existingIds.has(edge.node.id)
            );
            return [...prev, ...uniqueNewEdges];
          });
        }

        setEndCursor(newData?.pageInfo?.endCursor || null);
      }
    } catch (err) {
      setCurrentError(
        err instanceof Error
          ? err.message
          : "An error occurred while loading more data"
      );
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasNextPage, isLoadingMore, endCursor]);

  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const target = entries[0];
      if (
        target.isIntersecting &&
        hasNextPage &&
        !isLoadingMore &&
        !isLoading
      ) {
        loadMoreData();
      }
    },
    [hasNextPage, isLoadingMore, isLoading, loadMoreData]
  );

  useEffect(() => {
    setData(initialData);
    setCurrentError(error);
    setAllItems(initialData?.transactions || []);
    setEndCursor(initialData?.pageInfo?.endCursor || null);
    setIsLoading(false);
  }, [initialData, error]);

  useEffect(() => {
    const observer = new IntersectionObserver(handleObserver, {
      threshold: 0.1,
      rootMargin: "100px",
    });

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [handleObserver]);

  return (
    <Stack
      spacing={0}
      flex={1}
      border='1px solid'
      borderColor='gray.200'
      borderRadius={6}
      minH='max-content'
    >
      <>
        {isMeduimDevice && (
          <ListRow isHeader>
            <Flex ml={{ base: 0, md: 6 }}>Status</Flex>
            <Flex>ID</Flex>
            <Flex>Amount</Flex>
            <Flex>Method</Flex>
            <Flex>Type</Flex>
            <Flex>Date</Flex>
          </ListRow>
        )}

        <Stack spacing={0}>
          {isLoading ? (
            <Stack>
              {[...Array(5)].map((_, i) => (
                <ListItemSkeleton key={i} />
              ))}
            </Stack>
          ) : currentError ? (
            <Flex
              py={4}
              mx={8}
            >
              <Alert status='error'>
                <AlertIcon />
                <AlertDescription>{currentError}</AlertDescription>
              </Alert>
            </Flex>
          ) : !isLoading && totalCount === 0 ? (
            <LoanApplicationsEmpty />
          ) : (
            <>
              {allItems?.map((item: any, index: number) => (
                <ListItem
                  key={`${item?.node?.id}-${index}`}
                  transaction={item?.node}
                />
              ))}

              {isLoadingMore && (
                <Stack>
                  {[...Array(3)].map((_, i) => (
                    <ListItemSkeleton key={`loading-${i}`} />
                  ))}
                </Stack>
              )}
            </>
          )}

          {hasNextPage && !isLoadingMore && allItems.length > 0 && (
            <div
              ref={loadMoreRef}
              style={{
                height: "20px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Button
                variant='ghost'
                size='sm'
                onClick={loadMoreData}
                disabled={isLoadingMore}
                _hover={{ bg: "gray.50" }}
              >
                Load More
              </Button>
            </div>
          )}

          {!hasNextPage && allItems.length > 0 && (
            <Flex
              justify='center'
              align='center'
              py={4}
              borderTop='1px solid'
              borderColor='gray.200'
              color='gray.500'
              fontSize='sm'
            >
              No more results to load
            </Flex>
          )}
        </Stack>
      </>
    </Stack>
  );
};

export default TransactionsTable;
