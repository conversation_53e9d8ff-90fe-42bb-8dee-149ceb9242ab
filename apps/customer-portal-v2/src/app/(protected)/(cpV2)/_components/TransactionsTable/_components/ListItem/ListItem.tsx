"use client";
import { formatAmount } from "@/utils/index";
import { Transaction } from "@/__generated/graphql";
import {
  Badge,
  Flex,
  Text,
  ThemeTypings,
  useMediaQuery,
} from "@chakra-ui/react";
import { format } from "date-fns";
import React from "react";
import { ListRow } from "../ListRow";

type ListItemProps = {
  transaction: Transaction;
};
const ListItem = ({ transaction }: ListItemProps) => {
  const renderStatus = (
    status: string
  ): { label: string; color: ThemeTypings["colorSchemes"] } => {
    switch (status) {
      case "success":
        return { label: "Success", color: "green" };
      case "reversed":
        return { label: "Reversed", color: "yellow" };
      case "failed":
        return { label: "Failed", color: "red" };
      default:
        return { label: "Pending", color: "blue" };
    }
  };

  const [isSmallDevice] = useMediaQuery("(max-width: 768px)");

  return (
    <Flex>
      <ListRow>
        {/* Status */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          ml={{ base: 0, lg: 6 }}
          _before={
            isSmallDevice
              ? {
                  content: "'Status'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Badge
            h={6}
            px={2}
            fontSize='xs'
            w='fit-content'
            display='flex'
            variant='solid'
            textAlign='center'
            alignItems='center'
            borderRadius={6}
            justifyContent='center'
            color={`${renderStatus(transaction?.status)?.color}.500`}
            bg={`${renderStatus(transaction?.status)?.color}.100`}
          >
            {renderStatus(transaction?.status)?.label}
          </Badge>
        </Flex>

        {/* ID*/}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'ID'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text textTransform='uppercase'>{transaction?.id}</Text>
        </Flex>

        {/* Amount */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Amount'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>{formatAmount(transaction?.amount)}</Text>
        </Flex>

        {/* Method */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Method'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>{transaction?.paymentMethod?.name}</Text>
        </Flex>

        {/* Type */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Type'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>{transaction?.type?.name}</Text>
        </Flex>

        {/*  Date */}
        <Flex
          justifyContent={{ base: "space-between", lg: "flex-start" }}
          _before={
            isSmallDevice
              ? {
                  content: "'Date'",
                  color: "gray.500",
                }
              : undefined
          }
        >
          <Text>
            {(transaction?.createdAt &&
              format(transaction?.createdAt, "MMM d, yyyy")) ||
              "N/A"}
          </Text>
        </Flex>
      </ListRow>
    </Flex>
  );
};
export default ListItem;
