"use client";
import { Flex, Grid } from "@chakra-ui/react";
import React, { ReactNode } from "react";

type ListRowProps = {
  children: ReactNode;
  isHeader?: boolean;
};

const ListRow = ({ children, isHeader }: ListRowProps) => {
  return (
    <Flex
      w='100%'
      alignItems='center'
      style={{ textDecoration: "none" }}
      cursor={!isHeader ? "pointer" : undefined}
    >
      <Grid
        p={{ base: 2, md: 4 }}
        flex={1}
        pos='relative'
        color={isHeader ? "gray.500" : "gray.700"}
        bg={isHeader ? "#D6DDEB80" : "white"}
        alignItems='center'
        textDecoration='none'
        wordBreak='break-all'
        gap={{ base: 4, lg: 0 }}
        borderBottom='1px solid'
        borderColor='gray.200'
        transitionDuration='slow'
        style={{ textDecoration: "none" }}
        fontWeight={isHeader ? "medium" : "normal"}
        templateColumns={{
          lg: "0.8fr 1.5fr repeat(4,1fr)",
        }}
      >
        {children}
      </Grid>
    </Flex>
  );
};
export default ListRow;
