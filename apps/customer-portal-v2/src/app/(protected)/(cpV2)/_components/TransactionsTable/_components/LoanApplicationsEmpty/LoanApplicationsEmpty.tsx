"use client";
import { Flex, Text } from "@chakra-ui/react";
import { FileX } from "lucide-react";
import React from "react";

const LoanApplicationsEmpty = () => {
  return (
    <Flex
      p={8}
      gap={4}
      flex={1}
      flexDir='column'
      alignItems='center'
      justifyContent='center'
      color='gray.400'
    >
      <FileX
        width='60px'
        height='60px'
      />
      <Text
        fontSize='lg'
        textAlign='center'
        color='gray.600'
      >
        You currently have no transactions.
      </Text>
    </Flex>
  );
};

export default LoanApplicationsEmpty;
