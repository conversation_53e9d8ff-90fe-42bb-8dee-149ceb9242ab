"use client";

import { Select } from "@/components/forms";
import {
  Application,
  Bank,
  ClientInfo,
  CreateApplicationTraceInput,
  Viewer,
} from "@/__generated/graphql";
import {
  Box,
  Heading,
  Button,
  Flex,
  Alert,
  AlertIcon,
  AlertDescription,
  Text,
  FormControl,
  Input,
  FormLabel,
  UnorderedList,
  ListItem,
  Stack,
  FormHelperText,
  FormErrorMessage,
  Center,
} from "@chakra-ui/react";
import { format, subMonths } from "date-fns";
import React, { useEffect, useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormSchema, FormValues } from "@/src/schema";
import { bytesToSize } from "@/utils/index";
import { FullPageLoader } from "@/components/FullPageLoader";
import { getCpV2ClientInfo } from "@/src/app/actions";
import useUploadBankService from "../../_services/UploadBankStatement/useUploadBankStatement";
import { useParams } from "next/navigation";

interface AccountInfo {
  label: string;
  value: string | undefined;
}

interface UploadBankStatementProps {
  application: Application;
  applicationNumber: string;
  banks: Bank[];
  createAppTrace: (params: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  viewer: Viewer;
  getDecideJobStatus: () => Promise<
    | {
        data: null;
        error: any;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  uploadAction: (formData: FormData) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
  >;
  allowBankSelectionOnPdfUpload: boolean;
}

const UploadBankStatement = ({
  application,
  applicationNumber,
  banks,
  createAppTrace,
  viewer,
  getDecideJobStatus,
  uploadAction,
  allowBankSelectionOnPdfUpload,
}: UploadBankStatementProps) => {
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const params = useParams();
  const basePath = params?.basePath as string;
  const path = basePath ?? null;
  const {
    handleSubmit,
    control,
    register,
    trigger,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    mode: "onChange",
    resolver: zodResolver(FormSchema),
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { uploadBankStatementFn, error, isPolling, setError } =
    useUploadBankService({
      applicationNumber,
      createAppTrace,
      getDecideJobStatus,
      uploadAction,
    });

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: path });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const { externalBankStatementTenor } = clientInfo || {};

  const applicationBank =
    application?.bankAccount || viewer?.account?.bankAccounts?.[0];

  const { accountName, accountNumber, bank } = applicationBank || {};

  const { id, name } = bank || {};

  const tenorValue = Number(externalBankStatementTenor || 3);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setValue("bankStatement", file);
      trigger("bankStatement");
    }
  };

  const onSubmit = async (data: FormValues) => {
    try {
      const { bankId, bankStatement, password } = data || {};

      const res = await uploadBankStatementFn({
        bankId: bankId! || id!,
        password: password || "",
        bankStatement: {
          validity: true,
          file: bankStatement!,
        },
      });
      if (res?.error) {
        setError(res.error);
      }
    } catch (error: any) {
      setError(error?.message);
    }
  };
  const accountInfoData: AccountInfo[] = useMemo(
    () => [
      { label: "Account Number", value: accountNumber },
      { label: "Account Name", value: accountName },
      { label: "Bank", value: name },
      {
        label: "Expected Duration",
        value: `${format(subMonths(new Date(), tenorValue), "MMM d, yyyy")} to ${format(new Date(), "MMM d, yyyy")}`,
      },
    ],
    [accountNumber, accountName, name, tenorValue]
  );

  useEffect(() => {
    register("bankStatement");
    register("bankId");
  }, [register]);

  const renderAccountInfo = () => (
    <Box
      textAlign='center'
      my={4}
      w='100%'
      bg='blackAlpha.100'
      borderRadius={6}
      p={4}
      data-testid='account-info'
    >
      <Text>Upload a pdf bank statement for the account details below:</Text>
      <Flex
        flexDir='column'
        mx='auto'
        w='fit-content'
        textAlign='left'
      >
        <UnorderedList>
          {accountInfoData?.map((accountInfo, index) => (
            <ListItem key={index}>
              {`${accountInfo?.label}:  `}
              <Box
                as='span'
                fontWeight={700}
              >
                {accountInfo?.value}
              </Box>
            </ListItem>
          ))}
        </UnorderedList>
      </Flex>
    </Box>
  );

  const renderBankSelection = () => (
    <FormControl isInvalid={!!errors?.bankId}>
      <FormHelperText
        mb={4}
        fontSize='md'
      >
        If uploading for a different bank, please select the bank below.
      </FormHelperText>
      <Controller
        name='bankId'
        control={control}
        render={({ field }) => (
          <Select
            {...field}
            placeholder='--Select a Bank--'
            label='Bank'
            isInvalid={!!errors?.bankId}
            errorMessage={errors?.bankId?.message}
            data-testid='bank-select'
          >
            {banks?.map((data, idx) => (
              <option
                key={idx}
                value={data?.id}
              >
                {data?.name}
              </option>
            ))}
          </Select>
        )}
      />
    </FormControl>
  );

  const DecidePolling = () => (
    <Center>
      <Stack
        spacing={6}
        align='center'
        textAlign='center'
        data-testid='decide-polling'
      >
        <svg
          width='101'
          height='100'
          viewBox='0 0 101 100'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <rect
            x='0.5'
            width='100'
            height='100'
            rx='50'
            fill='url(#paint0_linear_5451_5087)'
          />
          <path
            d='M56.2013 40H45.7986C44.3409 40 43.0619 40.9847 43.0035 42.4036C42.93 44.1888 44.1855 45.3742 45.5042 46.4871C47.3285 48.0266 48.2406 48.7964 48.3363 49.7708C48.3513 49.9233 48.3513 50.0767 48.3363 50.2292C48.2406 51.2036 47.3285 51.9734 45.5042 53.5129C44.1492 54.6564 42.9262 55.7195 43.0035 57.5964C43.0619 59.0153 44.3409 60 45.7986 60H56.2013C57.659 60 58.938 59.0153 58.9964 57.5964C59.0429 56.4668 58.6243 55.342 57.7351 54.56C57.3297 54.2034 56.9088 53.8615 56.4957 53.5129C54.6714 51.9734 53.7593 51.2036 53.6636 50.2292C53.6486 50.0767 53.6486 49.9233 53.6636 49.7708C53.7593 48.7964 54.6714 48.0266 56.4957 46.4871C57.8365 45.3556 59.0728 44.2581 58.9964 42.4036C58.938 40.9847 57.659 40 56.2013 40Z'
            stroke='#0235DD'
            strokeWidth='2'
          />
          <path
            d='M48 59.6381C48 59.1962 48 58.9752 48.0876 58.7821C48.1015 58.7514 48.117 58.7214 48.134 58.6923C48.241 58.509 48.4221 58.3796 48.7843 58.1208C49.7905 57.4021 50.2935 57.0427 50.8652 57.0045C50.955 56.9985 51.045 56.9985 51.1348 57.0045C51.7065 57.0427 52.2095 57.4021 53.2157 58.1208C53.5779 58.3796 53.759 58.509 53.866 58.6923C53.883 58.7214 53.8985 58.7514 53.9124 58.7821C54 58.9752 54 59.1962 54 59.6381V60H48V59.6381Z'
            stroke='#0235DD'
            strokeWidth='2'
            strokeLinecap='round'
          />
          <defs>
            <linearGradient
              id='paint0_linear_5451_5087'
              x1='50.5'
              y1='0'
              x2='50.5'
              y2='100'
              gradientUnits='userSpaceOnUse'
            >
              <stop
                offset='0.446547'
                stopColor='#FAFAFA'
              />
              <stop
                offset='0.740998'
                stopColor='#BBC8F3'
              />
              <stop
                offset='1'
                stopColor='#0235DD'
              />
            </linearGradient>
          </defs>
        </svg>

        <Text>Application Processing</Text>
        <Text>
          Please wait while we process your application, this takes less than 60
          seconds to complete.
        </Text>
      </Stack>
    </Center>
  );

  const renderFileUpload = () => (
    <FormControl
      isInvalid={!!errors.bankStatement}
      mb={4}
    >
      <Flex
        alignItems='left'
        flexDir='column'
        w='100%'
      >
        <FormLabel>Bank Statement Upload</FormLabel>
        <Box
          flex='1'
          w='100%'
          display='flex'
          flexDir='column'
        >
          <Box
            border='1px dashed'
            h='220px'
            w='100%'
            cursor='pointer'
            position='relative'
            overflow='hidden'
            borderRadius={6}
            borderColor={!!errors.bankStatement ? "red.500" : "gray.300"}
            data-testid='file-upload-container'
          >
            <Flex
              h='100%'
              alignItems='center'
              justifyContent='center'
            >
              {selectedFile ? (
                <Flex
                  border='1px solid'
                  height='80%'
                  borderRadius={6}
                  width='50%'
                  p={4}
                  flexDirection='column'
                  alignItems='center'
                  justifyContent='center'
                  borderColor={!!errors.bankStatement ? "red.500" : "gray.300"}
                >
                  <Text
                    isTruncated
                    maxWidth='90%'
                  >
                    {selectedFile.name}
                  </Text>
                  <Text>{bytesToSize(selectedFile.size)}</Text>
                </Flex>
              ) : (
                <svg
                  width={80}
                  height={80}
                  viewBox='0 0 36 36'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M21 3H9a3 3 0 00-3 3v24a3 3 0 003 3h18a3 3 0 003-3V12l-9-9z'
                    stroke='#6E798F'
                    strokeWidth={1.5}
                    strokeLinecap='round'
                    strokeLinejoin='round'
                  />
                  <path
                    d='M21 3v9h9M24 19.5H12M24 25.5H12M15 13.5h-3'
                    stroke='#6E798F'
                    strokeWidth={1.5}
                    strokeLinecap='round'
                    strokeLinejoin='round'
                  />
                </svg>
              )}
            </Flex>
            <Input
              type='file'
              accept='application/pdf'
              position='absolute'
              top={0}
              left={0}
              height='100%'
              width='100%'
              opacity={0}
              cursor='pointer'
              onChange={handleFileChange}
            />
          </Box>
          <FormHelperText>
            Upload PDF file and file size should not exceed 10MB
          </FormHelperText>
          <FormErrorMessage data-testid='file-upload-error'>
            {errors?.bankStatement?.message}
          </FormErrorMessage>
        </Box>
      </Flex>
    </FormControl>
  );

  const renderPasswordInput = () => (
    <FormControl isInvalid={!!errors?.password}>
      <FormLabel>Password</FormLabel>

      <Input
        placeholder='Enter bank statement password'
        maxLength={6}
        {...register("password")}
        data-testid='password-input'
      />
      <FormHelperText
        my={2}
        data-testid='password-helper-text'
      >
        Please enter document password (if any)
      </FormHelperText>
    </FormControl>
  );

  if (!clientInfo) return <FullPageLoader />;
  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      {isPolling ? (
        <DecidePolling />
      ) : (
        <>
          <Heading
            as='h2'
            fontSize='24px'
            mb={2}
            textAlign='center'
            data-testid='heading-title'
          >
            Upload your bank statement
          </Heading>
          <Text
            mb={6}
            textAlign='center'
            data-testid='heading-subtitle'
          >
            {`Please upload your bank statement for the last ${tenorValue} months`}
          </Text>

          {error && (
            <Alert
              status='error'
              data-testid='error-alert'
            >
              <AlertIcon />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {renderAccountInfo()}

          <Stack
            as='form'
            spacing={8}
            w='full'
            onSubmit={handleSubmit(onSubmit)}
          >
            {allowBankSelectionOnPdfUpload ? renderBankSelection() : null}
            {renderFileUpload()}
            {renderPasswordInput()}

            <Button
              type='submit'
              isLoading={isSubmitting}
              w='full'
              px={8}
              data-testid='next-button'
            >
              Next
            </Button>
          </Stack>
        </>
      )}
    </Box>
  );
};

export default UploadBankStatement;
