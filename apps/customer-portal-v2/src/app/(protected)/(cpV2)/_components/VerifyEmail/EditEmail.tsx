"use client";
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  useDisclosure,
  Text,
  Stack,
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  ButtonGroup,
  Alert,
  AlertIcon,
  AlertDescription,
} from "@chakra-ui/react";
import { useForm } from "react-hook-form";
import { EditEmailSchema, EditEmailType } from "@/src/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";

type EditEmailProps = {
  editEmail: ({ email }: { email: string }) => Promise<
    | {
        success: boolean;
        redirect: boolean;
        error?: undefined;
      }
    | {
        success: boolean;
        redirect?: undefined;
        error?: undefined;
      }
    | {
        success: boolean;
        error: any;
        redirect?: undefined;
      }
    | undefined
  >;
  setUserEmail: React.Dispatch<React.SetStateAction<string>>;
  currentEmail: string;
  sendInitialOtp: ({ messageEmail }: { messageEmail: string }) => Promise<void>;
};

const EditEmail = ({
  editEmail,
  setUserEmail,
  currentEmail,
  sendInitialOtp,
}: EditEmailProps) => {
  const [isEmailUpdated, setIsEmailUpdated] = useState(false);
  const { error, setError } = useMessageTimer();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<EditEmailType>({
    resolver: zodResolver(EditEmailSchema),
    mode: "onChange",
    defaultValues: {
      email: currentEmail || "",
    },
  });

  const closeModal = () => {
    reset();
    setError("");
    setIsEmailUpdated(false);
    onClose();
  };

  useEffect(() => {
    reset({ email: currentEmail });
  }, [currentEmail, reset]);

  const onSubmit = async (data: any) => {
    try {
      setError("");
      const res = await editEmail({ email: data?.email });

      if (res?.error) {
        setError(res?.error);
      }

      if (res?.success) {
        localStorage.removeItem("otpRequested");
        setUserEmail(data?.email);
        setIsEmailUpdated(true);
        setTimeout(() => {
          sendInitialOtp({
            messageEmail: data?.email,
          });
          closeModal();
        }, 1000);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to update email");
    }
  };

  const onOpenModal = () => {
    setError("");
    setIsEmailUpdated(false);
    onOpen();
  };

  return (
    <>
      <Button
        variant='link'
        onClick={onOpenModal}
        mx={1}
        fontWeight='bold'
        colorScheme='customPrimary'
        _hover={{
          color: "customPrimary.600",
          textDecoration: "underline",
        }}
        data-testid='edit-email-button'
      >
        Edit Email
      </Button>

      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        size='lg'
      >
        <ModalOverlay />
        <ModalContent
          pos='fixed'
          top='20%'
          transform='translate(-50%, -50%)'
        >
          <ModalHeader data-testid='modal-header'>Edit your email</ModalHeader>
          <ModalCloseButton data-testid='modal-close-button' />
          <ModalBody px={4}>
            {isEmailUpdated && (
              <Alert
                status='success'
                w='full'
                mb={2}
                borderRadius={6}
                data-testid='success-alert'
              >
                <AlertIcon />
                <AlertDescription
                  whiteSpace='pre-wrap'
                  wordBreak='break-word'
                >
                  Email updated successfully
                </AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert
                status='error'
                w='full'
                my={4}
                borderRadius={6}
                data-testid='error-alert'
              >
                <AlertIcon />
                <AlertDescription
                  whiteSpace='pre-wrap'
                  wordBreak='break-word'
                >
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <Text textAlign={{ base: "center", md: "left" }}>
              Please enter your email so we can send your verification OTP
            </Text>
            <Stack
              as='form'
              spacing={8}
              py={6}
              w='full'
            >
              <FormControl isInvalid={!!errors.email}>
                <FormLabel>Email</FormLabel>
                <Input
                  type='email'
                  autoFocus
                  placeholder='Enter your email address'
                  {...register("email")}
                  data-testid='email-input'
                />
                <FormErrorMessage data-testid='email-error'>
                  {errors?.email?.message}
                </FormErrorMessage>
              </FormControl>
              <ButtonGroup
                flexDirection={{ base: "column", md: "row" }}
                w='full'
              >
                <Button
                  w='full'
                  type='button'
                  isLoading={isSubmitting}
                  onClick={handleSubmit(onSubmit)}
                  data-testid='update-email-button'
                >
                  Update Email
                </Button>
              </ButtonGroup>
            </Stack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default EditEmail;
