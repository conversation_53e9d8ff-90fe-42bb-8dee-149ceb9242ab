"use client";
import { EmailOTPType, EmailOTPValidationSchema } from "@/src/schema";
import {
  Box,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Stack,
  Text,
  AlertDescription,
  AlertIcon,
  Alert,
  Flex,
} from "@chakra-ui/react";
import React, { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { NumericInput } from "@/components/forms";
import { Controller, useForm } from "react-hook-form";
import EditEmail from "./EditEmail";
import { useRouter } from "next/navigation";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";

type VerifyEmailProps = {
  email: string;
  editEmail: ({ email }: { email: string }) => Promise<
    | {
        success: boolean;
        redirect: boolean;
        error?: undefined;
      }
    | {
        success: boolean;
        redirect?: undefined;
        error?: undefined;
      }
    | {
        success: boolean;
        error: any;
        redirect?: undefined;
      }
    | undefined
  >;
  confirmEmail: ({ code }: { code: string }) => Promise<any>;
  triggerOtp: () => Promise<any>;
  path: string;
  createAppTrace?: ({
    page,
    comment,
  }: {
    page: string;
    comment: string;
  }) => Promise<any>;
};

const VerifyEmail = ({
  email = "",
  editEmail,
  confirmEmail,
  triggerOtp,
  path,
  createAppTrace,
}: VerifyEmailProps) => {
  const router = useRouter();
  const [userEmail, setUserEmail] = useState(email);
  const { error, setError, setSuccess, success } = useMessageTimer();
  const [isResending, setIsResending] = useState(false);

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<EmailOTPType>({
    resolver: zodResolver(EmailOTPValidationSchema),
    mode: "onChange",
  });

  useEffect(() => {
    const otpStatus = localStorage.getItem("otpRequested");
    if (!otpStatus) {
      sendInitialOtp({
        messageEmail: userEmail,
      });
    }
  }, []);

  const sendInitialOtp = async ({ messageEmail }: { messageEmail: string }) => {
    try {
      setError("");
      const result = await triggerOtp();

      if (result?.success) {
        localStorage.setItem("otpRequested", "true");
        setSuccess(`OTP sent to ${messageEmail} successfully`);
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to send OTP");
    }
  };

  const resendOtp = async () => {
    try {
      setError("");
      setIsResending(true);
      const result = await triggerOtp();

      if (result?.success) {
        localStorage.setItem("otpRequested", "true");
        setSuccess(`OTP resent to ${userEmail} successfully`);
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to resend OTP");
    } finally {
      setIsResending(false);
    }
  };

  const onSubmit = async (data: EmailOTPType) => {
    try {
      setError("");
      const result = await confirmEmail({
        code: data?.workEmailVerificationCode.trim(),
      });

      if (result?.success) {
        localStorage.removeItem("otpRequested");

        if (result.redirect) {
          if (createAppTrace) {
            await createAppTrace({
              page: "Email verification",
              comment: "Email verification completed",
            });
          }
          router.push(path);
        }

        reset();
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (error: any) {
      setError(error?.message || "Failed to verify email");
    }
  };

  // eslint-disable-next-line no-console
  console.log({ error });

  return (
    <Box
      w={{ base: "full", md: "xl" }}
      bg='white'
      py={8}
      px={{ base: 4, md: 8 }}
      borderRadius='md'
      boxShadow='sm'
    >
      <Heading
        as='h2'
        fontSize='20px'
        mb={2}
        textAlign='center'
        data-testid='verify-email-title'
      >
        Verify your e-mail
      </Heading>
      <Text
        mb={6}
        textAlign='center'
        data-testid='verify-email-description'
      >
        {`Please enter verification code sent to`}{" "}
        <strong>{userEmail || ""}</strong>
      </Text>

      {error && (
        <Alert
          status='error'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='verify-email-error-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {error}
          </AlertDescription>
        </Alert>
      )}

      {success && userEmail && (
        <Alert
          status='success'
          w='full'
          my={4}
          borderRadius={6}
          data-testid='verify-email-success-alert'
        >
          <AlertIcon />
          <AlertDescription
            whiteSpace='pre-wrap'
            wordBreak='break-word'
          >
            {success}
          </AlertDescription>
        </Alert>
      )}

      <Stack
        as='form'
        spacing={8}
        w='full'
        onSubmit={handleSubmit(onSubmit)}
      >
        <FormControl isInvalid={!!errors?.workEmailVerificationCode}>
          <FormLabel>Verification Code</FormLabel>
          <Controller
            name='workEmailVerificationCode'
            control={control}
            defaultValue=''
            render={({ field }) => (
              <NumericInput
                maxLength={6}
                allowLeadingZeros
                placeholder='Enter Verification Code'
                thousandSeparator={false}
                value={field.value || ""}
                onChange={field.onChange}
                onBlur={field.onBlur}
                isInvalid={!!errors?.workEmailVerificationCode?.message}
                autoFocus
                data-testid='verify-email-code-input'
              />
            )}
          />
          <FormErrorMessage
            fontSize='sm'
            data-testid='verify-email-error-message'
          >
            {errors?.workEmailVerificationCode?.message}
          </FormErrorMessage>
        </FormControl>

        <Flex
          justifyContent='center'
          alignItems='center'
          flexDirection='column'
        >
          <Text
            textAlign='center'
            mb={2}
          >
            Didn't get OTP?
          </Text>
          <Flex alignItems='center'>
            <Button
              variant='link'
              onClick={resendOtp}
              mx={1}
              isLoading={isResending}
              loadingText='Sending...'
              disabled={isResending}
              colorScheme='customPrimary'
              fontWeight='bold'
              _hover={{
                color: "customPrimary.600",
                textDecoration: "underline",
              }}
              data-testid='verify-email-resend-otp-button'
            >
              Resend OTP
            </Button>
            <Text>/</Text>
            <EditEmail
              editEmail={editEmail}
              setUserEmail={setUserEmail}
              currentEmail={userEmail}
              sendInitialOtp={sendInitialOtp}
            />
          </Flex>
        </Flex>

        <Button
          type='submit'
          isLoading={isSubmitting}
          w='full'
          px={8}
          data-testid='verify-email-submit-button'
        >
          Verify Email
        </Button>
      </Stack>
    </Box>
  );
};

export default VerifyEmail;
