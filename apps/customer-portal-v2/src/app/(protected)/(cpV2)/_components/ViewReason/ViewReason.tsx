import {
  <PERSON>ton,
  <PERSON>dal,
  Modal<PERSON>verlay,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Center,
  Stack,
  HStack,
  Text,
  Box,
} from "@chakra-ui/react";
import React from "react";

const ViewReason = ({ rejectReason }: { rejectReason: string }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const splitCommaToArray = (data: string) => {
    return data
      ? data
          .split(",")
          .map((item) => item.trim())
          .filter((item) => item !== "")
      : [];
  };

  return (
    <>
      <Button
        variant='link'
        onClick={onOpen}
        colorScheme='black'
      >
        View Reason
      </Button>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size='xl'
      >
        <ModalOverlay />
        <ModalContent
          pos='fixed'
          top='20%'
          transform='translate(-50%, -50%)'
        >
          <ModalHeader
            textAlign={{ md: "center" }}
            fontSize={{ base: "md", md: "2xl" }}
          >
            Why didn't you get this loan?
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <Center>
              <Stack spacing={4}>
                {splitCommaToArray(rejectReason).map((reason, index) => (
                  <HStack
                    key={index}
                    spacing={4}
                    textAlign='center'
                  >
                    <Box
                      w={6}
                      h={6}
                      bg='red.500'
                      textAlign='center'
                      color='white'
                      fontWeight='extrabold'
                      borderRadius='md'
                    >
                      !
                    </Box>
                    <Text size='lg'>{reason}.</Text>
                  </HStack>
                ))}
              </Stack>
            </Center>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};
export default ViewReason;
