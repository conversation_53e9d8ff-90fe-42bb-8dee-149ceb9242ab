"use client";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { constants } from "@/utils/constants";
import { getAttributrFromProduct } from "@/utils/index";
import {
  Application,
  CardReferenceStatusInput,
  ClientInfo,
  CreateApplicationTraceInput,
  GenerateAddCardReferenceInput,
  ViewerQueryQuery,
} from "@/__generated/graphql";
import { useToast } from "@chakra-ui/react";
import { useParams, useRouter } from "next/navigation";
import { useCallback, useState, useMemo, useEffect } from "react";
import { usePaystackPayment } from "react-paystack";

type Props = {
  clientInfo: ClientInfo | null;
  viewer: ViewerQueryQuery["viewer"];
  application: Application;
  applicationNumber: string;
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  generateByPayStackRef: ({
    metadata,
  }: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  runSkipCardCollection: () => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  getByPayStackRef: ({
    reference,
    loanDuration,
  }: {
    reference: CardReferenceStatusInput["reference"];
    loanDuration: CardReferenceStatusInput["loanDuration"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
};

const useAddCard = ({
  application,
  applicationNumber,
  clientInfo,
  viewer,
  createAppTrace,
  generateByPayStackRef,
  runSkipCardCollection,
  getByPayStackRef,
}: Props) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath;

  const toast = useToast();
  const { error, setError } = useMessageTimer();
  const [reference, setReference] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const loanDuration = application?.loanDuration;
  const repaymentServices = clientInfo?.repaymentServices;
  const { id: accountId } = viewer?.account || {};
  const { id: applicationId } = application || {};
  const channels = ["card"];
  const email = viewer?.me?.email!;
  const amount = clientInfo?.addCardCharge || 100;
  const paystackPubKey = clientInfo?.paystackPubKey!;

  const loanCategoryAttributes =
    application?.loanCategory?.loanCategoryAttributes!;

  const namesSet = new Set(["allowCardSkipDuringApplication"]);

  const customerAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const skipAddCard = customerAttributes?.allowCardSkipDuringApplication;

  const collectionMethod = useMemo(
    () =>
      repaymentServices?.filter((service) => service?.name !== "CARD") || [],
    [repaymentServices]
  );

  const metadata = useMemo(
    () => ({
      cardReference: reference,
      accountId,
      loanDuration,
      transactionType: constants.transactType.transactionType,
      custom_fields: [],
    }),
    [accountId, reference, loanDuration]
  );

  const generatePayStackRef = useCallback(async () => {
    setError("");
    try {
      setIsSubmitting(true);

      const res = await generateByPayStackRef({
        metadata,
      });

      if (res?.error) {
        setIsSubmitting(false);
        const errorMsg =
          res?.error || "There was an error generating paystack reference";
        setError(errorMsg);
        createAppTrace({
          page: constants.pages.addCard,
          comment: errorMsg,
          isDebug: true,
          metadata: {
            error: res?.error,
          },
        });
        return;
      }
      setReference(res?.data?.reference);
      setIsSubmitting(false);
    } catch (error: any) {
      setError(error?.message);
      setIsSubmitting(false);
    }
  }, [metadata, createAppTrace, generateByPayStackRef]);

  const handleSkipCardCollection = useCallback(async () => {
    setError("");
    if (!applicationId) return;
    try {
      setIsSubmitting(true);
      const res = await runSkipCardCollection();
      if (res?.data?.skipCardCollection) {
        toast({
          title: "Add card",
          description: "Successfully skipped add debit card",
          status: "success",
          duration: constants.duration.LONG,
          isClosable: true,
          position: "top-right",
        });
        const path = basePath
          ? `/${basePath}/application/${applicationNumber}/bank-info`
          : `/application/${applicationNumber}/bank-info`;

        router.push(path);
        return;
      }
      setIsSubmitting(false);

      if (res?.error) {
        setIsSubmitting(false);
        const errorMsg =
          res?.error || "There was an error skipping add debit card";
        setError(errorMsg);
        createAppTrace({
          page: constants.pages.addCard,
          comment: errorMsg,
          isDebug: true,
          metadata: {
            error: res?.error,
          },
        });
      }
    } catch (error: any) {
      setIsSubmitting(false);
      setError(error?.message);
    }
  }, [
    applicationId,
    applicationNumber,
    router,
    toast,
    runSkipCardCollection,
    createAppTrace,
  ]);

  const generateAddCardRef = useCallback(
    () => generatePayStackRef(),
    [generatePayStackRef]
  );

  useEffect(() => {
    if (clientInfo?.clientId && !reference) {
      generateAddCardRef();
    }
  }, [clientInfo?.clientId, reference]);

  const config = useMemo(
    () => ({
      reference,
      email,
      amount: amount * 100,
      publicKey: paystackPubKey,
      channels,
      metadata,
    }),
    [reference, email, amount, paystackPubKey, channels, metadata]
  );

  const onSuccess = async ({ status }: { status: string }) => {
    switch (status) {
      case "success":
        try {
          const res = await getByPayStackRef({
            reference,
            loanDuration: String(loanDuration),
          });

          if (res?.data) {
            setIsSubmitting(true);
            toast({
              title: "Add Card",
              description: "Card added successfully",
              status: "success",
              duration: constants.duration.SHORT,
              isClosable: true,
              position: "top-right",
            });
            await createAppTrace({
              page: "Add card page",
              comment: "Successfully Added Card",
            });
            const path = basePath
              ? `/${basePath}/application/${applicationNumber}/bank-info`
              : `/application/${applicationNumber}/bank-info`;

            router.push(path);
          } else if (res?.error) {
            setIsSubmitting(false);
            setError(res.error || "Failed to verify card reference");
            await createAppTrace({
              page: constants.pages.addCard,
              comment: "Failed to verify card reference",
              isDebug: true,
              metadata: {
                error: res.error,
              },
            });
          }
        } catch (error: any) {
          setIsSubmitting(false);
          setError(error?.message || "An unexpected error occurred");
          await createAppTrace({
            page: constants.pages.addCard,
            comment: "Error processing card verification",
            isDebug: true,
            metadata: {
              error: error?.message,
            },
          });
        }
        break;

      default:
        setIsSubmitting(false);
        generateAddCardRef();
    }
  };

  const onClose = () => {
    toast({
      title: "Add Card",
      description: "Closed Paystack Widget",
      status: "warning",
      duration: constants.duration.LONG,
      isClosable: true,
      position: "top-right",
    });

    createAppTrace({
      page: "Add card page",
      comment: "Paysatck Popup closed",
    });
    setIsSubmitting(false);
    generateAddCardRef();
  };

  const initializePayment = usePaystackPayment(config);

  const handlePaystackWidgetTrigger = useCallback(() => {
    setIsSubmitting(true);
    createAppTrace({
      page: "Add card page",
      comment: "Triggered Paystack Widget",
    });

    if (initializePayment) {
      initializePayment({
        onSuccess,
        onClose,
      });
    }
  }, [initializePayment, createAppTrace]);
  const skipCardStep = () => {
    document.cookie = "skippedCardAddition=true";

    const path = basePath
      ? `/${basePath}/application/${applicationNumber}/bank-info`
      : `/application/${applicationNumber}/bank-info`;
    router.push(path);
  };

  return useMemo(
    () => ({
      reference,
      generateAddCardRef,
      handleSkipCardCollection,
      collectionMethod,
      setError,
      error,
      isSubmitting,
      handlePaystackWidgetTrigger,
      amount,
      skipCardStep,
      skipAddCard,
    }),
    [
      reference,
      generateAddCardRef,
      handleSkipCardCollection,
      collectionMethod,
      setError,
      error,
      isSubmitting,
      handlePaystackWidgetTrigger,
      amount,
      skipCardStep,
      skipAddCard,
    ]
  );
};
export default useAddCard;
