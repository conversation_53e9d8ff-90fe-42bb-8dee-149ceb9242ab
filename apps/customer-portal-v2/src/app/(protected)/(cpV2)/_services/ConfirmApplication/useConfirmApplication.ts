"use client";

import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { CreateApplicationTraceInput } from "@/__generated/graphql";
import { useCallback, useMemo, useState } from "react";

const useConfirmApplication = ({
  completeApplication,
  createAppTrace,
}: {
  completeApplication: () => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  createAppTrace: ({
    page,
    comment,
    isDebug,
    metadata,
  }: {
    page: CreateApplicationTraceInput["page"];
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
  }) => Promise<any>;
}) => {
  const [isCompleteLoading, setIsCompleteLoading] = useState(false);
  const { setError, error: offerError } = useMessageTimer({ duration: 10000 });

  const runCompleteApplication = useCallback(async () => {
    try {
      setIsCompleteLoading(true);
      const res = await completeApplication();

      if (res?.data) {
        setIsCompleteLoading(false);
        createAppTrace({
          page: "Breakdown Page",
          comment: "Application completed successfully",
        });
        return {
          data: res?.data,
          error: null,
        };
      }

      if (res?.error) {
        setError(res?.error || "An error occurred during your application.");
        createAppTrace({
          page: "Breakdown Page",
          comment: "Application failed to complete",
          isDebug: true,
          metadata: {
            error: res?.error || "An error occurred during your application.",
          },
        });
        setIsCompleteLoading(false);
        return {
          data: null,
          error: res?.error,
        };
      }

      setIsCompleteLoading(false);
    } catch (error: any) {
      setIsCompleteLoading(false);
      return {
        data: null,
        error: error?.message,
      };
    }
  }, []);

  return useMemo(
    () => ({
      isCompleteLoading,
      runCompleteApplication,
      offerError,
      setError,
    }),
    [isCompleteLoading, runCompleteApplication, offerError, setError]
  );
};

export default useConfirmApplication;
