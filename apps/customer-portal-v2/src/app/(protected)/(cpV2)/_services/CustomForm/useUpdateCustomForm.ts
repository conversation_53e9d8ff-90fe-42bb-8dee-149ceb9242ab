"use client";
import { getLatestForm, saveForm } from "@/src/app/actions";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { Application } from "@/__generated/graphql";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import useUploadHelper from "../UploadHelper/useUploadHelper";

type Props = {
  defaultValues: any;
  application: Application;
  currentStepName: string;
  createAppTrace: ({
    page,
    comment,
  }: {
    page: string;
    comment: string;
  }) => Promise<any>;
};

const useUpdateCustomForm = ({
  defaultValues,
  currentStepName,
  createAppTrace,
  application,
}: Props) => {
  const session = useSession();
  const token = session?.data?.accessToken!;
  const router = useRouter();

  const { setError, setSuccess, success, error } = useMessageTimer();
  const [defaultFormValues, setDefaultFormValues] = useState(defaultValues);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { user, id } = application || {};
  const customerId = user?.id || "";
  const applicationId = id || "";

  const { uploadError, handleUpdateForm } = useUploadHelper({
    user,
    token,
  });

  useEffect(() => {
    if (uploadError) {
      setError(uploadError);
    }
  }, [uploadError, setError]);

  const getLatestFormData = useCallback(async () => {
    if (!customerId || !token) return;
    setError("");

    try {
      const res = await getLatestForm({
        formData: { customerId },
        token,
      });
      if (res?.latestForm?.data) {
        const fetchedData = JSON.parse(JSON.stringify(res.latestForm.data));
        setDefaultFormValues(fetchedData);
      }

      if (res?.latestFormError) {
        setError(res?.latestFormError);
      }
    } catch (error: any) {
      setError(error?.message);
    }
  }, [customerId, token, setError, setSuccess]);

  const saveCustomForm = useCallback(
    async (pageData: any) => {
      try {
        const res = await saveForm({
          formData: { ...pageData },
          token,
        });
        if (res?.data?.saveCustomApplicationForm?.success) {
          setSuccess("Data updated successfully");
          await getLatestFormData();
        }

        if (res?.error) {
          setError(res?.error);
        }
      } catch (error: any) {
        setError(error?.message);
      }
    },
    [token, getLatestFormData, setError, setSuccess]
  );

  const handleSubmitForm = useCallback(
    async ({ data }: { data: FormData }) => {
      setError("");
      setIsSubmitting(true);

      try {
        if (!currentStepName) {
          throw new Error("Current step name is required");
        }

        if (!applicationId) {
          throw new Error("Application ID is required");
        }

        const res = await handleUpdateForm({ ...data }, currentStepName);

        if (res?.error) {
          setError(res?.error);
          return;
        }

        if (createAppTrace) {
          try {
            await createAppTrace({
              page: `${currentStepName} Tab`,
              comment: `Updated ${currentStepName} successfully`,
            });
          } catch (traceError: any) {}
        }

        const nextPayload = {
          applicationId,
          data: { ...res?.data },
        };

        await saveCustomForm(nextPayload);
        window.scrollTo({ top: 0, behavior: "smooth" });
        router.refresh();
      } catch (error: any) {
        const errorMessage =
          error?.message || "An unexpected error occurred during submission.";
        setError(errorMessage);
        window.scrollTo({ top: 0, behavior: "smooth" });
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      currentStepName,
      applicationId,
      createAppTrace,
      saveCustomForm,
      setError,
      setSuccess,
    ]
  );

  return {
    success,
    setSuccess,
    error,
    setError,
    defaultFormValues,
    setDefaultFormValues,
    saveCustomForm,
    isSubmitting,
    handleSubmitForm,
  };
};

export default useUpdateCustomForm;
