"use client";

import React, {
  createContext,
  useContext,
  useState,
  useMemo,
  ReactNode,
  useEffect,
  useCallback,
} from "react";
import { Application, ClientInfo, Viewer } from "@/__generated/graphql";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { getCpV2ClientInfo, getLatestForm, saveForm } from "@/src/app/actions";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import useUploadHelper from "../UploadHelper/useUploadHelper";

type FormData = {
  [key: string]: any;
};

interface FormBuilderContextType {
  formIndex: number;
  setFormIndex: React.Dispatch<React.SetStateAction<number>>;
  handleSubmit: ({ data }: { data: FormData }) => Promise<void>;
  isSubmitting: boolean;
  applicationForm: any;
  setApplicationForm: React.Dispatch<React.SetStateAction<any>>;
  handleNextPage: any;
  error: string | null;
  handleUpdateForm: (
    formData: FormData,
    name: string
  ) => Promise<
    | {
        data: null;
        error: string;
      }
    | {
        data: FormData;
        error: null;
      }
    | undefined
  >;
  handleNext: (pageData: any) => Promise<void>;
  customPath: string;
  defaultFormValues: any;
  clientInfo: ClientInfo | null;
  application: Application;
  viewer: Viewer;
  applicationNumber: string;
  applicationFormData: Application["loanCategory"];
  token: string;
  setError: React.Dispatch<React.SetStateAction<string>>;
  currentStepName: string;
  isFirstStep: boolean;
  isLastStep: boolean;
  setIsSubmitting: React.Dispatch<React.SetStateAction<boolean>>;
  activeFormTabs: any[];
  backToLastStep: () => Promise<void>;
  isLoading: boolean;
}

const FormBuilderContext = createContext<FormBuilderContextType | undefined>(
  undefined
);

export const FormBuilderProvider = ({
  children,
  viewer,
  applicationNumber,
  application,
  applicationFormData,
  token,
  createAppTrace,
}: {
  children: ReactNode;
  viewer: Viewer;
  applicationNumber: string;
  application: Application;
  applicationFormData: Application["loanCategory"];
  token: string;
  createAppTrace: any;
}) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath as string;
  const baseUrl = basePath ?? null;
  const user = viewer?.me;
  const account = viewer?.account;
  const { customApplicationForm, id: applicationId } = application || {};
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);

  useEffect(() => {
    const fetchClientInfo = async () => {
      try {
        const info = await getCpV2ClientInfo({ basePath: baseUrl });
        setClientInfo(info);
      } catch (error) {
        setClientInfo(null);
      }
    };

    fetchClientInfo();
  }, []);

  const { uploadError, handleUpdateForm } = useUploadHelper({
    user,
    token,
  });

  useEffect(() => {
    if (uploadError) {
      setError(uploadError);
    }
  }, [uploadError]);

  const { error, setError } = useMessageTimer({ duration: 19000 });

  const { path, step } = customApplicationForm || {};

  const [formIndex, setFormIndex] = useState(step || 0);
  const [customPath, setCustomPath] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [applicationForm, setApplicationForm] = useState<any[]>([]);
  const [defaultFormValues, setDefaultFormValues] = useState(
    customApplicationForm
      ? JSON.parse(JSON.stringify(customApplicationForm))
      : null
  );

  const products = applicationFormData?.products;

  useEffect(() => {
    if (customApplicationForm) {
      setCustomPath(path);
      if (step) setFormIndex(step || defaultFormValues?.step);
      setDefaultFormValues(
        customApplicationForm
          ? JSON.parse(JSON.stringify(customApplicationForm))
          : null
      );
    }
  }, [customApplicationForm]);

  useEffect(() => {
    const forms = applicationFormData?.products?.[0]?.applicationForm ?? [];
    setApplicationForm(forms);
  }, [applicationFormData]);

  const activeFormTabs = useMemo(() => {
    return applicationForm?.filter((tab: any) => !tab?.linkedToOption) ?? [];
  }, [applicationForm]);

  const currentStepName = useMemo(() => {
    return activeFormTabs?.[formIndex]?.name ?? "";
  }, [activeFormTabs, formIndex]);

  const isFirstStep = useMemo(() => formIndex === 0, [formIndex]);
  const isLastStep = useMemo(
    () => formIndex >= activeFormTabs.length,
    [formIndex, activeFormTabs.length]
  );

  const shouldSkipStep = useCallback(
    (stepIndex: number) => {
      const stepFormName = activeFormTabs?.[stepIndex]?.builders?.[0]?.title;
      return stepFormName === "Card" || stepFormName === "Bank Info";
    },
    [activeFormTabs]
  );

  const skipToValidStep = useCallback(
    async (targetStepIndex: number): Promise<number> => {
      if (targetStepIndex >= activeFormTabs.length) {
        return targetStepIndex;
      }

      if (shouldSkipStep(targetStepIndex)) {
        try {
          await saveForm({
            formData: {
              data: { step: targetStepIndex + 1, path: "" },
              applicationId,
            },
            token,
          });

          return skipToValidStep(targetStepIndex + 1);
        } catch (error: any) {
          return targetStepIndex;
        }
      }

      return targetStepIndex;
    },
    [activeFormTabs, shouldSkipStep, applicationId, token]
  );

  const handleNextPage = useCallback(async () => {
    if (!isLastStep) {
      const nextStep = await skipToValidStep(formIndex + 1);
      setFormIndex(nextStep);
      setCustomPath("");
      setError("");
    }
  }, [isLastStep, setError, skipToValidStep, formIndex]);

  const handleNext = useCallback(
    async (pageData: any) => {
      try {
        const res = await saveForm({
          formData: { ...pageData },
          token: token as string,
        });
        if (res?.data?.saveCustomApplicationForm?.success) {
          const { path, step } =
            res?.data?.saveCustomApplicationForm?.data || {};

          if (path) {
            setCustomPath(path);
            return;
          }

          if (step < activeFormTabs?.length && !path) {
            setCustomPath("");
            await handleNextPage();
          } else {
            const nextPath = basePath
              ? `/${basePath}/application/${applicationNumber}/add-card`
              : `/application/${applicationNumber}/add-card`;
            router.push(nextPath);
          }
        }

        if (res?.error) {
          setError(res?.error);
        }
      } catch (error: any) {
        setError(error?.message);
      }
    },
    [saveForm, products, applicationNumber, handleNextPage, router]
  );

  const getLatestFormData = useCallback(async () => {
    if (!account?.name || !token) return;

    try {
      const res = await getLatestForm({
        formData: { customerId: account?.name },
        token: token,
      });
      if (res?.latestForm?.data && !defaultFormValues) {
        const fetchedData = JSON.parse(JSON.stringify(res.latestForm.data));
        setDefaultFormValues(fetchedData);

        if (fetchedData?.step && fetchedData?.step !== formIndex) {
          setDefaultFormValues(fetchedData);
        }
      }

      if (res?.latestFormError) {
        setError(res?.latestFormError);
      }
    } catch (error: any) {
      setError(error?.message);
    }
  }, [account?.name, formIndex, token, setError]);

  useEffect(() => {
    getLatestFormData();
  }, [account?.name, getLatestFormData]);

  useEffect(() => {
    const autoSkipIfNeeded = async () => {
      if (shouldSkipStep(formIndex)) {
        const validStep = await skipToValidStep(formIndex);
        if (validStep !== formIndex) {
          setFormIndex(validStep);
          router.refresh();
        }
      }
    };

    autoSkipIfNeeded();
  }, [formIndex, shouldSkipStep, skipToValidStep, router]);

  const handleSubmit = useCallback(
    async ({ data }: { data: FormData }) => {
      setError("");
      setIsSubmitting(true);

      try {
        const res = await handleUpdateForm({ ...data }, currentStepName);

        // Handle error cases
        if (res?.error) {
          setError(res?.error);
          return;
        }

        // Track successful update
        if (createAppTrace) {
          await createAppTrace({
            page: `${currentStepName} Page`,
            comment: `${currentStepName} completed`,
          });
        }

        // Prepare next payload
        const nextPayload = {
          applicationId,
          data: { ...res?.data, step: formIndex + 1, path: "" },
        };

        // Proceed to next step
        await handleNext(nextPayload);
        router.refresh();
      } catch (error: any) {
        setIsSubmitting(false);
        const errorMessage =
          error.message || "An unexpected error occurred during submission.";
        setError(errorMessage);
      }
    },
    [
      handleUpdateForm,
      currentStepName,
      createAppTrace,
      applicationId,
      formIndex,
      handleNext,
    ]
  );

  const backToLastStep = useCallback(async () => {
    if (isFirstStep) return;

    setError("");
    setIsLoading(true);

    const findPreviousValidStep = async (
      currentStep: number
    ): Promise<number> => {
      let previousStep = currentStep - 1;

      while (previousStep >= 0 && shouldSkipStep(previousStep)) {
        previousStep--;
      }

      return Math.max(0, previousStep);
    };

    try {
      const previousStepIndex = await findPreviousValidStep(formIndex);
      const previousStepName = activeFormTabs?.[previousStepIndex]?.name;

      const res = await saveForm({
        formData: {
          data: { step: previousStepIndex, path: "" },
          applicationId,
        },
        token: token,
      });

      if (res?.error) {
        setError(res?.error);
        setIsLoading(false);
        return;
      }

      if (res?.data?.saveCustomApplicationForm?.success) {
        await getLatestFormData();

        if (createAppTrace) {
          await createAppTrace({
            page: `${previousStepName} Page`,
            comment: `Navigated back to ${previousStepName} page`,
          });
        }

        setFormIndex(previousStepIndex);
        setCustomPath("");
        setIsLoading(false);
        router.refresh();
      }
    } catch (err: any) {
      setError(
        err?.message ?? "An unexpected error occurred while navigating back."
      );
      setIsLoading(false);
    }
  }, [
    isFirstStep,
    formIndex,
    setError,
    setIsLoading,
    activeFormTabs,
    saveForm,
    applicationId,
    token,
    createAppTrace,
    setFormIndex,
    shouldSkipStep,
    getLatestFormData,
    router,
  ]);

  const value = useMemo(
    () => ({
      applicationForm,
      setApplicationForm,
      handleNextPage,
      formIndex,
      setFormIndex,
      handleUpdateForm,
      handleNext,
      customPath,
      handleSubmit,
      defaultFormValues,
      clientInfo,
      isSubmitting,
      application,
      viewer,
      applicationNumber,
      applicationFormData,
      token,
      error,
      setError,
      currentStepName,
      isFirstStep,
      isLastStep,
      setIsSubmitting,
      activeFormTabs,
      backToLastStep,
      isLoading,
    }),
    [
      applicationForm,
      formIndex,
      handleUpdateForm,
      handleNext,
      customPath,
      isSubmitting,
      handleSubmit,
      defaultFormValues,
      viewer,
      applicationNumber,
      application,
      clientInfo,
      applicationFormData,
      token,
      error,
      setError,
      currentStepName,
      isFirstStep,
      isLastStep,
      setIsSubmitting,
      activeFormTabs,
      backToLastStep,
      isLoading,
    ]
  );

  return (
    <FormBuilderContext.Provider value={value}>
      {children}
    </FormBuilderContext.Provider>
  );
};

export const useFormBuilder = () => {
  const context = useContext(FormBuilderContext);
  if (!context) {
    throw new Error("useFormBuilder must be used within a FormBuilderProvider");
  }
  return context;
};
