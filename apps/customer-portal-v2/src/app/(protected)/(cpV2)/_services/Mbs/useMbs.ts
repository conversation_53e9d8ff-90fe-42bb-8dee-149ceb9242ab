"use client";

import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import {
  Application,
  CompleteBanksStatementInput,
  CompleteExternalBankStatementRequestInput,
  CreateApplicationTraceInput,
  Viewer,
} from "@/__generated/graphql";
import { format, subDays, subMonths } from "date-fns";
import { useParams, useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";

type Props = {
  viewer: Viewer;
  application: Application;
  applicationNumber: string;
  createAppTrace: ({
    page,
    comment,
    isDebug,
    metadata,
  }: {
    page: CreateApplicationTraceInput["page"];
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
  }) => Promise<any>;
  externalBankStatementRequest?: ({
    password,
    skipStep,
    ticketNum,
  }: {
    password?: CompleteExternalBankStatementRequestInput["password"];
    skipStep?: CompleteExternalBankStatementRequestInput["skipStep"];
    ticketNum?: CompleteExternalBankStatementRequestInput["ticketNum"];
  }) => Promise<
    | {
        error: any;
        data: null;
        success: boolean;
        redirect: boolean;
      }
    | {
        data: any;
        error: null;
        success: boolean;
        redirect: boolean;
      }
    | undefined
  >;

  completeBankStatementRequest?: ({
    password,
    skipStep,
    ticketNum,
  }: {
    password?: CompleteBanksStatementInput["password"];
    skipStep?: CompleteBanksStatementInput["skipStep"];
    ticketNum?: CompleteBanksStatementInput["ticketNum"];
  }) => Promise<
    | {
        error: any;
        data: null;
        success: boolean;
        redirect: boolean;
      }
    | {
        data: any;
        error: null;
        success: boolean;
        redirect: boolean;
      }
    | undefined
  >;

  isExternal: boolean;
};

const useMbs = ({
  application,
  applicationNumber,
  viewer,
  createAppTrace,
  externalBankStatementRequest,
  completeBankStatementRequest,
  isExternal,
}: Props) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath;

  const { error, setError, success, setSuccess } = useMessageTimer({
    duration: 10000,
  });
  const [otpTimer, setOtpTimer] = useState(true);
  const [skipMbsTimer, setSkipMbsTimer] = useState(true);
  const [counter, setCounter] = useState(59);
  const [skipCounter, setSkipCounter] = useState(90);
  const [bankRequestLoading, setBankRequestLoading] = useState(false);
  const [redirectLoading, setRedirectLoading] = useState(false);
  const [redirectExternalLoading, setRedirectExternalLoading] = useState(false);
  const [backLoading, setBackLoading] = useState(false);

  const applicationId = application?.id;
  const bankId = viewer?.account?.bankAccounts?.[0]?.bank?.id;
  const bankName = viewer?.account?.bankAccounts?.[0]?.bank?.name;
  const useCountDown = useCallback(() => counter, [counter]);

  const customMbsDates = useMemo(
    () => ({
      startDate: format(subMonths(subDays(new Date(), 1), 6), "PP"),
      endDate: format(subDays(new Date(), 1), "PP"),
    }),
    []
  );

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>;
    if (counter === 0) {
      setOtpTimer(false);
      timer = setTimeout(() => {
        setOtpTimer(false);
      }, 45000);
    } else {
      timer = setTimeout(() => {
        setCounter(counter - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [counter]);

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>;
    if (skipCounter === 0) {
      setSkipMbsTimer(false);
    } else {
      timer = setTimeout(() => {
        setSkipCounter(skipCounter - 1);
      }, 1000);
    }

    return () => clearTimeout(timer);
  }, [skipCounter]);

  const runCompleteBankStatementRequest = useCallback(
    async ({
      password,
      ticketNum,
      skipStep = false,
    }: {
      password: string;
      ticketNum: string;
      skipStep: boolean;
    }) => {
      try {
        if (!completeBankStatementRequest) {
          return;
        }

        setBankRequestLoading(true);
        const res = await completeBankStatementRequest({
          password,
          skipStep,
          ticketNum,
        });

        if (res?.error) {
          setBankRequestLoading(false);
          const errorMessage = `${res?.error}`;

          createAppTrace({
            page: "MBS Page",
            comment: errorMessage,
            isDebug: true,
            metadata: {
              mbsError: {
                message: res?.error,
              },
            },
          });
          setError(errorMessage);
          return {
            data: null,
            error: res?.error,
            redirect: false,
            success: false,
          };
        }
        return {
          data: res?.data,
          error: null,
          redirect: true,
          success: true,
        };
      } catch (error: any) {
        return {
          data: null,
          error: error?.message,
          redirect: false,
          success: false,
        };
      }
    },
    [applicationId, router, applicationNumber, completeBankStatementRequest]
  );

  const runExternalBankStatementRequest = useCallback(
    async ({
      password,
      ticketNum,
      skipStep = false,
    }: {
      password: string;
      ticketNum: string;
      skipStep: boolean;
    }) => {
      try {
        if (!externalBankStatementRequest) {
          return;
        }

        setBankRequestLoading(true);
        const res = await externalBankStatementRequest({
          password,
          skipStep,
          ticketNum,
        });

        if (res?.error) {
          setBankRequestLoading(false);
          const errorMessage = `${res?.error}`;

          createAppTrace({
            page: "MBS Instruction Page",
            comment: errorMessage,
            isDebug: true,
            metadata: {
              mbsError: {
                message: res?.error,
              },
            },
          });
          setError(errorMessage);
          return {
            data: null,
            error: res?.error,
            redirect: false,
            success: false,
          };
        }
        return {
          data: res?.data,
          error: null,
          redirect: true,
          success: true,
        };
      } catch (error: any) {
        return {
          data: null,
          error: error?.message,
          redirect: false,
          success: false,
        };
      }
    },
    [applicationId, router, applicationNumber, completeBankStatementRequest]
  );

  const handleBankStatementRequest = useCallback(
    async ({
      password,
      ticketNum,
      skipStep,
    }: {
      password: string;
      ticketNum: string;
      skipStep: boolean;
    }) => {
      try {
        if (isExternal) {
          const res = await runExternalBankStatementRequest({
            password,
            ticketNum,
            skipStep,
          });

          if (res?.error) {
            setError(res?.error);
            return {
              data: null,
              error: res?.error,
              redirect: false,
              success: false,
            };
          }

          if (res?.success) {
            return {
              data: res?.data,
              error: null,
              redirect: true,
              success: true,
            };
          }
        } else {
          const res = await runCompleteBankStatementRequest({
            password,
            ticketNum,
            skipStep,
          });

          if (res?.error) {
            setError(res?.error);
            return {
              data: null,
              error: res?.error,
              redirect: false,
              success: false,
            };
          }

          if (res?.success) {
            return {
              data: res?.data,
              error: null,
              redirect: true,
              success: true,
            };
          }
        }
      } catch (error: any) {
        setError(error?.message);
        return {
          data: null,
          error: error?.message,
          redirect: false,
          success: false,
        };
      }
    },
    [runCompleteBankStatementRequest, runExternalBankStatementRequest]
  );
  const getPath = useCallback(
    ({ path }: { path: string }) => {
      return basePath
        ? `/${basePath}/application/${applicationNumber}/${path}`
        : `/application/${applicationNumber}/${path}`;
    },
    [applicationNumber]
  );

  const handleExternalSubmit = useCallback(() => {
    setRedirectExternalLoading(true);
    createAppTrace({
      page: "MBS Instruction Page",
      comment: "Navigated to MBS OTP Page",
    });
    document.cookie = "passedExternalMbsInstruction=true";
    const path = getPath({ path: "mbs" });
    router.push(path);
  }, [getPath, router, createAppTrace, applicationId]);

  const backToExternalMbs = useCallback(() => {
    setBackLoading(true);
    createAppTrace({
      page: "MBS Page",
      comment: "Navigated back to MBS Instruction Page",
    });
    document.cookie = "passedExternalMbsInstruction=false";
    const path = getPath({ path: "mbs-instruction" });
    router.push(path);
  }, [getPath, router, createAppTrace, applicationId]);

  const handleTicketSubmit = useCallback(
    async ({ ...data }) => {
      if (!data?.ticketNum || !data?.password) {
        return;
      }

      try {
        const res = await handleBankStatementRequest({
          ticketNum: data?.ticketNum,
          password: data?.password,
          skipStep: false,
        });

        if (res?.error) {
          return {
            data: null,
            error: res?.error,
            redirect: true,
            success: true,
          };
        }

        if (res?.success) {
          return {
            data: res?.data,
            error: null,
            redirect: true,
            success: true,
          };
        }
      } catch (error: any) {
        setError(error?.message);
        return {
          data: null,
          error: error?.message,
          redirect: true,
          success: true,
        };
      }
    },
    [handleBankStatementRequest, getPath, applicationId, router]
  );
  const handleSkip = useCallback(async () => {
    setRedirectLoading(true);
    const res = await handleBankStatementRequest({
      password: "",
      ticketNum: "",
      skipStep: true,
    });

    if (res?.data) {
      const path = getPath({ path: "upload-statement" });
      router.push(path);
    }
  }, [handleBankStatementRequest, getPath, applicationId, router]);

  return useMemo(
    () => ({
      otpTimer,
      skipMbsTimer,
      setSkipMbsTimer,
      useCountDown,
      customMbsDates,
      handleTicketSubmit,
      bankName,
      handleExternalSubmit,
      handleSkip,
      backLoading,
      bankRequestLoading,
      redirectLoading,
      error,
      setError,
      success,
      redirectExternalLoading,
      setSuccess,
      getPath,
      backToExternalMbs,
    }),
    [
      otpTimer,
      skipMbsTimer,
      setSkipMbsTimer,
      useCountDown,
      customMbsDates,
      handleTicketSubmit,
      bankName,
      handleExternalSubmit,
      handleSkip,
      backLoading,
      bankRequestLoading,
      redirectLoading,
      error,
      setError,
      success,
      redirectExternalLoading,
      setSuccess,
      getPath,
      backToExternalMbs,
    ]
  );
};

export default useMbs;
