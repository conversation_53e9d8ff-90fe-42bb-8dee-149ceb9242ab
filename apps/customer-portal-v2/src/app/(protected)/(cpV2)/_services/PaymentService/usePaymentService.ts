"use client";

import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import {
  Application,
  ApplicationBankStageInput,
  BankAccountType,
  ClientInfo,
  CreateAccountBankInput,
  CreateApplicationTraceInput,
  ViewerQueryQuery,
} from "@/__generated/graphql";
import { useCallback, useState, useMemo } from "react";
import { getAttributrFromProduct } from "@/utils/index";
import { AddBankFormProps } from "@/src/types/addBankForm";
import { useRouter } from "next/navigation";

type Props = {
  clientInfo: ClientInfo | null;
  viewer: ViewerQueryQuery["viewer"];
  application: Application;
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;

  addBank: ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  setBank: ({
    applicationId,
    accountBankId,
  }: {
    applicationId: ApplicationBankStageInput["applicationId"];
    accountBankId: ApplicationBankStageInput["accountBankId"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  getResolvedAccountNumber: ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
};

const usePaymentService = ({
  application,
  clientInfo,
  viewer,
  createAppTrace,
  setBank,
  addBank,
  getResolvedAccountNumber,
}: Props) => {
  const router = useRouter();
  const { setError, setSuccess, error, success } = useMessageTimer();
  const { bankAccounts: bankInfo } = application?.account || {};

  const defaultBankAccount = bankInfo?.find(
    (account) => account?.isDefault === true
  );
  const [selectedBankAccountId, setSelectedBankAccountId] = useState(
    defaultBankAccount?.id || ""
  );
  const [bankAccounts, setBankAccounts] = useState(bankInfo);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddNewBankAccountForm, setShowAddNewBankAccountForm] = useState(
    bankAccounts ? bankAccounts?.length > 0 : false
  );

  const repaymentServices = clientInfo?.repaymentServices;
  const { id: applicationId } = application || {};
  const amount = clientInfo?.addCardCharge || 100;

  const loanCategoryAttributes =
    application?.loanCategory?.loanCategoryAttributes!;

  const namesSet = new Set([
    "requiresBankStatementUpload",
    "requiresBankStatement",
  ]);

  const customerAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const collectionMethod = useMemo(
    () =>
      repaymentServices?.filter((service) => service?.name !== "CARD") || [],
    [repaymentServices]
  );

  const resolveAccountNumber = useCallback(
    async (
      data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountType">
    ) => {
      try {
        const { bankId, accountNumber, accountType } = data;

        if (!bankId || !accountNumber || !accountType) {
          return;
        }
        setError("");
        setIsLoading(true);
        setSuccess("");
        const res = await getResolvedAccountNumber({
          bankId: bankId,
          accountNumber: accountNumber,
          accountType: accountType || BankAccountType.Personal,
        });

        if (res?.error) {
          setIsLoading(false);
          setError(res?.error || "Unable to resolve account number");
          createAppTrace({
            page: "Bank details Page",
            comment: res?.error || "Unable to resolve account number",
            isDebug: true,
            metadata: {
              errorAddingBankAccount: {
                message: res?.error,
              },
            },
          });
        }

        if (res?.data) {
          setIsLoading(false);
          createAppTrace({
            page: "Bank details Page",
            comment: "Resolved account number successfully",
            isDebug: true,
            metadata: {
              errorAddingBankAccount: {
                message: res?.error,
              },
            },
          });
          return res?.data;
        }
      } catch (error: any) {
        setIsLoading(false);
        setError(error?.message);
      }
    },
    [
      getResolvedAccountNumber,
      createAppTrace,
      setError,
      setIsLoading,
      setSuccess,
    ]
  );
  const handleSetApplicationBank = useCallback(
    async ({ bankId }: { bankId: string }) => {
      try {
        setError("");
        setIsLoading(true);
        setSuccess("");
        if (!applicationId || !bankId) return;

        const res = await setBank({
          applicationId,
          accountBankId: bankId,
        });

        if (res?.data?.setApplicationBankAccount?.success) {
          setIsLoading(false);
          setSuccess("Bank account linked successfully");
          createAppTrace({
            page: "Bank Details Page",
            comment: "Bank account linked successfully",
            isDebug: true,
            metadata: {
              setApplicationBankSuccess: {
                message: "Bank account linked successfully",
              },
            },
          });
          router.refresh();
        }

        if (res?.error) {
          const errorMessage =
            res.error || "Error linking bank account to application";
          setIsLoading(false);
          setError(errorMessage);
          createAppTrace({
            page: "Bank Details Page",
            comment: errorMessage,
            isDebug: true,
            metadata: { setApplicationBankError: { message: errorMessage } },
          });
        }
      } catch (error: any) {
        setIsLoading(false);
        setError(error?.message || "An unexpected error occurred");
      }
    },
    [
      applicationId,
      selectedBankAccountId,
      createAppTrace,
      setBank,
      setError,
      setIsLoading,
      setSuccess,
      customerAttributes,
    ]
  );

  const handleAddBankDetails = useCallback(
    async (
      data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountName">
    ) => {
      try {
        setError("");
        setIsLoading(true);
        setSuccess("");
        setSelectedBankAccountId("");
        const { bankId, accountName, accountNumber } = data || {};

        if (!bankId || !accountNumber || !accountName) {
          return;
        }

        const res = await addBank({
          bankId,
          accountNumber,
          accountName,
        });

        if (res?.error) {
          setIsLoading(false);
          setError(res?.error || "Error adding your bank account");

          createAppTrace({
            page: "Bank details Page",
            comment: res?.error || "Error adding your bank account",
            isDebug: true,
            metadata: {
              errorAddingBankAccount: {
                message: res?.error,
              },
            },
          });

          return { error: res?.error, data: null };
        }
        if (res?.data) {
          setIsLoading(false);
          setSelectedBankAccountId(res?.data?.addAccountBank?.id);
          setSuccess("Bank account added successfully");
          setBankAccounts((prevAccounts) => {
            if (!prevAccounts) return [res?.data?.addAccountBank];
            return [res?.data?.addAccountBank, ...prevAccounts];
          });

          createAppTrace({
            page: "Bank details Page",
            comment: "Bank account added successfully",
          });

          const setBankResult = await handleSetApplicationBank({
            bankId: res?.data?.addAccountBank?.id,
          });
          return { data: res?.data, error: null, setBankResult };
        }
      } catch (error: any) {
        setIsLoading(false);
        setError(error?.message);
        return { error: error?.message, data: null };
      }
    },
    [
      addBank,
      createAppTrace,
      setError,
      setIsLoading,
      setSuccess,
      setSelectedBankAccountId,
      setBankAccounts,
      handleSetApplicationBank,
    ]
  );

  return useMemo(
    () => ({
      collectionMethod,
      setError,
      error,
      amount,
      selectedBankAccountId,
      showAddNewBankAccountForm,
      handleSetApplicationBank,
      handleAddBankDetails,
      resolveAccountNumber,
      bankInfo,
      setSelectedBankAccountId,
      bankAccounts,
      isLoading,
      success,
    }),
    [
      collectionMethod,
      setError,
      error,
      amount,
      selectedBankAccountId,
      showAddNewBankAccountForm,
      handleSetApplicationBank,
      handleAddBankDetails,
      resolveAccountNumber,
      bankInfo,
      setSelectedBankAccountId,
      bankAccounts,
      isLoading,
      success,
    ]
  );
};
export default usePaymentService;
