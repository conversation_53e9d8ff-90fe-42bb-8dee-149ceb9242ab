"use client";

import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { constants } from "@/utils/constants";
import {
  BankAccountType,
  CardReferenceStatusInput,
  ClientInfo,
  CreateAccountBankInput,
  SetDefaultAccountBankInput,
  SetDefaultCardInput,
  ViewerQueryQuery,
} from "@/__generated/graphql";
import { useToast } from "@chakra-ui/react";
import { useCallback, useState, useMemo, useEffect } from "react";
import { usePaystackPayment } from "react-paystack";
import { useRouter } from "next/navigation";
import { AddBankFormProps } from "@/src/types/addBankForm";

type Props = {
  clientInfo: ClientInfo | null;
  viewer: ViewerQueryQuery["viewer"];
  getCardRefStatus: ({
    reference,
  }: {
    reference: CardReferenceStatusInput["reference"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  getAddCardRef: () => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  setDefaultCard: ({
    cardId,
  }: {
    cardId: SetDefaultCardInput["cardId"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;
  setDefaultBank: ({
    accountBankId,
  }: {
    accountBankId: SetDefaultAccountBankInput["accountBankId"];
  }) => Promise<
    | {
        data: any;
        error: null;
      }
    | {
        data: null;
        error: any;
      }
    | undefined
  >;

  addBank: ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  getResolvedAccountNumber: ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
};

const usePaymentService = ({
  clientInfo,
  viewer,
  getCardRefStatus,
  getAddCardRef,
  setDefaultCard,
  setDefaultBank,
  addBank,
  getResolvedAccountNumber,
}: Props) => {
  const toast = useToast();
  const router = useRouter();
  const { setError, error, success } = useMessageTimer();
  const [reference, setReference] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const email = viewer?.me?.email!;
  const amount = clientInfo?.addCardCharge || 100;
  const accountId = viewer?.account?.id;

  const defaultBankAccount = viewer?.account?.bankAccounts?.find(
    (account) => account?.isDefault === true
  );
  const [selectedBankAccountId, setSelectedBankAccountId] = useState(
    defaultBankAccount?.id || ""
  );
  const [bankAccounts, setBankAccounts] = useState(
    viewer?.account?.bankAccounts
  );

  const collectionMethod = useMemo(
    () =>
      clientInfo?.repaymentServices?.filter(
        (service) => service?.name !== "CARD"
      ) || [],
    [clientInfo?.repaymentServices]
  );

  const metadata = useMemo(
    () => ({
      cardReference: reference,
      accountId,
      transactionType: constants.transactType.transactionType,
      custom_fields: [],
    }),
    [accountId, reference]
  );

  const getCardRefData = useCallback(async () => {
    setError("");
    try {
      setIsSubmitting(true);
      const res = await getAddCardRef();

      if (res?.error) {
        setError(
          res?.error || "There was an error generating paystack reference"
        );
        return;
      }
      setReference(res?.data?.reference);
    } catch (error: any) {
      setError(error?.message);
    } finally {
      setIsSubmitting(false);
    }
  }, [getAddCardRef, setError]);

  const generateAddCardRef = useCallback(
    () => getCardRefData(),
    [getCardRefData]
  );

  useEffect(() => {
    if (clientInfo?.clientId && !reference) {
      generateAddCardRef();
    }
  }, [clientInfo?.clientId, reference, generateAddCardRef]);

  const config = useMemo(
    () => ({
      reference,
      email,
      amount: amount * 100,
      publicKey: clientInfo?.paystackPubKey!,
      channels: ["card"],
      metadata,
    }),
    [reference, email, amount, clientInfo?.paystackPubKey, metadata]
  );

  const onSuccess = async () => {
    try {
      setIsSubmitting(true);
      const res = await getCardRefStatus({ reference });
      const { status, reason } = res?.data || {};

      switch (status) {
        case "SUCCESS":
          toast({
            title: "Payment Options Tab",
            description: "Card added successfully",
            status: "success",
            duration: constants.duration.SHORT,
            isClosable: true,
            position: "top-right",
          });

          setIsSubmitting(false);
          router.refresh();
          break;
        case "FAILED":
          toast({
            title: "Payment Options Tab",
            description: `${reason}`,
            status: "error",
            duration: constants.duration.SHORT,
            isClosable: true,
            position: "top-right",
          });

          setIsSubmitting(false);
          break;
        case "NOT_USED":
        case "ABANDONED":
          toast({
            title: "Payment Options Tab",
            description:
              "To add a card, please proceed by clicking the add new card button",
            status: "info",
            duration: constants.duration.SHORT,
            isClosable: true,
            position: "top-right",
          });

          setIsSubmitting(false);
          break;
        case "INVALID":
          toast({
            title: "Payment Options Tab",
            description: `${reason}`,
            status: "info",
            duration: constants.duration.SHORT,
            isClosable: true,
            position: "top-right",
          });

          setIsSubmitting(false);
          break;
        case "NOT_REUSABLE":
          toast({
            title: "Payment Options Tab",
            description: "Your card can not be used. Please, add a new card",
            status: "error",
            duration: constants.duration.SHORT,
            isClosable: true,
            position: "top-right",
          });
          setIsSubmitting(false);
          break;
      }
      setIsSubmitting(false);
    } catch (error: any) {
      setError(error?.message);
      setIsSubmitting(false);
    }
  };
  const onClose = () => {
    toast({
      title: "Payment Options Tab",
      description: "Paysatck Popup closed",
      status: "warning",
      duration: constants.duration.LONG,
      isClosable: true,
      position: "top-right",
    });
    setIsSubmitting(false);
    generateAddCardRef();
  };

  const initializePayment = usePaystackPayment(config);

  const handlePaystackWidgetTrigger = useCallback(() => {
    setIsSubmitting(true);
    initializePayment?.({ onSuccess, onClose });
  }, [initializePayment]);

  const setDefaultCardAction = useCallback(
    async ({ cardId }: { cardId: string }) => {
      setError("");
      try {
        setIsSubmitting(true);
        const res = await setDefaultCard({ cardId });

        if (res?.error) {
          setError(res?.error);

          return;
        }
        setIsSubmitting(false);

        router?.refresh();
        return res;
      } catch (error: any) {
        setIsSubmitting(false);
        setError(error?.message);
      }
    },
    [setDefaultCard]
  );

  const setDefaultBankAction = useCallback(
    async ({ accountBankId }: { accountBankId: string }) => {
      setError("");
      try {
        setIsLoading(true);
        const res = await setDefaultBank({ accountBankId });

        if (res?.error) {
          setError(res?.error);

          return;
        }
        setIsLoading(false);

        router?.refresh();
        return res;
      } catch (error: any) {
        setIsLoading(false);
        setError(error?.message);
      }
    },
    [setDefaultBank]
  );

  const handleAddBankDetails = useCallback(
    async (
      data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountName">
    ) => {
      try {
        setError("");
        setIsLoading(true);
        setSelectedBankAccountId("");
        const { bankId, accountName, accountNumber } = data || {};

        if (!bankId || !accountNumber || !accountName) {
          return;
        }

        const res = await addBank({
          bankId,
          accountNumber,
          accountName,
        });

        if (res?.error) {
          setIsLoading(false);
          setError(res?.error || "Error adding your bank account");
          return { error: res?.error, data: null };
        }
        if (res?.data) {
          setIsLoading(false);
          setSelectedBankAccountId(res?.data?.addAccountBank?.id);
          setBankAccounts((prevAccounts) => {
            if (!prevAccounts) return [res?.data?.addAccountBank];
            return [res?.data?.addAccountBank, ...prevAccounts];
          });

          return { data: res?.data, error: null };
        }
      } catch (error: any) {
        setIsLoading(false);
        setError(error?.message);
        return { error: error?.message, data: null };
      }
    },
    [addBank, setError, setIsLoading, setSelectedBankAccountId, setBankAccounts]
  );

  const resolveAccountNumber = useCallback(
    async (
      data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountType">
    ) => {
      try {
        const { bankId, accountNumber, accountType } = data;

        if (!bankId || !accountNumber || !accountType) {
          return;
        }
        setError("");
        setIsLoading(true);

        const res = await getResolvedAccountNumber({
          bankId: bankId,
          accountNumber: accountNumber,
          accountType: accountType || BankAccountType.Personal,
        });

        if (res?.error) {
          setIsLoading(false);
          setError(res?.error || "Unable to resolve account number");
        }

        if (res?.data) {
          setIsLoading(false);

          return res?.data;
        }
      } catch (error: any) {
        setIsLoading(false);
        setError(error?.message);
      }
    },
    [getResolvedAccountNumber, setError, setIsLoading]
  );

  return useMemo(
    () => ({
      reference,
      generateAddCardRef,
      collectionMethod,
      setError,
      error,
      isSubmitting,
      handlePaystackWidgetTrigger,
      amount,
      success,
      setDefaultCardAction,
      setDefaultBankAction,
      isLoading,
      selectedBankAccountId,
      handleAddBankDetails,
      bankAccounts,
      resolveAccountNumber,
    }),
    [
      reference,
      generateAddCardRef,
      collectionMethod,
      setError,
      error,
      isSubmitting,
      handlePaystackWidgetTrigger,
      amount,
      success,
      setDefaultCardAction,
      setDefaultBankAction,
      isLoading,
      selectedBankAccountId,
      handleAddBankDetails,
      bankAccounts,
      resolveAccountNumber,
    ]
  );
};

export default usePaymentService;
