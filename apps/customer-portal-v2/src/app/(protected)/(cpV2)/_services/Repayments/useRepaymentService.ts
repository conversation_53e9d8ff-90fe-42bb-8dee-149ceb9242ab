"use client";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { constants } from "@/utils/constants";
import {
  Application,
  ClientInfo,
  CreatePaystackCardRepaymentInput,
  CreatePaystackReferenceRepaymentInput,
  GenerateAddCardReferenceInput,
  Portfolio,
  Viewer,
} from "@/__generated/graphql";
import { useToast } from "@chakra-ui/react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { usePaystackPayment } from "react-paystack";

type Props = {
  generateByPayStackRef: ({
    metadata,
  }: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => Promise<
    { data: any; error: null } | { data: null; error: any } | undefined
  >;
  viewer: Viewer;
  makePaystackCardRepayment: ({
    amount,
  }: {
    amount: CreatePaystackCardRepaymentInput["amount"];
  }) => Promise<
    { data: any; error: null } | { data: null; error: any } | undefined
  >;
  makePaystackRefRepayment: ({
    paystackReference,
  }: {
    paystackReference: CreatePaystackReferenceRepaymentInput["paystackReference"];
  }) => Promise<
    { data: any; error: null } | { data: null; error: any } | undefined
  >;
  methods: UseFormReturn<{
    repaymentTypeId: string;
    defaultCard: string;
    repaymentAmount?: number;
  }>;
  clientInfo: ClientInfo | null;
  getByPayStackRef: ({
    metadata,
  }: {
    metadata: GenerateAddCardReferenceInput["metadata"];
  }) => Promise<
    { data: any; error: null } | { data: null; error: any } | undefined
  >;
};

export type RepaymentType = {
  id: "repayFull" | "repayNext" | "repayCustom";
  name: string;
  amount: number;
};

const useRepaymentService = ({
  viewer,
  generateByPayStackRef,
  makePaystackCardRepayment,
  makePaystackRefRepayment,
  getByPayStackRef,
  methods,
  clientInfo,
}: Props) => {
  const toast = useToast();

  const { setError, error } = useMessageTimer({
    duration: 10000,
  });

  const { account, me } = viewer || {};

  const { clientId, paystackPubKey } = clientInfo || {};
  const { id: accountId, portfolios, applications, cards } = account || {};
  const { email } = me || {};

  const application = applications?.nodes?.[0] as Application;

  const loan = portfolios?.nodes?.find(
    (app: any) => app?.status?.name !== "CLOSED"
  ) as Portfolio;
  const portfolioId = loan?.id!;
  const defaultCard = cards?.find((card) => card?.isDefault === true);

  const [state, setState] = useState({
    loading: false,
    paymentType: defaultCard?.id,
    amountPaid: 0,
    repaymentComplete: false,
    reference: "",
    isSubmitting: false,
  });

  const selectedTypeId = methods.watch("repaymentTypeId");
  const selectedCard = methods.watch("defaultCard");
  const repaymentAmount = methods.watch("repaymentAmount");
  const nextInstallment = loan?.repayments?.find(
    (repayment) => repayment?.status?.name === "PENDING"
  );

  const repaymentTypes = useMemo(
    () =>
      [
        {
          id: "repayFull",
          name: "Repay Full Loan",
          amount: loan?.fullAmount - loan?.amountPaid!,
        },
        {
          id: "repayNext",
          name: "Repay Next Installment",
          amount: nextInstallment?.outstandingPayment || 0,
        },
        { id: "repayCustom", name: "Repay a Custom Amount", amount: 0 },
      ] as RepaymentType[],
    [nextInstallment?.outstandingPayment, loan?.fullAmount, loan?.amountPaid]
  );

  useEffect(() => {
    if (cards && cards?.length > 0 && defaultCard) {
      methods.setValue("defaultCard", defaultCard?.id);
    }

    methods.setValue("repaymentTypeId", "repayFull");
  }, [cards, defaultCard, methods]);

  const amount = useMemo(() => {
    if (selectedTypeId === "repayFull")
      return loan?.fullAmount - loan?.amountPaid!;
    if (selectedTypeId === "repayNext")
      return nextInstallment?.outstandingPayment || loan?.fullAmount;
    return repaymentAmount;
  }, [selectedTypeId, loan, nextInstallment, repaymentAmount]);

  const metadata = useMemo(
    () => ({
      cardReference: state.reference,
      accountId,
      portfolioId,
      transactionType: constants.transactType.repaymentTransaction,
      custom_fields: [], // Add required custom_fields property
    }),
    [accountId, state.reference, portfolioId]
  );

  const config = useMemo(
    () => ({
      reference: state.reference,
      email: email!,
      amount: amount! * 100,
      publicKey: paystackPubKey!,
      channels: ["card"],
      metadata,
    }),
    [state.reference, email, amount, paystackPubKey, metadata]
  );

  const makeManualRepayment = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true }));
      setError("");

      const response = await makePaystackCardRepayment({ amount: amount! });
      setState((prev) => ({ ...prev, loading: false }));

      if (response?.error) {
        setError(
          response?.error ||
            "There was an error making your repayment. Please, try again later."
        );
        return { data: null, error: response?.error };
      }

      if (response?.data) {
        const { amount } =
          response?.data?.makePaystackCardRepayment?.transaction || {};
        if (amount) {
          setState((prev) => ({ ...prev, amountPaid: amount }));
        }

        setState((prev) => ({ ...prev, repaymentComplete: true }));
        return { data: response?.data, error: null };
      }
    } catch (error: any) {
      setState((prev) => ({ ...prev, loading: false }));
      setError(error?.message || "An unexpected error occurred");
      return { data: null, error: error?.message };
    }
  };

  const generatePayStackRef = useCallback(async () => {
    try {
      setError("");
      setState((prev) => ({ ...prev, isSubmitting: true }));
      const res = await generateByPayStackRef({ metadata });

      if (res?.error) {
        setState((prev) => ({ ...prev, isSubmitting: false }));
        setError(
          res?.error || "There was an error generating paystack reference"
        );
        return;
      }

      setState((prev) => ({
        ...prev,
        reference: res?.data?.reference,
        isSubmitting: false,
      }));
    } catch (error: any) {
      setError(error?.message);
      setState((prev) => ({ ...prev, isSubmitting: false }));
    }
  }, [metadata, generateByPayStackRef]);

  const getPayStackRefData = useCallback(async () => {
    try {
      setError("");
      setState((prev) => ({ ...prev, isSubmitting: true }));
      const res = await getByPayStackRef({ metadata });

      if (res?.error) {
        setState((prev) => ({ ...prev, isSubmitting: false }));
        setError(
          res?.error || "There was an error generating paystack reference"
        );
        return { data: null, error: res?.error };
      }

      setState((prev) => ({
        ...prev,
        reference: res?.data?.reference,
        isSubmitting: false,
      }));
      return { data: res?.data, error: null };
    } catch (error: any) {
      setError(error?.message);
      setState((prev) => ({ ...prev, isSubmitting: false }));
      return { data: null, error: error?.message };
    }
  }, [metadata, getByPayStackRef]);

  const makePaystackReferenceRepayment = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true }));
      setError("");

      const response = await makePaystackRefRepayment({
        paystackReference: state?.reference,
      });

      setState((prev) => ({ ...prev, loading: false }));

      if (response?.error) {
        setError(response?.error);
        return { data: null, error: response?.error };
      }

      const { makePaystackReferenceRepayment } = response?.data;
      if (makePaystackReferenceRepayment?.success) {
        if (makePaystackReferenceRepayment?.transaction?.amount) {
          setState((prev) => ({
            ...prev,
            amountPaid: makePaystackReferenceRepayment?.transaction?.amount,
          }));
        }

        toast({
          title: "Repay loan",
          description: "Transaction successful",
          status: "success",
          duration: constants.duration.LONG,
          isClosable: true,
          position: "top-right",
        });
        setState((prev) => ({ ...prev, repaymentComplete: true }));
        return { data: makePaystackReferenceRepayment, error: null };
      }
    } catch (error: any) {
      setState((prev) => ({ ...prev, loading: false }));
      setError(error?.message || "An unexpected error occurred");
      return { data: null, error: error?.message };
    }
  };

  const onSuccess = async ({ status }: { status: string }) => {
    if (status === "success") {
      try {
        const res = await makePaystackReferenceRepayment();
        setState((prev) => ({ ...prev, isSubmitting: true }));

        if (res?.data) {
          setState((prev) => ({ ...prev, repaymentComplete: true }));
        } else if (res?.error) {
          setState((prev) => ({ ...prev, isSubmitting: false }));
          setError(res.error || "Failed to verify card reference");
        }
      } catch (error: any) {
        setState((prev) => ({ ...prev, isSubmitting: false }));
        setError(error?.message || "An unexpected error occurred");
      }
    } else {
      setState((prev) => ({ ...prev, isSubmitting: false }));
      generatePayStackRef();
    }
  };

  const onClose = () => {
    toast({
      title: "Repay loan",
      description: "Paysatck Popup closed",
      status: "warning",
      duration: constants.duration.LONG,
      isClosable: true,
      position: "top-right",
    });

    setState((prev) => ({ ...prev, isSubmitting: false }));
    generatePayStackRef();
  };

  const initializePayment = usePaystackPayment(config);

  const handlePaystackWidgetTrigger = useCallback(() => {
    try {
      setState((prev) => ({ ...prev, isSubmitting: true }));
      if (initializePayment) {
        initializePayment({ onSuccess, onClose });
      }
      setState((prev) => ({ ...prev, isSubmitting: false }));
    } catch (error: any) {
      setState((prev) => ({ ...prev, isSubmitting: false }));
      setError(error?.message || "An unexpected error occurred");
    }
  }, [initializePayment]);

  useEffect(() => {
    if (clientId && !state.reference) {
      generatePayStackRef();
    }
  }, [clientId, state.reference, generatePayStackRef]);

  useEffect(() => {
    setState((prev) => ({ ...prev, paymentType: selectedCard }));
  }, [selectedCard]);

  return {
    cards,
    repaymentTypes,
    loading: state.loading,
    amountPaid: state.amountPaid,
    repaymentComplete: state.repaymentComplete,
    selectedCard,
    setPaymentType: (type: string) =>
      setState((prev) => ({ ...prev, paymentType: type })),
    paymentType: state.paymentType,
    handlePaystackWidgetTrigger,
    application,
    defaultCard,
    isSubmitting: state.isSubmitting,
    error,
    getPayStackRefData,
    reference: state.reference,
    loan,
    makeManualRepayment,
  };
};
export default useRepaymentService;
