"use client";

import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { AddBankFormProps } from "@/src/types/addBankForm";
import { APPLICATION_STEPS } from "@/utils/constants";
import { getAttributrFromProduct } from "@/utils/index";
import {
  Application,
  ApplicationBankStageInput,
  BankAccountType,
  CreateAccountBankInput,
  CreateApplicationTraceInput,
  Viewer,
} from "@/__generated/graphql";
import { useParams, useRouter } from "next/navigation";
import { useCallback, useMemo, useState } from "react";

type Props = {
  viewer: Viewer;
  application: Application;
  applicationNumber: string;
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  addBank: ({
    bankId,
    accountName,
    accountNumber,
  }: {
    bankId: CreateAccountBankInput["bankId"];
    accountName: CreateAccountBankInput["accountName"];
    accountNumber: CreateAccountBankInput["accountNumber"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  setBank: ({
    applicationId,
    accountBankId,
  }: {
    applicationId: ApplicationBankStageInput["applicationId"];
    accountBankId: ApplicationBankStageInput["accountBankId"];
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
  getResolvedAccountNumber: ({
    bankId,
    accountNumber,
    accountType,
  }: {
    bankId: string;
    accountNumber: string;
    accountType: BankAccountType;
  }) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
    | undefined
  >;
};

const useSelectBank = ({
  application,
  applicationNumber,
  viewer,
  createAppTrace,
  addBank,
  setBank,
  getResolvedAccountNumber,
}: Props) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath;

  const { id: applicationId } = application || {};

  const { bankAccounts: bankInfo } = viewer?.account || {};
  const defaultBankAccount = bankInfo?.find(
    (account) => account?.isDefault === true
  );

  const [selectedBankAccountId, setSelectedBankAccountId] = useState(
    defaultBankAccount?.id || ""
  );
  const [bankAccounts, setBankAccounts] = useState(bankInfo);

  const [isLoading, setIsLoading] = useState(false);

  const { setError, setSuccess, error, success } = useMessageTimer();
  const [showAddNewBankAccountForm, setShowAddNewBankAccountForm] = useState(
    bankAccounts ? bankAccounts?.length > 0 : false
  );

  const loanCategoryAttributes =
    application?.loanCategory?.loanCategoryAttributes!;

  const namesSet = new Set([
    "requiresBankStatementUpload",
    "requiresBankStatement",
  ]);

  const customerAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const resolveAccountNumber = useCallback(
    async (
      data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountType">
    ) => {
      try {
        const { bankId, accountNumber, accountType } = data;

        if (!bankId || !accountNumber || !accountType) {
          return;
        }
        setError("");
        setIsLoading(true);
        setSuccess("");
        const res = await getResolvedAccountNumber({
          bankId: bankId,
          accountNumber: accountNumber,
          accountType: accountType || BankAccountType.Personal,
        });

        if (res?.error) {
          setIsLoading(false);
          setError(res?.error || "Unable to resolve account number");
          createAppTrace({
            page: "Bank details Page",
            comment: res?.error || "Unable to resolve account number",
            isDebug: true,
            metadata: {
              errorAddingBankAccount: {
                message: res?.error,
              },
            },
          });
        }

        if (res?.data) {
          setIsLoading(false);
          createAppTrace({
            page: "Bank details Page",
            comment: "Resolved account number successfully",
          });
          return res?.data;
        }
      } catch (error: any) {
        setIsLoading(false);
        setError(error?.message);
      }
    },
    [
      getResolvedAccountNumber,
      createAppTrace,
      setError,
      setIsLoading,
      setSuccess,
    ]
  );

  const handleAddBankDetails = useCallback(
    async (
      data: Pick<AddBankFormProps, "bankId" | "accountNumber" | "accountName">
    ) => {
      try {
        setError("");
        setIsLoading(true);
        setSuccess("");
        setSelectedBankAccountId("");
        const { bankId, accountName, accountNumber } = data || {};

        if (!bankId || !accountNumber || !accountName) {
          return;
        }

        const res = await addBank({
          bankId,
          accountNumber,
          accountName,
        });

        if (res?.error) {
          setIsLoading(false);
          setError(res?.error || "Error adding your bank account");

          createAppTrace({
            page: "Bank details Page",
            comment: res?.error || "Error adding your bank account",
            isDebug: true,
            metadata: {
              errorAddingBankAccount: {
                message: res?.error,
              },
            },
          });
          return { error: res?.error, data: null };
        }

        if (res?.data) {
          setIsLoading(false);
          // Automatically select the newly added bank
          setSelectedBankAccountId(res?.data?.addAccountBank?.id);
          setSuccess("Bank account added successfully");
          setBankAccounts((prevAccounts) => {
            if (!prevAccounts) return [res?.data?.addAccountBank];
            return [res?.data?.addAccountBank, ...prevAccounts];
          });

          createAppTrace({
            page: "Bank details Page",
            comment: "Bank account added successfully",
          });

          return { data: res?.data, error: null };
        }
      } catch (error: any) {
        setIsLoading(false);
        setError(error?.message);
        return { error: error?.message, data: null };
      }
    },
    [
      addBank,
      createAppTrace,
      setError,
      setIsLoading,
      setSuccess,
      setSelectedBankAccountId,
      setBankAccounts,
    ]
  );

  const handleSetApplicationBank = useCallback(async () => {
    try {
      setError("");
      setIsLoading(true);
      setSuccess("");
      if (!applicationId || !selectedBankAccountId) return;

      const res = await setBank({
        applicationId,
        accountBankId: selectedBankAccountId,
      });

      if (res?.data?.setApplicationBankAccount?.success) {
        createAppTrace({
          page: "Bank details Page",
          comment: "Bank Account Set Successfully",
        });
        const { requiredSteps } =
          res.data.setApplicationBankAccount.application;

        const baseUrl = basePath
          ? `/${basePath}/application/${applicationNumber}`
          : `/application/${applicationNumber}`;
        const { requiresBankStatementUpload } = customerAttributes || {};

        if (
          requiredSteps?.includes(
            APPLICATION_STEPS.completeExternalBankStatementRequest.name
          )
        ) {
          router.push(`${baseUrl}/mbs-instruction`);
        } else if (
          requiredSteps?.includes(
            APPLICATION_STEPS.initiateBankStatementRequest.name
          )
        ) {
          router.push(`${baseUrl}/mbs`);
        } else if (
          requiresBankStatementUpload ||
          requiredSteps.includes(APPLICATION_STEPS.uploadBankStatement.name)
        ) {
          router.push(`${baseUrl}/upload-statement`);
        } else {
          router.push(`${baseUrl}/breakdown`);
        }
      }

      if (res?.error) {
        const errorMessage =
          res.error || "Error linking bank account to application";
        setIsLoading(false);
        setError(errorMessage);
        createAppTrace({
          page: "Bank Details Page",
          comment: errorMessage,
          isDebug: true,
          metadata: { setApplicationBankError: { message: errorMessage } },
        });
      }
    } catch (error: any) {
      setIsLoading(false);
      setError(error?.message || "An unexpected error occurred");
    }
  }, [
    applicationId,
    selectedBankAccountId,
    applicationNumber,
    basePath,
    createAppTrace,
    router,
    setBank,
    setError,
    setIsLoading,
    setSuccess,
    customerAttributes,
  ]);

  const toggleShowAddNewBankAccountForm = useCallback(() => {
    setShowAddNewBankAccountForm((prev) => !prev);
  }, []);

  const handleAccountBankSwitch = useCallback(({ id }: { id: string }) => {
    setSelectedBankAccountId(id);
  }, []);

  const handleContinueButton = useCallback(() => {
    handleSetApplicationBank();
  }, [handleSetApplicationBank]);

  return useMemo(
    () => ({
      selectedBankAccountId,
      showAddNewBankAccountForm,
      toggleShowAddNewBankAccountForm,
      handleAccountBankSwitch,
      handleSetApplicationBank,
      handleAddBankDetails,
      handleContinueButton,
      resolveAccountNumber,
      bankInfo,
      setSelectedBankAccountId,
      bankAccounts,
      isLoading,
      setError,
      error,
      setSuccess,
      success,
    }),
    [
      selectedBankAccountId,
      showAddNewBankAccountForm,
      toggleShowAddNewBankAccountForm,
      handleAccountBankSwitch,
      handleSetApplicationBank,
      handleAddBankDetails,
      handleContinueButton,
      resolveAccountNumber,
      bankInfo,
      setSelectedBankAccountId,
      isLoading,
      bankAccounts,
      setError,
      error,
      setSuccess,
      success,
    ]
  );
};
export default useSelectBank;
