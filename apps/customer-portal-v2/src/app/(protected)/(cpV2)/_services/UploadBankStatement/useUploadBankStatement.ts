"use client";

import { CreateApplicationTraceInput } from "@/__generated/graphql";
import { useCallback, useMemo, useState } from "react";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { useParams, useRouter } from "next/navigation";

// Constants
const POLLING_INTERVAL = 15000; // 15 seconds
const MAX_POLLING_DURATION = 90000; // 90 seconds

const JOB_STATUS = {
  IN_PROGRESS: "IN_PROGRESS",
  DONE: "DONE",
  FAILED: "FAILED",
  SKIPPED: "SKIPPED",
};

type Props = {
  applicationNumber: string;
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  getDecideJobStatus: () => Promise<
    | {
        data: null;
        error: any;
      }
    | {
        data: any;
        error: null;
      }
    | undefined
  >;
  uploadAction: (formData: FormData) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
  >;
};

const useUploadBankService = ({
  applicationNumber,
  getDecideJobStatus,
  createAppTrace,
  uploadAction,
}: Props) => {
  const router = useRouter();
  const params = useParams();
  const basePath = params?.basePath;

  const [isPolling, setIsPolling] = useState(false);
  const { error, setError } = useMessageTimer({ duration: 10000 });

  const getPath = (path: string) =>
    basePath
      ? `/${basePath}/application/${applicationNumber}/${path}`
      : `/application/${applicationNumber}/${path}`;

  const uploadBankStatementFn = useCallback(
    async ({
      bankStatement,
      bankId,
      password,
    }: {
      bankStatement: any;
      bankId: string;
      password: string;
    }) => {
      try {
        setError("");

        if (!bankStatement) {
          return;
        }

        const { validity, file } = bankStatement;

        if (!validity) {
          return;
        }

        const formData = new FormData();
        formData.append("bankId", bankId);
        formData.append("file", file);
        if (password) {
          formData.append("password", password);
        }

        const res = await uploadAction(formData);

        if (res?.error) {
          return {
            error: res?.error,
            data: null,
          };
        }

        if (res?.data) {
          startPolling();
          setIsPolling(true);
          createAppTrace({
            comment: `Bank statement uploaded successfully`,
            page: "Upload Bank Statement Page",
          });
          return { data: res?.data, error: null };
        }
      } catch (error: any) {
        setIsPolling(false);
        return {
          data: null,
          error: error?.message || "Upload failed. Please try again.",
        };
      }
    },
    []
  );

  const startPolling = async () => {
    setIsPolling(true);
    setError("");
    let elapsed = 0;
    const interval = setInterval(async () => {
      elapsed += POLLING_INTERVAL;

      try {
        const result = await getDecideJobStatus();
        if (
          result?.data?.status === JOB_STATUS.DONE ||
          result?.data?.status === JOB_STATUS.SKIPPED
        ) {
          clearInterval(interval);
          createAppTrace({
            comment: `Decide job completed successfully`,
            page: "Upload Bank Statement Page",
          });

          router.push(getPath("breakdown"));
        } else if (result?.data?.status === JOB_STATUS.FAILED) {
          clearInterval(interval);
          setIsPolling(false);
          setError(
            result?.data?.message || "Processing failed. Please re-upload"
          );
          createAppTrace({
            comment: `Decide job failed: ${result?.data?.message || "Processing failed. Please re-upload"}`,
            page: "Upload Bank Statement Page",
          });
        }
        if (
          elapsed >= MAX_POLLING_DURATION &&
          result?.data?.status === JOB_STATUS.IN_PROGRESS
        ) {
          clearInterval(interval);
          createAppTrace({
            comment: `Decide job timed out`,
            page: "Upload Bank Statement Page",
          });
          router.push(getPath("breakdown"));
        }
      } catch (error: any) {
        clearInterval(interval);
        setIsPolling(false);
        setError("Error checking processing status");
        createAppTrace({
          comment: `Decide job failed: ${error?.message}`,
          page: "Upload Bank Statement Page",
        });
      }
    }, POLLING_INTERVAL);
  };
  return useMemo(
    () => ({
      uploadBankStatementFn,
      error,
      setError,
      isPolling,
    }),
    [uploadBankStatementFn, error, setError, isPolling]
  );
};
export default useUploadBankService;
