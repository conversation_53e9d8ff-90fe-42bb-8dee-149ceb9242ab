"use client";

import { CreateApplicationTraceInput } from "@/__generated/graphql";
import { useCallback, useMemo } from "react";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { useRouter } from "next/navigation";

type Props = {
  createAppTrace: ({
    comment,
    isDebug,
    metadata,
    page,
  }: {
    comment: CreateApplicationTraceInput["comment"];
    isDebug?: CreateApplicationTraceInput["isDebug"];
    metadata?: CreateApplicationTraceInput["metadata"];
    page?: CreateApplicationTraceInput["page"];
  }) => Promise<any>;
  uploadAction: (formData: FormData) => Promise<
    | {
        error: any;
        data: null;
      }
    | {
        error: null;
        data: any;
      }
  >;
};

const useUploadBankStatement = ({ createAppTrace, uploadAction }: Props) => {
  const router = useRouter();
  const { error, setError, setSuccess, success } = useMessageTimer({
    duration: 10000,
  });

  const uploadBankStatementFn = useCallback(
    async ({
      bankStatement,
      bankId,
      password,
    }: {
      bankStatement: any;
      bankId: string;
      password: string;
    }) => {
      try {
        setError("");

        if (!bankStatement) {
          return;
        }

        const { validity, file } = bankStatement;

        if (!validity) {
          return;
        }

        const formData = new FormData();
        formData.append("bankId", bankId);
        formData.append("file", file);
        if (password) {
          formData.append("password", password);
        }

        const res = await uploadAction(formData);

        if (res?.error) {
          return {
            error: res?.error,
            data: null,
          };
        }

        if (res?.data) {
          createAppTrace({
            comment: `Bank statement uploaded successfully`,
            page: "Upload Bank Statement Page",
          });
          setSuccess("Bank statement uploaded successfully");
          return { data: res?.data, error: null };
        }
        router.refresh();
      } catch (error: any) {
        return {
          data: null,
          error: error?.message || "Upload failed. Please try again.",
        };
      }
    },
    []
  );

  return useMemo(
    () => ({
      uploadBankStatementFn,
      error,
      setError,
      success,
    }),
    [uploadBankStatementFn, error, setError, success]
  );
};
export default useUploadBankStatement;
