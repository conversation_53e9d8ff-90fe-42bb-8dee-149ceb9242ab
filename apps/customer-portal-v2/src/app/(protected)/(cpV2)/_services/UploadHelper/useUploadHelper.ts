"use client";
import { useCallback, useMemo, useState } from "react";
import {
  SupportingDocumentInput,
  UploadImageAndSaveToUserMetaDataInput,
  UploadImageTypes,
  ViewerQueryQuery,
} from "@/__generated/graphql";
import { upload } from "@/src/app/actions";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";

type SelectedFile = {
  validity: boolean;
  file: File;
};

type FormData = {
  [key: string]: any;
};

type HandleUploadsParams = {
  name: string;
  fileDetails: SelectedFile;
  documentName?: string;
  documentNumber?: string;
};

const FILE_TYPES = {
  WORK_ID: "WORKID",
  SELFIE: "PASSPORT",
  GOVERNMENT_ID: "GOVERNMENTID",
  DOCUMENT: "DOCUMENT",
};

const UPLOAD_IMAGE = `
   mutation UploadImageAndSaveToUserMetaData(
     $input: UploadImageAndSaveToUserMetaDataInput!
   ) {
     uploadImageAndSaveToUserMetaData(input: $input) {
       status
       fileUrl
       key
       bucket
     }
   }
 `;

const UPLOAD_SUPPORTING_DOCUMENT = `
  mutation UploadSupportingDocument($input: SupportingDocumentInput!) {
    uploadSupportingDocument(input: $input) {
      documentName
      fileUrl
      id
      file {
        url
        key
        bucket
      }
      user {
        id
      }
    }
  }
`;

const useUploadHelper = ({
  user,
  token,
}: {
  user: ViewerQueryQuery["viewer"]["me"];
  token?: string;
}) => {
  const { error, setError } = useMessageTimer();
  const [customerLoading, setCustomerLoading] = useState(false);

  const uploadImage = useCallback(
    async ({
      fileName,
      image,
      type,
    }: UploadImageAndSaveToUserMetaDataInput) => {
      const formData = new FormData();
      formData.append(
        "operations",
        JSON.stringify({
          query: UPLOAD_IMAGE,
          variables: { input: { fileName, type, image } },
        })
      );
      formData.append(
        "map",
        JSON.stringify({ "1": ["variables.input.image"] })
      );
      formData.append("1", new Blob([image], { type: image?.type }), fileName);

      setError("");
      try {
        const res = await upload({ data: formData, token: token! });

        if (res?.errors) {
          throw new Error(res?.errors?.[0]?.message || "Error uploading image");
        }
        return res.data;
      } catch (error: any) {
        throw new Error(error?.message || "Error uploading image");
      }
    },
    [token]
  );

  const uploadSupportingDoc = useCallback(
    async ({ documentName, file, userId }: SupportingDocumentInput) => {
      const formData = new FormData();

      formData.append(
        "operations",
        JSON.stringify({
          query: UPLOAD_SUPPORTING_DOCUMENT,
          variables: {
            input: {
              documentName,
              file,
              userId,
            },
          },
        })
      );

      formData.append(
        "map",
        JSON.stringify({
          "1": ["variables.input.file"],
        })
      );

      formData.append(
        "1",
        new Blob([file], { type: file?.type }),
        documentName
      );
      setError("");
      try {
        const res = await upload({ data: formData, token: token! });

        if (res?.errors) {
          throw new Error(res?.errors?.[0]?.message || "Error uploading file");
        }

        return res?.data;
      } catch (error: any) {
        throw new Error(error?.message || "Error uploading file");
      }
    },
    [token]
  );

  const uploadFile = useCallback(
    async ({
      fileDetails,
      options,
    }: {
      fileDetails: SelectedFile;
      options: {
        type: UploadImageTypes;
        name: string;
        documentName?: string;
        documentNumber?: string;
      };
    }) => {
      const { type, name, documentName, documentNumber } = options;
      const { file, validity } = fileDetails;

      if (!validity) {
        throw new Error("Invalid file");
      }
      const { id } = user || {};
      let fileName;
      let variables;

      const firstName = user?.firstName || "";
      const lastName = user?.lastName || "";

      const isImage = file?.type?.startsWith("image/");

      switch (type) {
        case FILE_TYPES.WORK_ID:
          fileName = `work_id_${firstName}_${lastName}_${id}`;
          variables = { image: file, fileName, type: FILE_TYPES.WORK_ID };
          break;
        case FILE_TYPES.SELFIE:
          fileName =
            name === "selfie"
              ? `selfie_${firstName}_${lastName}_${id}`
              : `image_${firstName}_${lastName}_${id}`;
          variables = { image: file, fileName, type: FILE_TYPES.SELFIE };
          break;
        case FILE_TYPES.GOVERNMENT_ID:
          fileName = `${documentName}_${documentNumber}_${firstName}_${lastName}_${id}`;
          variables = { image: file, fileName, type: FILE_TYPES.GOVERNMENT_ID };
          break;
        case FILE_TYPES.DOCUMENT:
          fileName = `${name}_${firstName}_${lastName}_${id}`;
          // If it's an image file but in a document field, use the image mutation
          if (isImage) {
            variables = { image: file, fileName, type: FILE_TYPES.SELFIE };
          } else {
            variables = { file, documentName: fileName, userId: id };
          }
          break;
        default:
          fileName = `${name}_${firstName}_${lastName}_${id}`;
          variables = { file, documentName: fileName, userId: id };
          break;
      }
      try {
        // Use appropriate mutation based on file type and mime type
        if (
          [
            FILE_TYPES.WORK_ID,
            FILE_TYPES.SELFIE,
            FILE_TYPES.GOVERNMENT_ID,
          ].includes(type) ||
          (type === FILE_TYPES.DOCUMENT && isImage)
        ) {
          const res = await uploadImage({
            fileName: variables?.fileName?.replace(/\s+/g, "")!,
            image: variables?.image,
            type: variables?.type as UploadImageTypes,
          });

          const { fileUrl, key, bucket } =
            res?.uploadImageAndSaveToUserMetaData || {};
          return { url: fileUrl, key, bucket };
        } else {
          const res = await uploadSupportingDoc({
            documentName: variables?.documentName?.replace(/\s+/g, "")!,
            file: variables?.file,
            userId: variables?.userId!,
          });
          const { url, key, bucket } =
            res?.uploadSupportingDocument?.file || {};
          return {
            url,
            key,
            bucket,
          };
        }
      } catch (error: any) {
        throw new Error(`Failed to upload ${name}: ${error?.message}`);
      }
    },
    [user, uploadImage, uploadSupportingDoc]
  );

  const processFile = useCallback(
    async (file: HandleUploadsParams) => {
      const { name, fileDetails, documentName, documentNumber } = file;

      let type: UploadImageTypes;
      // Determine file type based on name and mime type
      if (name === "workId") {
        type = FILE_TYPES.WORK_ID as UploadImageTypes.Workid;
      } else if (name === "selfie") {
        type = FILE_TYPES.SELFIE as UploadImageTypes.Passport;
      } else if (name === "governmentId") {
        type = FILE_TYPES.GOVERNMENT_ID as UploadImageTypes.Governmentid;
      } else {
        // For other files, check if it's an image or document
        type = fileDetails?.file?.type?.startsWith("image/")
          ? name === "image"
            ? (FILE_TYPES.SELFIE as UploadImageTypes.Passport)
            : (FILE_TYPES.DOCUMENT as any)
          : (FILE_TYPES.DOCUMENT as any);
      }
      const uploadResult = await uploadFile({
        fileDetails,
        options: {
          type,
          name,
          documentName,
          documentNumber,
        },
      });

      // Validate that we have required fields in the upload result
      if (!uploadResult || !uploadResult?.key || !uploadResult?.url) {
        throw new Error(
          `Upload for ${name} failed or returned incomplete data`
        );
      }

      // Format the result based on file type
      if (name === "governmentId") {
        return {
          name,
          fileDetails: {
            ...uploadResult,
            documentName,
            documentNumber,
          },
        };
      } else {
        return {
          name,
          fileDetails: uploadResult,
        };
      }
    },
    [uploadFile]
  );

  // Extract form data without files
  const extractFormDataWithoutFiles = useCallback(
    ({ formData, formName }: { formData: FormData; formName: string }) => {
      // Check if formData[formName] exists
      if (!formData[formName]) {
        return {}; // Return empty object if it doesn't exist
      }

      const keySet = new Set(
        Array.isArray(formData[formName]?.files)
          ? formData[formName].files.map((item: { name: string }) => item?.name)
          : []
      );

      return Object.keys(formData[formName])
        .filter((key) => !keySet.has(key))
        .reduce((result: Record<string, unknown>, key) => {
          result[key] = formData[formName][key];
          return result;
        }, {});
    },
    []
  );

  const isFileAlreadyUploaded = useCallback((fileDetails: any): boolean => {
    return (
      fileDetails &&
      typeof fileDetails === "object" &&
      typeof fileDetails.key === "string" &&
      typeof fileDetails.url === "string" &&
      typeof fileDetails.bucket === "string" &&
      fileDetails.key.length > 0 &&
      fileDetails.url.length > 0 &&
      fileDetails.bucket.length > 0 &&
      !fileDetails.file &&
      !(fileDetails instanceof File)
    );
  }, []);

  const handleUpdateForm = useCallback(
    async (formData: FormData, name: string) => {
      try {
        let uploadedFiles = [];

        if (!formData?.[name]) {
          formData[name] = {};
        }

        if (
          Array.isArray(formData?.[name]?.files) &&
          formData?.[name]?.files?.length > 0
        ) {
          try {
            const filesToProcess = formData[name].files.map((file: any) => {
              if (isFileAlreadyUploaded(file?.fileDetails)) {
                return Promise.resolve({
                  name: file.name,
                  fileDetails: file.fileDetails,
                });
              } else {
                const parsedFile = {
                  ...file,
                  documentName:
                    file?.documentName || formData?.[name]?.documentName,
                  documentNumber:
                    file?.documentNumber || formData?.[name]?.documentNumber,
                };

                return processFile(parsedFile);
              }
            });

            uploadedFiles = await Promise.all(filesToProcess);

            const hasFailedUploads = uploadedFiles.some(
              (file) =>
                !file?.fileDetails?.key ||
                !file?.fileDetails?.url ||
                Object.keys(file?.fileDetails).length === 0
            );

            if (hasFailedUploads) {
              const errorMsg =
                "One or more file uploads failed. Please try again.";
              setError(errorMsg);
              return { data: null, error: errorMsg };
            }

            const formDataWithoutFiles = extractFormDataWithoutFiles({
              formData,
              formName: name,
            });

            formData[name] = {
              ...formDataWithoutFiles,
              files: uploadedFiles,
            };

            return { data: formData, error: null };
          } catch (uploadError: any) {
            const errorMsg = uploadError?.message;
            setError(errorMsg);
            return { data: null, error: errorMsg };
          }
        } else {
          const formDataWithoutFiles = extractFormDataWithoutFiles({
            formData,
            formName: name,
          });

          formData[name] = {
            ...formDataWithoutFiles,
            files: [],
          };

          return { data: formData, error: null };
        }
      } catch (error: any) {
        const errorMsg = error?.message;
        setError(errorMsg);
        return { data: null, error: errorMsg };
      }
    },
    [processFile, extractFormDataWithoutFiles]
  );

  const uploadError = error;

  return useMemo(
    () => ({
      uploadError,
      handleUpdateForm,
      customerLoading,
      setCustomerLoading,
    }),
    [uploadError, handleUpdateForm, customerLoading, setCustomerLoading]
  );
};

export default useUploadHelper;
