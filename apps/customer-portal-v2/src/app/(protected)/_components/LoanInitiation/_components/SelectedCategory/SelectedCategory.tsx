"use client";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  ButtonGroup,
  Flex,
  Radio,
  RadioGroup,
  Text,
} from "@chakra-ui/react";
import {
  AmountEligibilityPayload,
  ApplicableTenorsResponse,
  ClientInfoQuery,
  CustomerCreateApplicationInput,
  LoanCategory,
  ViewerQueryQuery,
} from "src/__generated__/graphql";
import { formatAmount } from "src/utils";
import { LoanAmount } from "../LoanAmount";
import { LoanTenor } from "../LoanTenor";
import { useLoanInitiation } from "@/src/services";
import { useCallback, useEffect, useState } from "react";

import { FullPageLoader } from "@/components/FullPageLoader";
import { useForm } from "react-hook-form";
import { LoanInitiationForm } from "@/src/types/loanInitiationForm";
import { useRouter } from "next/navigation";
import {
  applicationPagesRedirection,
  initiateApplication,
} from "@/src/app/actions";
import { externalCustomEventTrigger } from "@/lib/externalCustomEvent";
import logger from "@/lib/logger";

type Props = {
  category: LoanCategory;
  loanInitiationData: ({
    amount,
    selectedCategoryId,
  }: {
    selectedCategoryId?: string | undefined;
    amount?: number | undefined;
  }) => Promise<{
    applicableTenor: ApplicableTenorsResponse[];
    eligibleAmount: AmountEligibilityPayload[];
    initiationError: {
      eligibleAmountError: string;
      applicableTenorError: string;
    };
  }>;
  clientData: ClientInfoQuery["clientInfo"];
  user: ViewerQueryQuery["viewer"]["me"];
  isAgent: boolean;
};

const SelectedCategory = ({
  category,
  loanInitiationData,
  isAgent,
  clientData,
}: Props) => {
  const router = useRouter();
  const [error, setError] = useState("");
  const [isTenorValid, setIsTenorValid] = useState(true);
  const {
    selectedCategoryId,
    handleResetSelection,
    amount,
    setEligibleAmount,
    setIsLoanInitiationError,
    setIsLoanInitiationLoading,
    setLoanTenor,
    setInitiationLoading,
    initiationLoading,
    setShowTenor,
    showTenor,
    tenor,
    token,
    setTenor,
    validateCardDuration,
  } = useLoanInitiation();

  const getInitiationData = useCallback(async () => {
    try {
      setIsLoanInitiationLoading(true);
      const { eligibleAmount, applicableTenor, initiationError } =
        await loanInitiationData({
          selectedCategoryId,
          amount,
        });

      setEligibleAmount(eligibleAmount);
      setLoanTenor(applicableTenor);
      setIsLoanInitiationError({
        amountError: initiationError.eligibleAmountError,
        tenorError: initiationError.applicableTenorError,
      });
      setIsLoanInitiationLoading(false);
    } catch (error: any) {
      logger({ error });
      setIsLoanInitiationLoading(false);
    }
  }, [selectedCategoryId, amount]);

  const { clientId } = clientData || {};

  const form = useForm<LoanInitiationForm>({
    mode: "onChange",
  });
  const { reset } = form;

  useEffect(() => {
    if (selectedCategoryId || amount) {
      getInitiationData();
    }
  }, [selectedCategoryId, amount]);

  useEffect(() => {
    if (!showTenor) {
      setTenor("");
    }
  }, [showTenor]);

  useEffect(() => {
    if (tenor) {
      setIsTenorValid(validateCardDuration(tenor) === true);
    }
  }, [tenor]);

  const onSubmit = async () => {
    setShowTenor(true);
    const tenorValue = tenor?.split(" ");

    const durationType = () => {
      switch (tenorValue && tenorValue[1]) {
        case "days":
          return "DAILY";
        case "months":
          return "MONTHLY";
        case "weeks":
          return "WEEKLY";
        case "years":
          return "ANNUALLY";
        default:
          return "DAILY";
      }
    };
    const formData: CustomerCreateApplicationInput = {
      amount,
      loanCategoryId: selectedCategoryId,
      source: "WEB" as any,
      clientId,
      loanDuration: Number(tenorValue && tenorValue[0]),
      durationType: durationType() as any,
    };
    try {
      setInitiationLoading(true);
      const res = await initiateApplication({ formData, token });

      if (res?.data?.initiateApplication?.success) {
        externalCustomEventTrigger(
          "initiate-application",
          true,
          JSON.stringify(res?.data?.initiateApplication)
        );
        const { applicationNumber } =
          res?.data?.initiateApplication?.application || {};

        const basePath = isAgent ? "/widget" : "";

        await applicationPagesRedirection();
      }
      setInitiationLoading(false);
      if (res?.error) {
        setInitiationLoading(false);
        setError(res?.error);
      }
    } catch (error) {
      setInitiationLoading(false);
    }
  };
  return (
    <>
      {initiationLoading && <FullPageLoader />}

      {error && (
        <Alert status="error">
          <AlertIcon />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <RadioGroup
        border="1px solid"
        borderColor="gray.200"
        borderRadius={0}
        w="100%"
        value={selectedCategoryId}
      >
        <Flex>
          <Radio
            w="100%"
            p={4}
            justifyContent="space-between"
            flexDirection="row-reverse"
            value={category?.id}
            isRequired
          >
            <Box w="100%">
              <Text
                textTransform="capitalize"
                fontWeight="bold"
              >
                {category?.name}
              </Text>
              <Text>{category?.description}</Text>
              <Text>{`Min: ${formatAmount(category?.minAmount || 0)}`}</Text>
              <Text>{`Max: ${formatAmount(category?.maxAmount || 0)}`}</Text>
            </Box>
          </Radio>
        </Flex>
      </RadioGroup>

      <Button
        onClick={() => {
          handleResetSelection();
          setShowTenor(false);
          reset();
        }}
        variant="link"
      >
        Change Loan Product
      </Button>

      <LoanAmount category={category} />

      {amount && showTenor ? <LoanTenor /> : ""}

      {amount && selectedCategoryId && tenor && isTenorValid ? (
        <ButtonGroup
          justifyContent="flex-end"
          width="100%"
          my={8}
        >
          <Button
            width="100%"
            onClick={onSubmit}
            isDisabled={
              !amount || !selectedCategoryId || !tenor || !isTenorValid
            }
          >
            Proceed
          </Button>
        </ButtonGroup>
      ) : (
        ""
      )}
    </>
  );
};

export default SelectedCategory;
