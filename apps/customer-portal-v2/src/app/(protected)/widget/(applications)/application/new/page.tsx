import React from "react";
import { GET_LOAN_CATEGORIES } from "src/graphql/query/loanCategories";
import { LoanInitiation } from "@/src/app/(protected)/_components/LoanInitiation";
import {
  applicationPagesRedirection,
  getBasePath,
  getLoanInitiationData,
} from "@/src/app/actions";
import { LoanInitiationProvider } from "@/src/services";
import { serverConfig } from "@/src/config/serverConfig";
import { cookies } from "next/headers";
import logger from "@/lib/logger";

export const revalidate = 0;
export const dynamic = "force-dynamic";
export const maxDuration = 60;

const CLIENT_INFO = `
  query ClientInfo($input: ClientInfoInput!) {
    clientInfo(input: $input) {
      clientId
      kycConfiguration {
        form
      }
    }
  }
`;

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        id
        firstName
        lastName
        kycInformation
      }
      account {
        applications {
          nodes {
            status {
              name
            }
          }
        }
        channel {
          type
        }
        cards {
          id
          expiryDate
          email
          bankName
          isDefault
          maskedPan
          status
          type
        }
      }
    }
  }
`;

const LoanInitiationPage = async () => {
  const clientAppUrlValue = await getBasePath();
  const token = cookies().get("authToken")?.value;

  const variables =
    serverConfig?.web?.appEnv === "development"
      ? { input: { slug: "kwikpay" } }
      : { input: { clientAppUrl: clientAppUrlValue } };

  const getLoanCategories = async () => {
    let loading = false;
    try {
      const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: GET_LOAN_CATEGORIES,
        }),
      });

      if (response.ok) {
        loading = false;
        const result = await response.json();

        return {
          data: result?.data,
          error: result?.errors?.[0]?.message,
          loading,
        };
      }
    } catch (error: any) {
      loading = false;
      logger({ error });
    }
  };

  const data = await getLoanCategories();

  const getClientData = async () => {
    try {
      const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: CLIENT_INFO,
          variables,
        }),
      });

      if (response.ok) {
        const result = await response.json();

        return result;
      }
    } catch (error: any) {
      logger({ error });
    }
  };

  const clientData = await getClientData();

  const getViewerData = async () => {
    try {
      const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: VIEWER_QUERY,
        }),
      });

      if (response.ok) {
        const result = await response.json();

        return result;
      }
    } catch (error: any) {
      logger({ error });
    }
  };

  const viewerData = await getViewerData();

  const isAgent = viewerData?.data?.viewer?.account?.channel?.type === "agent";

  const loanInitiationData = async ({
    amount,
    selectedCategoryId,
  }: {
    selectedCategoryId?: string;
    amount?: number;
  }) => {
    "use server";
    const {
      applicableTenor,
      eligibleAmount,
      errors: initiationError,
    } = await getLoanInitiationData({
      clientId: clientData?.data?.clientInfo?.clientId,
      amount,
      selectedCategoryId,
      token: token as string,
    });

    return {
      applicableTenor,
      eligibleAmount,
      initiationError,
    };
  };

  const pendingApplication =
    viewerData?.data?.viewer?.account?.applications?.nodes?.find(
      (node: any) => node?.status?.name === "PENDING"
    );

  if (pendingApplication) {
    await applicationPagesRedirection();
  }

  return (
    <LoanInitiationProvider
      cards={viewerData?.data?.viewer?.account?.cards}
      token={token as string}
    >
      <LoanInitiation
        data={data?.data}
        loading={data?.loading as boolean}
        error={data?.error}
        clientData={clientData?.data?.clientInfo}
        user={viewerData?.data?.viewer?.me}
        loanInitiationData={loanInitiationData}
        isAgent={isAgent}
      />
    </LoanInitiationProvider>
  );
};
export default LoanInitiationPage;
