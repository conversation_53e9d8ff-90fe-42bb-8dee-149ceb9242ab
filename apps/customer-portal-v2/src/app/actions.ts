"use server";

import { authOptions } from "@/lib/authOptions";
import logger from "@/lib/logger";
import {
  APPLICABLE_TENORS,
  CUSTOMER_ELIGIBILITY_AMOUNTS,
  CUSTOMER_REMITA_STATUS,
  GENERATE_PAYSTACK_CARD_REFERENCE,
  GET_LOAN_CATEGORIES,
  GET_PAYSTACK_CARD_REFERENCE,
  LATEST_APPLICATION_FORMDATA,
  RESOLVE_ACCOUNT_NUMBER,
} from "@/src/graphql/query";
import { APPLICATION_STEPS, bankStatementDoneSteps } from "@/utils/constants";
import {
  AmountEligibilityPayload,
  ApplicableTenorsResponse,
  Application,
  ApplicationBankStageInput,
  ApplicationStep,
  BankAccountType,
  BankStatementUploadInput,
  CardReferenceStatusInput,
  ClientInfo,
  CompleteBanksStatementInput,
  CompleteExternalBankStatementRequestInput,
  ConfirmPhoneInput,
  CreateAccountBankInput,
  CreateApplicationTraceInput,
  CustomerCreateApplicationInput,
  CustomerRemitaStatusInput,
  GenerateAddCardReferenceInput,
  GetDecidePdfStatusInput,
  InitiateBanksStatementInput,
  LatestApplicationFormDataInput,
  Maybe,
  ApplicationStatusEnum,
  SaveCustomApplicationFormInput,
  SkipCardCollectionInput,
  TriggerPhoneConfirmationInput,
  Viewer,
  SupportingDocumentInput,
  UploadRequestedSupportingDocumentInput,
  ApproveUpdatedLoanAmountInput,
  RepaymentBreakdownInput,
} from "@/__generated/graphql";
import { getServerSession } from "next-auth";
import { cookies, headers } from "next/headers";
import { redirect } from "next/navigation";
import { serverConfig } from "../config/serverConfig";
import {
  ADD_BANK_ACCOUNT,
  COMPLETE_APPLICATION,
  COMPLETE_BANK_STATEMENT_REQUEST,
  CONFIRM_PHONE,
  CREATE_APPLICATION_TRACE,
  EXTERNAL_BANK_STATEMENT_REQUEST,
  INITIATE_APPLICATIONS,
  INITIATE_BANK_STATEMENT_REQUEST,
  SAVE_CUSTOM_FORM,
  SET_BANK_ACCOUNT,
  SKIP_CARD_COLLECTION,
  TRIGGER_PHONE_CONFIRMATION,
  UPDATE_CUSTOMER_INFO,
  UPLOAD_BANK_STATEMENT,
} from "../graphql/mutation";
import { GET_BANKS } from "../graphql/query";
import { getAttributrFromProduct } from "../utils";
import { graphqlRequest } from "@/lib/graphql-client";
import { isEmpty, omitBy } from "lodash";
import { GET } from "./api/auth/[...nextauth]/route";

const CLIENT_INFO = `
  query ClientInfo($input: ClientInfoInput!) {
    clientInfo(input: $input) {
      clientId
      requiresPhoneVerification
      requiresBankStatementUpload
      kycConfiguration {
        minimumRequirements
        form
      }
      cookiesPolicyUrl
      clientTheme {
        primaryColor
        secondaryColor
      }
      howToApplyVideoLink
      logoUrl
      privacyPolicyUrl
      faviconUrl
      termsAndConditionsUrl
      name
      contactDetails {
        phone
        email
      }
      addCardCharge
      repaymentServices {
        name
      }
      paystackPubKey
      externalBankStatementTenor
      useRemita
    }
  }
`;

const VIEWER_QUERY = `
  query ViewerQuery {
    viewer {
      me {
        id
        firstName
        lastName
        kycInformation
        isPhoneConfirmed
        isEmailConfirmed
        bvnStatus {
          bvn
        }
      }
      account {
        applications {
          nodes {
            status {
              name
            }
            applicationNumber
            customApplicationForm
            requiredSteps
            completedSteps
            loanCategory {
              id
              products {
                processingFeeName
                id
                applicationForm
              }
              loanCategoryAttributes {
                attribute {
                  id
                  name
                }
                value
              }
            } 
            bankAccount {
              id
              bank {
                id
                name
              }
            }
            credit {
              ticketNo
            }
          }
        }
        channel {
          type
        }
        cards {
          id
        }
      }
    }
  }
`;

const SINGLE_APPLICATION = `
  query Application($applicationNumber: String!) {
    application(applicationNumber: $applicationNumber) {
      applicationNumber
      customApplicationForm
      requiredSteps
      loanDuration
      amount
      dateOfRepayment
      completedSteps
      id
      policy {
        id
      }
      loanCategory {
        id
        products {
          processingFeeName
          id
          applicationForm
        }
        loanCategoryAttributes {
          attribute {
            id
            name
          }
          value
        }
      } 
      bankAccount {
        id
        bank {
          id
          name
        }
      }
      credit {
        ticketNo
      }
      reviewFeedbacks {
        status
        reviewType
        oldApplicationStatus {
          name
        }
        id
        documentRequests {
          document {
            id
            user {
              id
            }
            documentName
            uploadType
            file {
              key
              bucket
              etag
              url
            }
            createdAt
          }
          id
          message
          status
          title
        }
        createdAt
        comment
      }
    }
  }
`;

const GET_DECIDE_JOB_STATUS = `
  query GetDecidePdfStatus($input: GetDecidePdfStatusInput) {
  getDecidePdfStatus(input: $input) {
    bankStatementId
    message
    status
  }
}
`;

const FETCH_MORE_VIEWER_QUERY = `
query Account($where: ApplicationWhereInput, $after: ConnectionCursor, $first: ConnectionLimitInt) {
    viewer {
      account {
        applications(where: $where, after: $after, first: $first) {
          edges {
            node {
              applicationNumber
              baseAmount
              fullAmount
              id
              status {
                name
              }
              portfolio {
                portfolioNumber
                status {
                  name
                }
              }
              loanCategory {
                id
                products {
                  applicationForm
                  id
                }
              }
              customApplicationForm
              dateOfRepayment
              createdAt
            }
            cursor
          }
          totalCount
          pageInfo {
            endCursor
            hasNextPage
            startCursor
            hasPreviousPage
          }
        }
      }
    }
  }
`;

const FETCH_MORE_TRANSACTIONS = `
  query ViewerQuery ($after: ConnectionCursor, $first: ConnectionLimitInt) {
     viewer {
      account {
        portfolios {
          edges {
            node {
              transactions(after: $after, first: $first) {
                pageInfo {
                  endCursor
                  hasNextPage
                  startCursor
                  hasPreviousPage
                }
                totalCount
                edges {
                  cursor
                  node {
                    amount
                    createdAt
                    id
                    paymentMethod {
                      name
                      iconUrl
                    }
                    status
                    type {
                      name
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;

const APPROVE_UPDATED_LOAN_AMOUNT_MUTATION = `
  mutation ApproveUpdatedLoanAmount($input: ApproveUpdatedLoanAmountInput!) {
    approveUpdatedLoanAmount(input: $input) {
      application {
        fullAmount
        dateOfRepayment
        reviewDetails {
          rejectReason
        }
      }
      success
      mbsFeedback {
        status
        message
        requestId
        feedback
      }
    }
  }
`;

const UPLOAD_REQUESTED_DOCUMENT = `
  mutation UploadRequestedSupportingDocument($input: UploadRequestedSupportingDocumentInput!) {
    uploadRequestedSupportingDocument(input: $input) {
      success
      mbsFeedback {
        status
        requestId
        message
        feedback
      }
    }
  }
`;

const UPLOAD_SUPPORTING_DOCUMENT = `
  mutation UploadSupportingDocument($input: SupportingDocumentInput!) {
    uploadSupportingDocument(input: $input) {
      id
      user {
        id
      }
      documentName
      file {
        url
      }
    }
  }
`;

const GET_REPAYMENT_BREAKDOWN = `
  query GetRepaymentBreakdown($input: RepaymentBreakdownInput!) {
    getRepaymentBreakdown(input: $input) {
      duration
      interestPerPeriodInDecimal
      principalAmount
      rateInDecimal
      repaymentBreakdown {
        principalBalance
        expectedPayment
        interestPortion
        principalPortion
        endingBalance
        dueDate
      }
      repaymentFrequency
      totalExpectedPayment
      totalInterest
      totalInterestPortion
    }
  }
`;

export const getClientInfo = async () => {
  try {
    const clientAppUrlValue = await getBasePath();
    const variables =
      serverConfig?.web?.appEnv === "development"
        ? { input: { slug: "kwikpay" } }
        : { input: { clientAppUrl: clientAppUrlValue } };

    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        query: CLIENT_INFO,
        variables,
      }),
    });

    if (response.ok) {
      const result = await response.json();

      return result;
    }
  } catch (error: any) {
    logger({ error });
  }
};

export const getViewerInfo = async ({ token }: { token: string }) => {
  try {
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: VIEWER_QUERY,
      }),
    });

    if (response.ok) {
      const result = await response.json();

      return result;
    }
  } catch (error: any) {
    logger({ error });
  }
};

export const getEligibleAmount = async ({
  categoryId,
  token,
}: {
  categoryId: string;
  token: string;
}) => {
  let loading = false;

  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: CUSTOMER_ELIGIBILITY_AMOUNTS,
        variables: {
          input: {
            categoryId,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();

      loading = false;
      return {
        eligibleAmount: result?.data?.customerAmountEligibility,
        isEligibleAmountLoading: loading,
        isEligibleAmountError: result?.errors?.[0]?.message,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const getLoanTenor = async ({
  token,
  loanAmount,
  categoryId,
  clientId,
}: {
  categoryId: string;
  clientId: string;
  loanAmount: number;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: APPLICABLE_TENORS,
        variables: {
          input: {
            loanAmount,
            categoryId,
            clientId,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        loanTenor: result?.data?.applicableTenors,
        loanTenorLoading: loading,
        loanTenorError: result?.errors?.[0]?.message,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const getCustomerRemitaStatus = async ({
  token,
  ...data
}: {
  data: CustomerRemitaStatusInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: CUSTOMER_REMITA_STATUS,
        variables: {
          input: {
            ...data,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        customerRemitaStatus: result?.data?.customerRemitaStatus,
        customerRemitaStatusLoading: loading,
        customerRemitaStatusError: result?.errors?.[0]?.message,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const getByPayStackRef = async ({
  token,
  referenceData,
}: {
  referenceData: CardReferenceStatusInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: GET_PAYSTACK_CARD_REFERENCE,
        variables: {
          input: {
            ...referenceData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        getByPayStackRef: result?.data?.getCardReferenceStatus,
        getByPayStackRefLoading: loading,
        getByPayStackRefError: result?.errors?.[0]?.message,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const generateByPayStackRef = async ({
  token,
  referenceData,
}: {
  referenceData: GenerateAddCardReferenceInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: GENERATE_PAYSTACK_CARD_REFERENCE,
        variables: {
          input: {
            ...referenceData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();

      loading = false;
      return {
        generateByPayStackRef: result?.data?.getAddCardReference,
        generateByPayStackRefLoading: loading,
        generateByPayStackRefError: result?.errors?.[0]?.message,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const getLatestForm = async ({
  token,
  formData,
}: {
  formData: LatestApplicationFormDataInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: LATEST_APPLICATION_FORMDATA,
        variables: {
          input: {
            ...formData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        latestForm: result?.data?.latestApplicationFormData,
        latestFormLoading: loading,
        latestFormError: result?.errors?.[0]?.message,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const getBankDetails = async ({
  token,
  loanCategoryId,
}: {
  loanCategoryId: string;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: GET_BANKS,
        variables: {
          input: {
            loanCategoryId,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        banksData: result?.data?.getBanks,
        banksDataLoading: loading,
        banksDataError: result?.errors?.[0]?.message,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const getResolvedAccountNumber = async ({
  bankId,
  accountNumber,
  accountType,
  token,
}: {
  bankId: string;
  accountNumber: string;
  accountType: BankAccountType;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: RESOLVE_ACCOUNT_NUMBER,
        variables: {
          bankId,
          accountNumber,
          accountType,
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        resolveBankData: result?.data?.resolveAccountNumber,
        resolveBankDataLoading: loading,
        resolveBankDataError: result?.errors?.[0]?.message,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const getLoanInitiationData = async ({
  clientId,
  selectedCategoryId,
  amount,
  token,
}: {
  clientId: string;
  selectedCategoryId?: string;
  amount?: number;
  token: string;
}) => {
  let eligibleAmount: AmountEligibilityPayload[] = [];
  let applicableTenor: ApplicableTenorsResponse[] = [];
  // eslint-disable-next-line prefer-const
  let errors = { eligibleAmountError: "", applicableTenorError: "" };

  try {
    const eligibilityData = await getEligibleAmount({
      categoryId: selectedCategoryId as string,
      token,
    });

    if (eligibilityData?.eligibleAmount) {
      eligibleAmount =
        eligibilityData?.eligibleAmount as AmountEligibilityPayload[];
    }

    if (eligibilityData?.isEligibleAmountError) {
      errors.eligibleAmountError =
        eligibilityData?.isEligibleAmountError?.message;
    }
  } catch (error: any) {
    throw new Error(error?.message);
  }

  try {
    const tenorData = await getLoanTenor({
      loanAmount: amount as number,
      categoryId: selectedCategoryId as string,
      clientId: clientId,
      token,
    });

    if (tenorData?.loanTenor) {
      applicableTenor = tenorData?.loanTenor as ApplicableTenorsResponse[];
    }

    if (tenorData?.loanTenorError) {
      errors.applicableTenorError = tenorData?.loanTenorError?.message;
    }
  } catch (error: any) {
    throw new Error(error?.message);
  }

  return {
    eligibleAmount,
    applicableTenor,
    errors,
  };
};
export const getBasePath = async () => {
  const headersList = headers();
  const host = headersList.get("host");
  const protocol = process.env.NODE_ENV === "production" ? "https" : "http";
  const fullUrl = `${protocol}://${host}${headersList.get("x-nextjs-rewrite") || ""}`;
  const url = new URL(fullUrl);
  const basePath = url.origin;

  return basePath;
};

export const updateForm = async ({
  token,
  ...formData
}: {
  formData: any;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: UPDATE_CUSTOMER_INFO,
        variables: {
          input: {
            kycInformation: {
              data: { ...formData },
            },
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;

      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const initiateApplication = async ({
  token,
  formData,
}: {
  formData: CustomerCreateApplicationInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: INITIATE_APPLICATIONS,
        variables: {
          input: {
            ...formData,
          },
        },
      }),
    });

    loading = false;

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const confirmPhone = async ({
  token,
  phoneData,
}: {
  phoneData: ConfirmPhoneInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: CONFIRM_PHONE,
        variables: {
          input: {
            ...phoneData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const triggerConfirmPhone = async ({
  token,
  phoneData,
}: {
  phoneData: TriggerPhoneConfirmationInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: TRIGGER_PHONE_CONFIRMATION,
        variables: {
          input: {
            ...phoneData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const runSkipCardCollection = async ({
  token,
  input,
}: {
  input: SkipCardCollectionInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: SKIP_CARD_COLLECTION,
        variables: {
          input,
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const createTrace = async ({
  token,
  traceData,
}: {
  traceData: CreateApplicationTraceInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: CREATE_APPLICATION_TRACE,
        variables: {
          input: {
            ...traceData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const addBank = async ({
  token,
  bankData,
}: {
  bankData: CreateAccountBankInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: ADD_BANK_ACCOUNT,
        variables: {
          input: {
            ...bankData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const setBank = async ({
  token,
  bankData,
}: {
  bankData: ApplicationBankStageInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: SET_BANK_ACCOUNT,
        variables: {
          input: {
            ...bankData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const bankStatementRequest = async ({
  token,
  bankData,
}: {
  bankData: InitiateBanksStatementInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: INITIATE_BANK_STATEMENT_REQUEST,
        variables: {
          input: {
            ...bankData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const completeBankStatementRequest = async ({
  token,
  bankData,
}: {
  bankData: CompleteBanksStatementInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: COMPLETE_BANK_STATEMENT_REQUEST,
        variables: {
          input: {
            ...bankData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const externalBankStatementRequest = async ({
  token,
  bankData,
}: {
  bankData: CompleteExternalBankStatementRequestInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: EXTERNAL_BANK_STATEMENT_REQUEST,
        variables: {
          input: {
            ...bankData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const uploadBankStatement = async ({
  token,
  ...bankStatement
}: {
  bankStatement: BankStatementUploadInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: UPLOAD_BANK_STATEMENT,
        variables: {
          input: {
            ...bankStatement,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const saveForm = async ({
  token,
  formData,
}: {
  formData: SaveCustomApplicationFormInput;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: SAVE_CUSTOM_FORM,
        variables: {
          input: {
            ...formData,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const completeApplication = async ({
  token,
  applicationId,
}: {
  applicationId: string;
  token: string;
}) => {
  let loading = false;
  try {
    loading = true;
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: COMPLETE_APPLICATION,
        variables: {
          input: {
            applicationId,
          },
        },
      }),
    });

    if (response.ok) {
      const result = await response.json();
      loading = false;
      const CookieStore = cookies();

      CookieStore.delete("widgetSkippedCardAddition");

      return {
        data: result?.data,
        error: result?.errors && result?.errors[0]?.message,
        loading,
      };
    }
    loading = false;
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

export const upload = async ({ data, token }: { data: any; token: string }) => {
  const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: data,
  });

  const result = await response.json();
  return result;
};

export const getRedirectUri = async () => {
  const CookieStore = cookies();

  const redirectUri = CookieStore.get("redirectUri")?.value;
  const customerId = CookieStore.get("customerId")?.value;

  const url = `${redirectUri}/v1/customers/${customerId}`;

  return url;
};

const getLoanCategories = async ({ token }: { token: string }) => {
  let loading = false;
  try {
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: GET_LOAN_CATEGORIES,
      }),
    });
    if (response.ok) {
      loading = false;
      const result = await response.json();
      return {
        data: result?.data,
        error: result?.errors?.[0]?.message,
        loading,
      };
    }
  } catch (error: any) {
    loading = false;
    logger({ error });
  }
};

const getApplicationData = async ({
  token,
  applicationNumber,
}: {
  token: string;
  applicationNumber: string;
}) => {
  try {
    const response = await fetch(`${serverConfig?.ignite?.baseUrl}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: SINGLE_APPLICATION,
        variables: {
          applicationNumber,
        },
      }),
    });
    if (response.ok) {
      const result = await response.json();
      return result;
    }
  } catch (error: any) {
    logger({ error });
  }
};

export const applicationPagesRedirection = async () => {
  const CookieStore = cookies();

  const token = CookieStore.get("authToken")?.value;

  const viewerData = await getViewerInfo({ token: token as string });
  const clientData = await getClientInfo();
  const data = await getLoanCategories({ token: token as string });

  const pendingApplication =
    viewerData?.data?.viewer?.account?.applications?.nodes?.find(
      (node: any) => node?.status?.name === "PENDING"
    );
  const applicationNumber = pendingApplication?.applicationNumber;
  const applicationData = await getApplicationData({
    token: token as string,
    applicationNumber: applicationNumber as string,
  });

  const { loanCategory, bankAccount, credit } =
    applicationData?.data?.application || {};
  const { isPhoneConfirmed } = viewerData?.data?.viewer?.me || {};

  const namesSet = new Set(["requiresPhoneVerification"]);
  const loanCategoryAttributes = loanCategory?.loanCategoryAttributes!;

  const applicationAttributes: any = getAttributrFromProduct({
    namesSet,
    loanCategoryAttributes,
  });

  const requiresPhoneVerification =
    applicationAttributes?.requiresPhoneVerification;
  const { categories } = data?.data?.getLoanCategories || {};
  const customApplication = categories?.find(
    (category: any) => category?.id === loanCategory?.id
  );
  const ticketNumber = credit?.ticketNo;
  const isCustomApplication =
    customApplication?.products &&
    customApplication?.products[0]?.applicationForm?.length > 0;
  const hasCards = (viewerData?.data?.viewer?.account?.cards?.length ?? 0) > 0;
  const isApplicationStepPending = (step: any) =>
    applicationData?.data?.application?.requiredSteps?.includes(step) &&
    !applicationData?.data?.application?.completedSteps?.includes(step);
  const completedStepsHashMap: Record<string, number> = {};
  const shouldShowMbsOtp = async () => {
    if (
      bankStatementDoneSteps.find((step) => !!completedStepsHashMap[step!]) ||
      isApplicationStepPending(APPLICATION_STEPS.uploadBankStatement.name)
    ) {
      return false;
    }

    return (
      !ticketNumber &&
      applicationData?.data?.application?.requiredSteps?.includes(
        APPLICATION_STEPS.initiateBankStatementRequest.name
      )
    );
  };
  const shouldUploadBankStatement = () => {
    if (bankStatementDoneSteps?.find((step) => !!completedStepsHashMap[step!]))
      return false;
    return (
      isApplicationStepPending(APPLICATION_STEPS.uploadBankStatement.name) ||
      clientData?.data?.clientInfo?.requiresBankStatementUpload
    );
  };
  const hasBank = bankAccount?.id;
  const hasMbsOtp = await shouldShowMbsOtp();
  const hasUpload = await shouldUploadBankStatement();

  if (isCustomApplication) {
    if (requiresPhoneVerification && !isPhoneConfirmed) {
      redirect(
        `/widget/application-custom/${applicationNumber}/phone-verification`
      );
    } else {
      redirect(`/widget/application-custom/${applicationNumber}`);
    }
  } else {
    if (requiresPhoneVerification && !isPhoneConfirmed) {
      redirect(`/widget/application/${applicationNumber}/phone-verification`);
    } else if (!hasCards) {
      redirect(`/widget/application/${applicationNumber}/add-card`);
    } else if (!hasBank) {
      redirect(`/widget/application/${applicationNumber}/bank-info`);
    } else if (!hasMbsOtp) {
      redirect(`/widget/application/${applicationNumber}/mbs-instruction`);
    } else if (hasUpload) {
      redirect(`/widget/application/${applicationNumber}/upload-statement`);
    } else {
      redirect(`/widget/application/${applicationNumber}/breakdown`);
    }
  }
};

export const applicationRedirection = async () => {
  const CookieStore = cookies();

  const token = CookieStore.get("authToken")?.value;

  const viewerInfo = await getViewerInfo({ token: token as string });

  const pendingApplication =
    viewerInfo?.data?.viewer?.account?.applications?.nodes?.find(
      (node: any) => node?.status?.name === "PENDING"
    );

  if (pendingApplication) {
    await applicationPagesRedirection();
  }

  if (viewerInfo?.data?.viewer?.me?.kycInformation && !pendingApplication) {
    redirect("/widget/application/new");
  }
};

export const getBaseUrlWithFirstSegment = async ({
  basePath,
}: {
  basePath?: string | null;
}): Promise<string | null> => {
  try {
    let origin = headers().get("origin")?.trim();
    if (!origin) return null;

    if (!/^https?:\/\//i.test(origin)) {
      origin = `http://${origin}`;
    }

    const path = basePath?.trim().replace(/^\/+|\/+$/g, "");

    try {
      const url = new URL(origin);
      return path ? `${url.origin}/${path}` : url.origin;
    } catch {
      const isValidShape = /^https?:\/\/[\w.-]+(:\d+)?$/i.test(origin);
      return isValidShape ? (path ? `${origin}/${path}` : origin) : null;
    }
  } catch {
    return null;
  }
};

export const getDecideJobStatus = async ({
  input,
  accessToken,
}: {
  input: GetDecidePdfStatusInput;
  accessToken: string;
}) => {
  "use server";
  try {
    if (!input?.applicationId) {
      return;
    }

    const status = await graphqlRequest(
      GET_DECIDE_JOB_STATUS,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (status?.errors?.length) {
      return { error: status?.errors?.[0]?.message, data: null };
    }

    return { error: null, data: status?.data?.getDecidePdfStatus };
  } catch (error: any) {
    return { error: error?.message, data: null };
  }
};

export const appRedirection = async ({
  basePath,
}: {
  basePath?: string | null;
}) => {
  const session = await getServerSession(authOptions);

  const accessToken = session?.accessToken;

  const path = basePath ? `/${basePath}` : `/`;

  if (!accessToken) {
    redirect(path);
  }

  try {
    const viewerQuery = await graphqlRequest(
      VIEWER_QUERY,
      "POST",
      {},
      accessToken
    );
    const viewerData: Viewer = viewerQuery?.data?.viewer;

    const { me, account } = viewerData || {};
    const pendingApplication = account?.applications?.nodes?.find(
      (app: Maybe<Application>) => app?.status?.name === "PENDING"
    ) as Application;
    const latestApplication = account?.applications?.nodes?.[0] as Application;

    const applicationNumber = pendingApplication?.applicationNumber;
    const applicationQuery = await graphqlRequest(
      SINGLE_APPLICATION,
      "POST",
      { applicationNumber },
      accessToken
    );
    const application: Application = applicationQuery?.data?.application;

    if (!application || !latestApplication) {
      return;
    }

    const {
      loanCategory,
      bankAccount,
      credit,
      completedSteps,
      requiredSteps,
      customApplicationForm,
      id: applicationId,
    } = application || latestApplication;

    const baseUrl = basePath
      ? `/${basePath}/application/${applicationNumber}`
      : `/application/${applicationNumber}`;
    const namesSet = new Set([
      "requiresPhoneVerification",
      "noCardImplementation",
    ]);
    const loanCategoryAttributes = loanCategory?.loanCategoryAttributes!;
    const ticketNumber = credit?.ticketNo;

    const applicationAttributes: any = getAttributrFromProduct({
      namesSet,
      loanCategoryAttributes,
    });
    const passedExternalMbsInstruction =
      cookies().get("passedExternalMbsInstruction")?.value === "true";

    const isApplicationStepPending = (step: any) =>
      requiredSteps?.includes(step) && !completedSteps?.includes(step);

    const decideJobData = await getDecideJobStatus({
      input: { applicationId },
      accessToken,
    });

    const pdfJobIsDone = ["DONE", "SKIPPED"].includes(
      decideJobData?.data?.status
    );
    const completedStepsHashMap =
      completedSteps?.reduce(
        (map: { [key: string]: boolean }, step: Maybe<ApplicationStep>) => {
          if (step) {
            map[step] = true;
          }
          return map;
        },
        {}
      ) || {};

    const activeFormTabs = loanCategory?.products?.[0]?.applicationForm?.filter(
      (tab: any) => !tab?.linkedToOption
    );
    const shouldUploadBankStatement = () => {
      if (
        bankStatementDoneSteps.some((step) => !!completedStepsHashMap[step!]) &&
        !pdfJobIsDone
      ) {
        return false;
      }
      return isApplicationStepPending(
        APPLICATION_STEPS.uploadBankStatement.name
      );
    };

    const shouldSelectBankAccount = () => {
      if (!bankAccount?.id) return true;
      return isApplicationStepPending(APPLICATION_STEPS.setBankAccount.name);
    };

    const shouldShowExternalMbs = () => {
      if (
        bankStatementDoneSteps.some((step) => !!completedStepsHashMap[step!]) ||
        isApplicationStepPending(APPLICATION_STEPS.uploadBankStatement.name)
      ) {
        return false;
      }
      const isPending = Boolean(passedExternalMbsInstruction);
      const showExternalMbs = !isPending;
      const requiresBankStatementStep = application?.requiredSteps?.some(
        (step) => step === "COMPLETE_EXTERNAL_BANK_STATEMENT_REQUEST"
      );
      return requiresBankStatementStep && !ticketNumber && showExternalMbs;
    };

    const shouldShowMbsOtp = () => {
      if (
        bankStatementDoneSteps.some((step) => !!completedStepsHashMap[step!]) ||
        isApplicationStepPending(APPLICATION_STEPS.uploadBankStatement.name)
      ) {
        return false;
      }

      const requiresBankStatementStep = application?.requiredSteps?.some(
        (step) =>
          step === "INITIATE_BANK_STATEMENT_REQUEST" ||
          step === "COMPLETE_EXTERNAL_BANK_STATEMENT_REQUEST"
      );

      return !ticketNumber && requiresBankStatementStep;
    };

    const isEmailConfirmed = me?.isEmailConfirmed;
    const isPhoneConfirmed = me?.isPhoneConfirmed;
    const outOfTabs = activeFormTabs?.length
      ? customApplicationForm?.step >= activeFormTabs.length
      : false;

    const noCardImplementation = !!applicationAttributes?.noCardImplementation;

    const hasSkippedCard = Boolean(
      cookies().get("skippedCardAddition")?.value === "true"
    );

    const shouldAddCard = () => {
      const hasCards =
        account?.cards &&
        Array.isArray(account.cards) &&
        account.cards.length > 0;

      if (noCardImplementation || hasSkippedCard) return false;

      return !hasCards;
    };

    const shouldVerifyEmail = () => !isEmailConfirmed;
    const shouldVerifyPhone = () =>
      (isPhoneConfirmed == null || !isPhoneConfirmed) &&
      applicationAttributes?.requiresPhoneVerification;

    const isCompleteApplication = !!completedSteps?.includes(
      APPLICATION_STEPS.completeApplication.name
    );

    const getRouteForStep = (stepType: string) => {
      const stepConfig = {
        VERIFY_EMAIL: {
          applicationStep: "verifyEmail",
          condition: shouldVerifyEmail,
          route: `${baseUrl}/verify-email`,
        },
        VERIFY_PHONE: {
          applicationStep: "verifyPhone",
          condition: shouldVerifyPhone,
          route: `${baseUrl}/verify-phone`,
        },
        FORM_PROGRESS: {
          applicationStep: "formProgress",
          condition: () => activeFormTabs?.length > 0 && !outOfTabs,
          route: baseUrl,
        },
        ADD_CARD: {
          applicationStep: "addCard",
          condition: shouldAddCard,
          route: `${baseUrl}/add-card`,
        },
        BANK_ACCOUNT: {
          applicationStep: APPLICATION_STEPS.setBankAccount.name,
          condition: shouldSelectBankAccount,
          route: `${baseUrl}/bank-info`,
        },
        EXTERNAL_MBS: {
          applicationStep:
            APPLICATION_STEPS.completeExternalBankStatementRequest.name,
          condition: shouldShowExternalMbs,
          route: `${baseUrl}/mbs-instruction`,
        },
        MBS_OTP: {
          applicationStep: APPLICATION_STEPS.initiateBankStatementRequest.name,
          condition: shouldShowMbsOtp,
          route: `${baseUrl}/mbs`,
        },
        UPLOAD_BANK_STATEMENT: {
          applicationStep: APPLICATION_STEPS.uploadBankStatement.name,
          condition: shouldUploadBankStatement,
          route: `${baseUrl}/upload-statement`,
        },
      };

      const config = stepConfig[stepType as keyof typeof stepConfig];
      if (!config) return undefined;

      // Skip if the step was already completed.
      if (
        config.applicationStep &&
        completedStepsHashMap[config.applicationStep] &&
        stepType !== "MBS_OTP" &&
        stepType !== "EXTERNAL_MBS"
      ) {
        return undefined;
      }
      // Only return a route if the condition for the step is met.
      if (config.condition && !config.condition()) {
        return undefined;
      }
      return config.route === "" ? baseUrl : config.route;
    };

    // Define the step sequence to check.
    const stepSequence = [
      "VERIFY_EMAIL",
      "VERIFY_PHONE",
      "FORM_PROGRESS",
      "ADD_CARD",
      "BANK_ACCOUNT",
      "EXTERNAL_MBS",
      "MBS_OTP",
      "UPLOAD_BANK_STATEMENT",
    ];

    // Loop over each step in sequence until a valid route is found.
    for (const stepType of stepSequence) {
      const route = getRouteForStep(stepType);
      if (route) return route;
    }

    // Fallback route if none of the conditions match.
    if (isCompleteApplication) {
      return `${basePath ?? ""}/dashboard`;
    } else if (!pendingApplication) {
      return `${basePath ?? ""}/application/new`;
    } else {
      return `${baseUrl}/breakdown`;
    }
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : "Unknown error");
  }
};
export const signUpRedirection = async ({
  basePath,
}: {
  basePath?: string | null;
}) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken!;

  const path = basePath ? `/${basePath}` : `/`;

  if (!accessToken) {
    redirect(path);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;
  const { me } = viewerData || {};
  const { isEmailConfirmed, bvnStatus, kycInformation } = me || {};
  const signUpBaseUrl = basePath ? `/${basePath}/sign-up` : `/sign-up`;

  const getRouteForStep = (stepType: string) => {
    const stepConfig = {
      VERIFY_BVN: {
        applicationStep: "verifyBvn",
        condition: !bvnStatus?.bvn,
        route: `${signUpBaseUrl}/verify-bvn`,
      },
      VERIFY_EMAIL: {
        applicationStep: "verifyEmail",
        condition: !!bvnStatus?.bvn && !isEmailConfirmed,
        route: `${signUpBaseUrl}/verify-email`,
      },
      CUSTOMER_INFO: {
        applicationStep: "customerInfo",
        condition:
          !!bvnStatus?.bvn && !!isEmailConfirmed && !kycInformation?.data,
        route: `${signUpBaseUrl}/customer-info`,
      },
    };

    const config = stepConfig[stepType as keyof typeof stepConfig];
    if (!config) return undefined;

    if (config.condition) {
      return config.route;
    }

    return undefined;
  };

  // Define the step sequence to check in order of priority
  const stepSequence = ["VERIFY_BVN", "VERIFY_EMAIL", "CUSTOMER_INFO"];

  for (const stepType of stepSequence) {
    const route = getRouteForStep(stepType);
    if (route) {
      return route;
    }
  }

  const url = basePath ? `/${basePath}/application/new` : `/application/new`;
  return url;
};

export const loginRedirection = async ({
  basePath,
}: {
  basePath?: string | null;
}) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  const path = basePath ? `/${basePath}` : `/`;

  if (!accessToken) {
    redirect(path);
  }

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewerData: Viewer = viewerQuery?.data?.viewer;

  const { account, me } = viewerData || {};

  const hasBVN = !!me?.bvnStatus?.bvn;
  const hasKYC = !!me?.kycInformation;
  const hasConfirmedEmail = !!me?.isEmailConfirmed;

  const pendingApplication = account?.applications?.nodes?.find(
    (app: Maybe<Application>) => app?.status?.name === "PENDING"
  ) as Application;

  const applicationCount = account?.applications?.nodes?.length || 0;
  const isCompleteApplication = !!pendingApplication?.completedSteps?.includes(
    APPLICATION_STEPS.completeApplication.name
  );

  const result = await appRedirection({
    basePath: basePath,
  });

  if (isCompleteApplication) {
    const url = basePath ? `/${basePath}/dashboard` : `/dashboard`;
    redirect(url);
  } else if (applicationCount === 0) {
    if (!hasBVN) {
      const url = basePath
        ? `/${basePath}/sign-up/verify-bvn`
        : `/sign-up/verify-bvn`;
      redirect(url);
    } else if (!hasConfirmedEmail) {
      const url = basePath
        ? `/${basePath}/sign-up/verify-email`
        : `/sign-up/verify-email`;
      redirect(url);
    } else if (!hasKYC) {
      const url = basePath
        ? `/${basePath}/sign-up/customer-info`
        : `/sign-up/customer-info`;
      redirect(url);
    } else {
      const url = basePath
        ? `/${basePath}/application/new`
        : `/application/new`;
      redirect(url);
    }
  } else if (applicationCount > 0 && !!pendingApplication) {
    redirect(result!);
  } else {
    const url = basePath ? `/${basePath}/dashboard` : `/dashboard`;
    redirect(url);
  }
};

export async function fetchMoreApplications(
  endCursor: string,
  searchTerm: string
) {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  if (!accessToken) {
    return { error: "No access token", data: null };
  }

  const page = 10;
  const cleanSearchTerm = searchTerm.trim();

  try {
    let variables: { where: any; first: number; after?: string } = {
      where: {},
      first: page,
      after: endCursor,
    };

    if (cleanSearchTerm) {
      omitBy(
        (variables.where = {
          applicationNumber: cleanSearchTerm.toUpperCase(),
        }),
        isEmpty
      );
    }

    const res = await graphqlRequest(
      FETCH_MORE_VIEWER_QUERY,
      "POST",
      variables,
      accessToken
    );

    if (res?.errors && res.errors.length > 0) {
      return { error: res.errors[0].message, data: null };
    }
    if (res?.data) {
      const newData = res.data.viewer?.account;
      const newEdges = newData?.applications?.edges || [];
      return { data: { newEdges, newData }, error: null };
    }
  } catch (err) {
    return {
      error:
        err instanceof Error
          ? err.message
          : "An error occurred while fetching data",
      data: null,
    };
  }
}

export async function fetchMoreTransactions(endCursor: string) {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  if (!accessToken) {
    return { error: "No access token", data: null };
  }

  const page = 10;

  try {
    let variables: { first: number; after?: string } = {
      first: page,
      after: endCursor,
    };

    const res = await graphqlRequest(
      FETCH_MORE_TRANSACTIONS,
      "POST",
      variables,
      accessToken
    );

    if (res?.errors && res.errors.length > 0) {
      return { error: res.errors[0].message, data: null };
    }
    if (res?.data) {
      const newData = res.data.viewer?.account;
      const portfolios = newData?.portfolios?.edges || [];
      const newEdges =
        portfolios.length > 0
          ? portfolios[0]?.node?.transactions?.edges || []
          : [];

      return {
        data: {
          newEdges,
          newData:
            portfolios.length > 0
              ? portfolios[0]?.node?.transactions
              : { pageInfo: { hasNextPage: false, endCursor: null } },
        },
        error: null,
      };
    }
  } catch (err) {
    return {
      error:
        err instanceof Error
          ? err.message
          : "An error occurred while fetching data",
      data: null,
    };
  }
}

export async function getCpV2ClientInfo({
  basePath,
}: {
  basePath: string | null;
}): Promise<ClientInfo | null> {
  "use server";
  try {
    const clientAppUrl = await getBaseUrlWithFirstSegment({ basePath });

    let variables;

    if (serverConfig?.web?.appEnv === "development") {
      variables = { input: { slug: "kwikpay" } };
    } else if (clientAppUrl) {
      variables = { input: { clientAppUrl } };
    } else {
      variables = { input: { slug: basePath } };
    }

    if (variables.input.slug && !/^[a-zA-Z0-9-]+$/.test(variables.input.slug)) {
      return null;
    }

    const { data } = await graphqlRequest(CLIENT_INFO, "POST", variables);
    const clientInfo: ClientInfo | null = data?.clientInfo;

    return clientInfo?.name ? clientInfo : null;
  } catch (error) {
    return null;
  }
}

export const loanAdjustment = async ({
  input,
}: {
  input: ApproveUpdatedLoanAmountInput;
}) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  if (!accessToken) {
    return { error: "No access token", data: null };
  }

  try {
    const res = await graphqlRequest(
      APPROVE_UPDATED_LOAN_AMOUNT_MUTATION,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors && res.errors.length > 0) {
      return { error: res.errors[0].message, data: null };
    }

    return {
      data: res?.data?.approveUpdatedLoanAmount,
      error: null,
    };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "An error occurred during loan adjustment",
      data: null,
    };
  }
};

export const approveUpdatedLoanAmount = async (data: {
  applicationId: string;
  requestId: string;
}) => {
  const { applicationId, requestId } = data;

  return await loanAdjustment({
    input: {
      applicationId,
      requestId,
      approve: true,
    },
  });
};

export const declineUpdatedLoanAmount = async (data: {
  applicationId: string;
  requestId: string;
}) => {
  const { applicationId, requestId } = data;

  return await loanAdjustment({
    input: {
      applicationId,
      requestId,
      approve: false,
    },
  });
};

export const uploadSupportingDocument = async ({
  file,
  documentName,
}: {
  file: any;
  documentName: string;
}) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  if (!accessToken) {
    return { error: "No access token", data: null };
  }

  try {
    if (!file || !file.data || !file.name) {
      return { error: "Invalid file provided", data: null };
    }

    const fileBuffer = Buffer.from(file.data, "base64");

    const formData = new FormData();
    formData.append(
      "operations",
      JSON.stringify({
        query: UPLOAD_SUPPORTING_DOCUMENT,
        variables: {
          input: {
            file: null,
            documentName,
            userId: session.user?.id,
          },
        },
      })
    );
    formData.append(
      "map",
      JSON.stringify({
        "1": ["variables.input.file"],
      })
    );

    const blob = new Blob([fileBuffer], { type: file.type });
    formData.append("1", blob, file.name);

    const response = await fetch(`${serverConfig.ignite.baseUrl}/graphql`, {
      method: "POST",
      headers: { Authorization: `Bearer ${accessToken}` },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    if (result.errors && result.errors.length > 0) {
      return { error: result.errors[0].message, data: null };
    }

    return { data: result.data, error: null };
  } catch (err: any) {
    return {
      error:
        err instanceof Error
          ? err.message
          : "An error occurred during supporting document upload",
      data: null,
    };
  }
};

export const uploadRequestedDocument = async ({
  input,
}: {
  input: UploadRequestedSupportingDocumentInput;
}) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  if (!accessToken) {
    return { error: "No access token", data: null };
  }

  try {
    if (
      !input?.requestId ||
      !input?.requestedDocuments ||
      input?.requestedDocuments?.length === 0
    ) {
      return {
        error: "Invalid input: missing request ID or documents",
        data: null,
      };
    }

    const invalidDocuments = input?.requestedDocuments?.filter(
      (doc) => !doc?.uploadedDocumentId || !doc?.documentRequestId
    );

    if (invalidDocuments && invalidDocuments.length > 0) {
      return { error: "Some documents are missing upload IDs", data: null };
    }

    const res = await graphqlRequest(
      UPLOAD_REQUESTED_DOCUMENT,
      "POST",
      {
        input,
      },
      accessToken
    );

    if (res?.errors && res.errors.length > 0) {
      return { error: res.errors[0].message, data: null };
    }

    return {
      data: res?.data?.uploadRequestedSupportingDocument,
      error: null,
    };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "An error occurred during requested supporting document upload",
      data: null,
    };
  }
};

export const messagingService = async () => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  const viewerQuery = await graphqlRequest(
    VIEWER_QUERY,
    "POST",
    {},
    accessToken
  );

  const viewer: Viewer = viewerQuery?.data?.viewer;
  const applicationNumber =
    viewer?.account?.applications?.nodes?.[0]?.applicationNumber;

  const applicationQuery = await graphqlRequest(
    SINGLE_APPLICATION,
    "POST",
    { applicationNumber },
    accessToken
  );
  const application: Application = applicationQuery?.data?.application;

  const pendingFeedback =
    application &&
    application?.status?.name !== ApplicationStatusEnum.Approved &&
    application?.status?.name !== ApplicationStatusEnum.Denied &&
    application?.reviewFeedbacks &&
    application?.reviewFeedbacks.find(
      (feedback) =>
        feedback?.status === "PENDING" &&
        feedback?.reviewType !== "OFFER_LETTER_REQUEST"
    );

  const pendingDocumentFeedback =
    pendingFeedback &&
    pendingFeedback?.documentRequests?.length !== 0 &&
    pendingFeedback?.documentRequests?.find(
      (document) => document.status === "PENDING"
    );

  return {
    application,
    pendingDocumentFeedback,
    pendingFeedback,
  };
};

export const getRepaymentBreakdown = async ({
  policyId,
  principalAmount,
  duration,
  applicationId,
}: {
  policyId: RepaymentBreakdownInput["policyId"];
  principalAmount: RepaymentBreakdownInput["principalAmount"];
  duration: RepaymentBreakdownInput["duration"];
  applicationId: RepaymentBreakdownInput["applicationId"];
}) => {
  const session = await getServerSession(authOptions);
  const accessToken = session?.accessToken;

  if (
    !policyId ||
    !principalAmount ||
    !duration ||
    !applicationId ||
    !accessToken
  ) {
    return null;
  }

  const breakdownQuery = await graphqlRequest(
    GET_REPAYMENT_BREAKDOWN,
    "POST",
    {
      input: { policyId, principalAmount, duration, applicationId },
    },
    accessToken
  );

  return breakdownQuery?.data?.getRepaymentBreakdown;
};
