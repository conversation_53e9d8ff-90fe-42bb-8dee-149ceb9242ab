"use client";

import {
  <PERSON><PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Flex,
} from "@chakra-ui/react";
import React from "react";
import { ChevronRightIcon } from "lucide-react";
import { useParams, usePathname } from "next/navigation";
import Link from "next/link";

const BreadCrumbs = ({
  children,
  showApplyButton,
}: {
  children?: React.ReactNode;
  showApplyButton: boolean;
}) => {
  const pathName = usePathname();
  const segments = pathName.split("/").filter(Boolean);

  const params = useParams();

  const basePath = params?.basePath;

  const newApplicationUrl = basePath
    ? `/${basePath}/application/new`
    : `/application/new`;

  const parsedSegment = basePath ? segments.slice(1) : segments.slice(0);
  return (
    <Flex
      bg='transparent'
      zIndex={2}
      w='100%'
      px={{ base: 2, md: 0 }}
      overflowY={{ base: "auto", md: "visible" }}
      css={{
        "&::-webkit-scrollbar": {
          display: "none",
        },
        "-ms-overflow-style": "none",
        "scrollbar-width": "none",
      }}
    >
      <Flex
        gap={2}
        flex={1}
        mx='auto'
        justifyContent='space-between'
        flexDir='row'
        alignItems='center'
      >
        <Breadcrumb
          separator={
            <Flex
              alignItems='center'
              color='gray.500'
            >
              <ChevronRightIcon
                width={18}
                height={18}
              />
            </Flex>
          }
        >
          {parsedSegment?.map((segment, index, arr) => {
            const isCurrentPage = index === arr.length - 1;
            const href = `/${segments.slice(0, index + 2).join("/")}`;

            return (
              <BreadcrumbItem
                key={`${segment}-${index}`}
                isCurrentPage={isCurrentPage}
              >
                <BreadcrumbLink
                  as={isCurrentPage ? undefined : Link}
                  href={isCurrentPage ? undefined : href}
                  fontWeight='bold'
                  fontSize={{ base: "sm", md: "md" }}
                  textTransform='capitalize'
                  cursor='pointer'
                  _hover={{
                    textDecoration: isCurrentPage ? "none" : "underline",
                  }}
                  color={isCurrentPage ? "gray.800" : "gray.500"}
                  w='max-content'
                >
                  {segment.replace(/-/g, " ")}
                </BreadcrumbLink>
              </BreadcrumbItem>
            );
          })}
        </Breadcrumb>

        <Flex>{children}</Flex>
        {!showApplyButton ? (
          <Button
            as={Link}
            href={newApplicationUrl}
            px={6}
            aria-label='Apply for loan button'
            color='white'
            _hover={{
              textDecoration: "none",
            }}
            colorScheme='customPrimary'
            _visited={{
              color: "white",
            }}
          >
            <Flex
              gap={2}
              alignItems='center'
            >
              Apply for loan
            </Flex>
          </Button>
        ) : null}
      </Flex>
    </Flex>
  );
};

export default BreadCrumbs;
