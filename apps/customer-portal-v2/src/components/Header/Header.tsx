"use client";

import { messagingService } from "@/src/app/actions";
import { ClientInfo } from "@/__generated/graphql";
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  Container,
  Flex,
  GridItem,
  HStack,
  Stack,
  StackDivider,
  Text,
} from "@chakra-ui/react";
import * as LucideIcons from "lucide-react";
import { signOut } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useParams, usePathname } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";
import { NotificationCenter } from "../NotificationCenter";

interface HeaderProps {
  accessToken: string;
  clientInfo: ClientInfo;
}

const { House, Settings } = LucideIcons;

const Header = ({ accessToken, clientInfo }: HeaderProps) => {
  const router = useRouter();
  const params = useParams();
  const pathName = usePathname();
  const basePath = params?.basePath as string;

  const [hasPendingFeedback, setHasPendingFeedback] = useState(false);

  const isSubHeader =
    pathName?.includes("sign-up") ||
    pathName?.includes("forgot-password") ||
    pathName?.includes("reset-password") ||
    pathName === `/${basePath}` ||
    pathName === `/`;

  const { logoUrl } = clientInfo || {};

  const handleLogout = useCallback(async () => {
    await signOut({ redirect: false });
    localStorage.removeItem("otpRequested");
    localStorage.removeItem("phoneOtpRequested");
    const url = basePath ? `/${basePath}` : "/";
    router.push(url);
  }, [router, basePath]);

  const checkPendingFeedback = useCallback(async () => {
    try {
      const { pendingFeedback, pendingDocumentFeedback } =
        await messagingService();
      setHasPendingFeedback(!!(pendingFeedback || pendingDocumentFeedback));
    } catch (error) {
      setHasPendingFeedback(false);
    }
  }, [hasPendingFeedback]);

  useEffect(() => {
    checkPendingFeedback();
  }, [hasPendingFeedback]);

  const path = basePath ? `/${basePath}/notifications` : `/notifications`;

  const isNotification = pathName === path;

  const shouldShowBanner =
    hasPendingFeedback && !isNotification && accessToken && !isSubHeader;

  const homeUrl = basePath ? `/${basePath}` : "/";
  const dashBoardUrl = basePath ? `/${basePath}/dashboard` : `/dashboard`;
  const settingsUrl = basePath
    ? `/${basePath}/settings/profile`
    : `/settings/profile`;
  const notificationUrl = basePath
    ? `/${basePath}/notifications`
    : `/notifications`;

  return (
    <Stack>
      <GridItem
        as='header'
        bg='white'
        py={4}
        role='banner'
        aria-label='Main header'
      >
        <Container maxWidth='8xl'>
          <Flex
            justify='space-between'
            align='center'
          >
            <Link
              href={homeUrl}
              prefetch={false}
              data-testid='company-logo'
            >
              <Image
                src={logoUrl!}
                alt='Company logo'
                width={90}
                height={90}
                priority
              />
            </Link>

            {accessToken ? (
              <HStack
                divider={
                  <StackDivider
                    borderWidth='2px'
                    h='30px'
                    alignSelf='center'
                  />
                }
                spacing={4}
                align='center'
              >
                <NotificationCenter />
                <Button
                  onClick={handleLogout}
                  px={6}
                  aria-label='Logout button'
                >
                  Logout
                </Button>
              </HStack>
            ) : (
              <Button
                as={Link}
                href={homeUrl}
                prefetch={false}
                px={6}
                aria-label='Sign in button'
                _hover={{
                  textDecoration: "none",
                  bg: "customBrand.600",
                }}
                _visited={{
                  color: "white",
                }}
                data-testid='sign-in-button'
              >
                Sign In
              </Button>
            )}
          </Flex>
        </Container>
      </GridItem>

      {!isSubHeader && (
        <GridItem
          bg='customPrimary.500'
          py={4}
          role='banner'
          aria-label='Sub header'
        >
          <Container maxWidth='8xl'>
            <Flex
              justify='space-between'
              align='center'
              py={1}
            >
              <Stack
                direction='row'
                spacing={8}
              >
                <Button
                  as={Link}
                  variant='link'
                  href={dashBoardUrl}
                  aria-label='Dashboard button'
                  color='white'
                  _hover={{
                    textDecoration: "none",
                    opacity: 0.8,
                    color: "white",
                  }}
                  _visited={{
                    color: "white",
                  }}
                  data-testid='dashboard-button'
                >
                  <Flex
                    gap={2}
                    alignItems='center'
                  >
                    <House /> Home
                  </Flex>
                </Button>

                <Button
                  as={Link}
                  variant='link'
                  href={settingsUrl}
                  aria-label='Settings button'
                  color='white'
                  _hover={{
                    textDecoration: "none",
                    opacity: 0.8,
                    color: "white",
                  }}
                  _visited={{
                    color: "white",
                  }}
                  data-testid='settings-button'
                >
                  <Flex
                    gap={2}
                    alignItems='center'
                  >
                    <Settings /> Settings
                  </Flex>
                </Button>
              </Stack>
            </Flex>
          </Container>
        </GridItem>
      )}

      {shouldShowBanner && (
        <Box
          w='full'
          bg='#f2f2f2'
        >
          <Alert
            status='info'
            mb={4}
          >
            <AlertIcon />
            <AlertDescription
              whiteSpace='pre-wrap'
              wordBreak='break-word'
              fontWeight='semibold'
              w='full'
            >
              <Text as='span'>
                You have a message that requires your attention. Click the
                notification icon above or click{" "}
                <Button
                  as={Link}
                  variant='link'
                  href={notificationUrl}
                  prefetch={false}
                  px={0}
                  aria-label='notification link'
                  _hover={{
                    textDecoration: "underline",
                    transform: "none",
                  }}
                  _visited={{
                    color: "blue.500",
                  }}
                  data-testid='notification-link'
                  display='inline'
                  height='auto'
                  minW='auto'
                >
                  here
                </Button>{" "}
                to take action.
              </Text>
            </AlertDescription>
          </Alert>
        </Box>
      )}
    </Stack>
  );
};

export default Header;
