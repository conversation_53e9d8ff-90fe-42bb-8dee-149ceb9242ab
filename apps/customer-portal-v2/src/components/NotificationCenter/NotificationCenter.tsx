"use client";

import { useState, useEffect, useCallback } from "react";
import { Box, IconButton, Badge } from "@chakra-ui/react";
import { Bell } from "lucide-react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { messagingService } from "@/src/app/actions";

const NotificationCenter = () => {
  const [hasPendingFeedback, setHasPendingFeedback] = useState(false);
  const params = useParams();
  const basePath = params?.basePath as string;

  const checkPendingFeedback = useCallback(async () => {
    try {
      const { pendingFeedback, pendingDocumentFeedback } =
        await messagingService();
      setHasPendingFeedback(!!(pendingFeedback || pendingDocumentFeedback));
    } catch (error) {
      setHasPendingFeedback(false);
    }
  }, [hasPendingFeedback]);

  useEffect(() => {
    checkPendingFeedback();
  }, [hasPendingFeedback]);

  const shouldShowBadge = hasPendingFeedback;

  const notificationUrl = basePath
    ? `/${basePath}/notifications`
    : `/notifications`;

  return (
    <Box
      position='relative'
      p={0}
    >
      <Link
        href={notificationUrl}
        passHref
      >
        <IconButton
          aria-label='Notifications'
          icon={
            <Box position='relative'>
              <Bell size={20} />
              {shouldShowBadge && (
                <Badge
                  position='absolute'
                  top='-1'
                  right='2'
                  bg='red.500'
                  borderRadius='full'
                  w='3'
                  h='3'
                />
              )}
            </Box>
          }
          variant='unstyled'
          colorScheme={"customBrand"}
          p={1}
          mr={-4}
          _hover={{
            color: "customBrand.800",
          }}
        />
      </Link>
    </Box>
  );
};

export default NotificationCenter;
