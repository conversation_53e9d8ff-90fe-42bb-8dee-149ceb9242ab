"use client";

import { messagingService } from "@/src/app/actions";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { Notification } from "@/src/types/notifications";
import {
  HStack,
  Text,
  Button,
  Box,
  List,
  ListItem,
  ListIcon,
  VStack,
  Flex,
  IconButton,
} from "@chakra-ui/react";
import { ChevronLeft, Dot } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import UpdateLoanAmount from "./UpdateLoanAmount";
import UploadDocument from "./UploadDocument";

interface NotificationDetailProps {
  notification: Notification;
  onNotificationsChange: (notifications: Notification[]) => void;
  onBack?: () => void;
}
const NotificationDetail = ({
  notification,
  onBack,
}: NotificationDetailProps) => {
  const { setError } = useMessageTimer({
    duration: 7000,
  });

  const [pendingFeedback, setPendingFeedback] = useState<any>(null);
  const [application, setApplication] = useState<any>(null);

  const checkPendingFeedback = useCallback(async () => {
    try {
      const { pendingFeedback, application } = await messagingService();
      setPendingFeedback(pendingFeedback as any);
      setApplication(application);
    } catch (error: any) {
      setError(error?.message);
    }
  }, [setError]);
  useEffect(() => {
    checkPendingFeedback();
  }, [checkPendingFeedback]);

  const pendingDocumentFeedback =
    pendingFeedback &&
    pendingFeedback?.documentRequests?.length !== 0 &&
    pendingFeedback?.documentRequests.find(
      (document: { status: string }) => document?.status === "PENDING"
    );

  const pendingDocMessage = pendingDocumentFeedback?.message
    ? pendingDocumentFeedback?.message
    : "Your application requires the following documents for it to be complete.";
  const mandateMessage = () => {
    return (
      <>
        <Box>
          <Text fontSize={{ base: "md", md: "lg" }}>test</Text>
        </Box>

        <HStack
          mt='auto'
          pt={4}
          justifyContent='center'
          alignItems='center'
          w='full'
        >
          <Button
            colorScheme='customPrimary'
            w='fit-content'
          ></Button>
        </HStack>
      </>
    );
  };

  const documentRequestMessage = () => {
    return (
      <>
        <Flex
          flexDirection='column'
          gap={4}
        >
          <Text
            fontSize={{ base: "md", md: "lg" }}
            textTransform='capitalize'
          >
            Hi <strong>{notification?.customerName}</strong>,
          </Text>
          <Text fontSize={{ base: "md", md: "lg" }}>
            Your application requires additional information. This will improve
            your chances of getting a loan from us. Please upload a copy of your
            :
          </Text>
          {notification?.documentRequests?.map((data: any) => (
            <List key={data?.id}>
              <ListItem>
                <HStack spacing={1}>
                  <ListIcon
                    as={Dot}
                    color='gray.500'
                    boxSize={10}
                  />{" "}
                  <Text
                    fontSize={{ base: "md", md: "lg" }}
                    textTransform='capitalize'
                  >
                    {data?.title}
                  </Text>
                </HStack>
              </ListItem>
            </List>
          ))}
        </Flex>

        <Text fontSize={{ base: "md", md: "lg" }}>
          Please click the button below to upload the required documents.
        </Text>

        <HStack
          mt='auto'
          pt={4}
          justifyContent='center'
          alignItems='center'
          w='full'
        >
          <UploadDocument
            notification={notification}
            pendingDocMessage={pendingDocMessage}
          />
        </HStack>
      </>
    );
  };

  const loanModificationMessage = () => {
    return (
      <>
        <Flex
          flexDirection='column'
          gap={4}
        >
          <Text
            fontSize={{ base: "md", md: "lg" }}
            textTransform='capitalize'
          >
            Hi <strong>{notification?.customerName}</strong>,
          </Text>
          <Text fontSize={{ base: "md", md: "lg" }}>
            Your loan amount has been updated. Please click the button below to
            review your loan amount.
          </Text>
        </Flex>

        <HStack
          mt='auto'
          pt={4}
          justifyContent='center'
          alignItems='center'
          w='full'
        >
          <UpdateLoanAmount
            notification={notification}
            application={application}
          />
        </HStack>
      </>
    );
  };
  return (
    <VStack
      align='stretch'
      spacing={4}
      w={{ base: "full", xl: "54vw" }}
    >
      <HStack
        w='full'
        borderBottom='1px solid'
        borderColor={"gray.200"}
        px={{ base: 4, md: 6 }}
        py={4}
        alignItems='center'
        spacing={4}
      >
        {onBack && (
          <IconButton
            onClick={onBack}
            variant='ghost'
            color='black'
            bg='gray.100'
            icon={
              <ChevronLeft
                width={20}
                height={20}
              />
            }
            aria-label='Back to list'
          />
        )}

        <HStack
          justifyContent='space-between'
          w='full'
        >
          <Text
            fontSize={{ base: "lg", md: "xl" }}
            fontWeight='bold'
          >
            {notification.title}
          </Text>
        </HStack>
      </HStack>

      <VStack
        w='full'
        align='start'
        spacing={2}
        px={{ base: 4, md: 12 }}
        pb={6}
        bg='white'
      >
        {notification.type === "DOCUMENT_REQUEST"
          ? documentRequestMessage()
          : notification.type === "DETAILS_MODIFICATION"
            ? loanModificationMessage()
            : null}
      </VStack>
    </VStack>
  );
};

export default NotificationDetail;
