"use client";

import { formatAmount } from "@/utils/index";
import { RepaymentBreakdownPayload } from "@/__generated/graphql";
import {
  useDisclosure,
  Button,
  Table,
  Thead,
  Tbody,
  Tfoot,
  Tr,
  Th,
  Td,
  Box,
} from "@chakra-ui/react";
import { format } from "date-fns";
import React from "react";

const RepaymentBreakDown = ({
  repaymentBreakdown,
}: {
  repaymentBreakdown: RepaymentBreakdownPayload;
}) => {
  const { isOpen, onToggle } = useDisclosure();

  return (
    <Box w='100%'>
      <Button
        onClick={onToggle}
        w='100%'
        variant='link'
        colorScheme='customPrimary'
        _hover={{
          textDecoration: "underline",
          color: "customPrimary.600",
        }}
        disabled={!repaymentBreakdown}
        data-testid='repayment-breakdown-button'
      >
        View Repayment Details
      </Button>

      {isOpen && (
        <Box
          w='100%'
          maxW='100%'
          overflowX='auto'
          overflowY='auto'
          maxH='300px'
          border='1px solid'
          borderColor='customPrimary.100'
          borderRadius='md'
          mt={4}
        >
          <Table
            borderColor='customPrimary.100'
            size={{ base: "xs", md: "sm" }}
            variant='simple'
            w='100%'
            style={{ tableLayout: "fixed" }}
          >
            <Thead bg='white'>
              <Tr fontWeight='900'>
                <Th
                  borderColor='customPrimary.100'
                  w={{ base: "25%", md: "25%" }}
                  fontSize={{ base: "10px", md: "sm" }}
                  px={{ base: 1, md: 4 }}
                  py={{ base: 2, md: 3 }}
                >
                  Date
                </Th>
                <Th
                  borderColor='customPrimary.100'
                  w={{ base: "25%", md: "25%" }}
                  fontSize={{ base: "10px", md: "sm" }}
                  px={{ base: 1, md: 4 }}
                  py={{ base: 2, md: 3 }}
                >
                  Principal
                </Th>
                <Th
                  borderColor='customPrimary.100'
                  w={{ base: "25%", md: "25%" }}
                  fontSize={{ base: "10px", md: "sm" }}
                  px={{ base: 1, md: 4 }}
                  py={{ base: 2, md: 3 }}
                >
                  Interest
                </Th>
                <Th
                  borderColor='customPrimary.100'
                  w={{ base: "25%", md: "25%" }}
                  fontSize={{ base: "10px", md: "sm" }}
                  px={{ base: 1, md: 4 }}
                  py={{ base: 2, md: 3 }}
                >
                  Total
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {repaymentBreakdown?.repaymentBreakdown?.map((repayment, idx) => (
                <Tr
                  key={idx}
                  _odd={{
                    backgroundColor: "customPrimary.50",
                  }}
                >
                  <Td
                    borderColor='customPrimary.100'
                    fontSize={{ base: "9px", md: "sm" }}
                    px={{ base: 1, md: 4 }}
                    py={{ base: 1, md: 2 }}
                    overflow='hidden'
                    textOverflow='ellipsis'
                    whiteSpace='nowrap'
                  >
                    {repayment?.dueDate &&
                      format(new Date(repayment.dueDate), "MMM dd, yyyy")}
                  </Td>
                  <Td
                    borderColor='customPrimary.100'
                    fontSize={{ base: "9px", md: "sm" }}
                    px={{ base: 1, md: 4 }}
                    py={{ base: 1, md: 2 }}
                    overflow='hidden'
                    textOverflow='ellipsis'
                    whiteSpace='nowrap'
                  >
                    {formatAmount(repayment?.principalPortion || 0)}
                  </Td>
                  <Td
                    borderColor='customPrimary.100'
                    fontSize={{ base: "9px", md: "sm" }}
                    px={{ base: 1, md: 4 }}
                    py={{ base: 1, md: 2 }}
                    overflow='hidden'
                    textOverflow='ellipsis'
                    whiteSpace='nowrap'
                  >
                    {formatAmount(repayment?.interestPortion || 0)}
                  </Td>
                  <Td
                    borderColor='customPrimary.100'
                    fontSize={{ base: "9px", md: "sm" }}
                    px={{ base: 1, md: 4 }}
                    py={{ base: 1, md: 2 }}
                    overflow='hidden'
                    textOverflow='ellipsis'
                    whiteSpace='nowrap'
                  >
                    {formatAmount(repayment?.expectedPayment || 0)}
                  </Td>
                </Tr>
              ))}
            </Tbody>
            <Tfoot
              position='sticky'
              bottom={0}
              bg='white'
              zIndex={1}
            >
              <Tr fontWeight='600'>
                <Td
                  borderColor='customPrimary.100'
                  fontSize={{ base: "9px", md: "sm" }}
                  px={{ base: 1, md: 4 }}
                  py={{ base: 1, md: 2 }}
                >
                  Total
                </Td>
                <Td
                  borderColor='customPrimary.100'
                  fontSize={{ base: "9px", md: "sm" }}
                  px={{ base: 1, md: 4 }}
                  py={{ base: 1, md: 2 }}
                  overflow='hidden'
                  textOverflow='ellipsis'
                  whiteSpace='nowrap'
                >
                  {formatAmount(repaymentBreakdown?.principalAmount || 0)}
                </Td>
                <Td
                  borderColor='customPrimary.100'
                  fontSize={{ base: "9px", md: "sm" }}
                  px={{ base: 1, md: 4 }}
                  py={{ base: 1, md: 2 }}
                  overflow='hidden'
                  textOverflow='ellipsis'
                  whiteSpace='nowrap'
                >
                  {formatAmount(repaymentBreakdown?.totalInterestPortion || 0)}
                </Td>
                <Td
                  borderColor='customPrimary.100'
                  fontSize={{ base: "9px", md: "sm" }}
                  px={{ base: 1, md: 4 }}
                  py={{ base: 1, md: 2 }}
                  overflow='hidden'
                  textOverflow='ellipsis'
                  whiteSpace='nowrap'
                >
                  {formatAmount(repaymentBreakdown?.totalExpectedPayment || 0)}
                </Td>
              </Tr>
            </Tfoot>
          </Table>
        </Box>
      )}
    </Box>
  );
};

export default RepaymentBreakDown;
