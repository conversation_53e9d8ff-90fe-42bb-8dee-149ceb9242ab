"use client";

import React, { useCallback, useEffect, useState } from "react";
import {
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Button,
  Alert,
  AlertIcon,
  AlertDescription,
  VStack,
  useToast,
  Text,
  Box,
  Stack,
  Flex,
} from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import { Notification } from "@/src/types/notifications";
import { Application, RepaymentBreakdownPayload } from "@/__generated/graphql";
import { formatAmount } from "@/utils/index";
import { format } from "date-fns";
import {
  approveUpdatedLoanAmount,
  declineUpdatedLoanAmount,
  getRepaymentBreakdown,
} from "@/src/app/actions";
import RepaymentBreakDown from "./RepaymentBreakDown";

type Props = {
  notification?: Notification;
  application?: Application;
};

const UpdateLoanAmount = ({ notification, application }: Props) => {
  const toast = useToast();
  const router = useRouter();
  const { error, setError } = useMessageTimer({ duration: 7000 });
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [approvalLoading, setApprovalLoading] = useState(false);
  const [deniedLoading, setDeniedLoading] = useState(false);
  const [repaymentBreakdown, setRepaymentBreakdown] =
    useState<RepaymentBreakdownPayload>();

  const closeModal = useCallback(() => {
    setError("");
    onClose();
    router.refresh();
  }, [onClose, router, setError]);

  const handleUpdatedAmountApproval = useCallback(async () => {
    try {
      setError("");
      setApprovalLoading(true);
      const res = await approveUpdatedLoanAmount({
        applicationId: application?.id!,
        requestId: notification?.id!,
      });

      if (res?.error) {
        setError(res?.error);
        setApprovalLoading(false);
      }

      toast({
        title: "Updated Loan Amount Approved",
        description:
          "Updated loan amount approved successfully. We will review and disburse your loan shortly",
        status: "success",
        duration: 5000,
        isClosable: true,
        position: "top-right",
      });
      closeModal();
    } catch (error: any) {
      setError(error?.message);
      setApprovalLoading(false);
    }
  }, [
    application?.id,
    notification?.id,
    setError,
    setApprovalLoading,
    toast,
    closeModal,
  ]);
  const handleUpdatedAmountDecline = useCallback(async () => {
    try {
      setError("");
      setDeniedLoading(true);

      const res = await declineUpdatedLoanAmount({
        applicationId: application?.id!,
        requestId: notification?.id!,
      });

      if (res?.error) {
        setError(res?.error);
        setDeniedLoading(false);
      }

      toast({
        title: "Updated Loan Amount Declined",
        description: "Updated loan amount declined successfully",
        status: "success",
        duration: 5000,
        isClosable: true,
        position: "top-right",
      });
      closeModal();
    } catch (error: any) {
      setError(error?.message);
      setDeniedLoading(false);
    }
  }, [
    application?.id,
    notification?.id,
    setError,
    setDeniedLoading,
    toast,
    closeModal,
  ]);

  const breakdownData = useCallback(async () => {
    if (
      !application?.policy?.id ||
      !application?.amount ||
      !application?.loanDuration ||
      !application?.id
    ) {
      return;
    }

    const data = await getRepaymentBreakdown({
      policyId: application.policy.id,
      principalAmount: application.amount,
      duration: application.loanDuration,
      applicationId: application.id,
    });

    if (data) {
      setRepaymentBreakdown(data);
    }
  }, [
    application?.policy?.id,
    application?.amount,
    application?.loanDuration,
    application?.id,
  ]);

  useEffect(() => {
    breakdownData();
  }, [breakdownData]);

  return (
    <>
      <Button
        onClick={onOpen}
        colorScheme='customPrimary'
        disabled={notification?.status === "COMPLETED" || !application}
      >
        Update Loan Amount
      </Button>

      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        size='3xl'
        scrollBehavior='inside'
        closeOnOverlayClick={false}
      >
        <ModalOverlay />
        <ModalContent
          top='200%'
          transform='translate(-50%, -50%)'
          maxW={{ base: "95vw", md: "3xl" }}
          mx={{ base: 4, md: "auto" }}
        >
          <ModalHeader>Update Loan Amount</ModalHeader>
          <ModalBody
            px={4}
            pb={6}
            maxW='100%'
            overflow='hidden'
          >
            {error && (
              <Alert
                status='error'
                mb={4}
                borderRadius={6}
              >
                <AlertIcon />
                <AlertDescription
                  whiteSpace='pre-wrap'
                  wordBreak='break-word'
                >
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <VStack
              spacing={6}
              align='center'
              w='100%'
              maxW='100%'
            >
              <Stack
                spacing={4}
                align='center'
                w='100%'
                maxW='100%'
              >
                <Text
                  fontSize='md'
                  fontWeight='medium'
                  textAlign='center'
                >
                  Your loan has been updated. Please, review the loan details
                  below.
                </Text>

                <Box textAlign='center'>
                  <Text
                    fontSize='sm'
                    fontWeight='semibold'
                    color='gray.600'
                  >
                    New Loan Amount
                  </Text>
                  <Text
                    fontSize='xl'
                    fontWeight='bold'
                    color='green.600'
                  >
                    {formatAmount(application?.amount!)}
                  </Text>
                </Box>

                <Box textAlign='center'>
                  <Text
                    fontSize='sm'
                    fontWeight='semibold'
                    color='gray.600'
                  >
                    Next repayment date
                  </Text>
                  <Text
                    fontSize='lg'
                    fontWeight='medium'
                  >
                    {application?.dateOfRepayment &&
                      format(
                        new Date(application?.dateOfRepayment),
                        "MMM d, yyyy"
                      )}
                  </Text>
                </Box>

                <Text
                  fontSize='md'
                  color='gray.700'
                  textAlign='center'
                >
                  By choosing <strong>"I ACCEPT"</strong> below, your loan
                  amount will be changed to the current amount.
                </Text>

                <Box
                  w='100%'
                  maxW='100%'
                >
                  <RepaymentBreakDown
                    repaymentBreakdown={repaymentBreakdown!}
                  />
                </Box>

                <Flex
                  direction={{ base: "column", sm: "row" }}
                  gap={4}
                  justify='center'
                  mt={4}
                  w='full'
                >
                  <Button
                    colorScheme='green'
                    onClick={handleUpdatedAmountApproval}
                    flex={1}
                    w='full'
                    disabled={deniedLoading || approvalLoading}
                    isLoading={approvalLoading}
                    py={2}
                  >
                    I ACCEPT
                  </Button>
                  <Button
                    colorScheme='red'
                    variant='outline'
                    onClick={handleUpdatedAmountDecline}
                    flex={1}
                    w='full'
                    disabled={deniedLoading || approvalLoading}
                    isLoading={deniedLoading}
                    py={2}
                  >
                    Reject
                  </Button>
                </Flex>
              </Stack>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default UpdateLoanAmount;
