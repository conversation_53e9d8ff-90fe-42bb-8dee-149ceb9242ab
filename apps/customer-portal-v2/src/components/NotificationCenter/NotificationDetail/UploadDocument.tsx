"use client";

import React, { useCallback, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Button,
  useDisclosure,
  Alert,
  AlertIcon,
  AlertDescription,
  VStack,
  Text,
  useBreakpointValue,
  Box,
  VisuallyHidden,
  Input,
  Icon,
  useToast,
  FormLabel,
  FormControl,
  SimpleGrid,
} from "@chakra-ui/react";
import { UploadIcon } from "lucide-react";
import { bytesToSize, validateFile } from "@/utils/index";
import { useMessageTimer } from "@/src/hooks/useMessageTimer";
import {
  uploadSupportingDocument,
  uploadRequestedDocument,
} from "@/src/app/actions";
import { Notification } from "@/src/types/notifications";
import { useRouter } from "next/navigation";

type UploadDocumentForm = {
  [key: string]: FileList;
};

type Props = {
  notification?: Notification;
  pendingDocMessage?: string;
};

const MAX_SIZE = 10 * 1024 * 1024; // 10MB
const allowedTypes = [
  "image/png",
  "image/jpeg",
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

const UploadDocument: React.FC<Props> = ({
  notification,
  pendingDocMessage,
}) => {
  const toast = useToast();
  const router = useRouter();
  const { error, setError } = useMessageTimer({ duration: 7000 });
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [uploadedDocumentIds, setUploadedDocumentIds] = useState<
    Record<string, string>
  >({});
  const [fileDetails, setFileDetails] = useState<
    Record<string, { fileName: string; fileSize: string }>
  >({});
  const [uploadingFiles, setUploadingFiles] = useState<Record<string, boolean>>(
    {}
  );

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { isSubmitting, errors },
  } = useForm<UploadDocumentForm>();

  const isMobile = useBreakpointValue({ base: true, md: false });
  const docRequests = notification?.documentRequests ?? [];

  const handleFileChange = useCallback(
    async (fileList: FileList | null, id: string) => {
      const file = fileList?.[0];
      if (!file) return;

      const { isValid, error: validationError } = validateFile(
        file,
        MAX_SIZE,
        allowedTypes
      );
      if (!isValid) {
        setError(validationError!);
        return;
      }

      setError("");
      setFileDetails((prev) => ({
        ...prev,
        [id]: { fileName: file?.name, fileSize: bytesToSize(file?.size) },
      }));
      setUploadingFiles((prev) => ({ ...prev, [id]: true }));

      try {
        const arrayBuffer = await file?.arrayBuffer();
        const base64 = Buffer.from(arrayBuffer).toString("base64");
        const title =
          notification?.documentRequests?.find((data) => data?.id === id)
            ?.title ?? "";
        const { data, error: uploadError } = await uploadSupportingDocument({
          file: {
            name: file?.name,
            type: file?.type,
            size: file?.size,
            data: base64,
          },
          documentName: title,
        });

        if (uploadError || !data?.uploadSupportingDocument?.id)
          throw new Error(uploadError ?? "Upload failed");
        setUploadedDocumentIds((prev) => ({
          ...prev,
          [id]: data?.uploadSupportingDocument?.id,
        }));
      } catch (err: any) {
        setError(err?.message);
        setFileDetails((prev) => {
          const info = { ...prev };
          delete info[id];
          return info;
        });
        setValue(id, {} as FileList);
      } finally {
        setUploadingFiles((prev) => ({ ...prev, [id]: false }));
      }
    },
    [
      notification?.documentRequests,
      setError,
      setFileDetails,
      setUploadingFiles,
      setUploadedDocumentIds,
      setValue,
    ]
  );

  const closeModal = useCallback(() => {
    reset();
    setError("");
    onClose();
    router.refresh();
  }, [reset, setError, onClose, router]);

  const onSubmit = useCallback(async () => {
    setError("");
    try {
      const missing = docRequests?.filter((d) => !uploadedDocumentIds?.[d?.id]);
      if (missing.length)
        throw new Error("Please upload all required documents");

      const requestedDocuments = docRequests.map((data) => ({
        documentRequestId: data?.id,
        uploadedDocumentId: uploadedDocumentIds?.[data?.id],
      }));
      const { error: reqErr } = await uploadRequestedDocument({
        input: { requestId: notification?.id ?? "", requestedDocuments },
      });
      if (reqErr) throw new Error(reqErr);

      toast({
        title: "Document Upload",
        description: "Documents submitted successfully",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top-right",
      });
      closeModal();
    } catch (err: any) {
      setError(err?.message);
    }
  }, [
    closeModal,
    docRequests,
    notification?.id,
    setError,
    toast,
    uploadRequestedDocument,
    uploadedDocumentIds,
  ]);
  const columns = useBreakpointValue({
    base: 1,
    md: docRequests.length > 1 ? 2 : 1,
  });
  const allUploaded = docRequests?.every((d) =>
    Boolean(uploadedDocumentIds?.[d?.id])
  );
  const anyUploading = Object.values(uploadingFiles).some(Boolean);

  return (
    <>
      <Button
        onClick={onOpen}
        colorScheme='customPrimary'
        disabled={notification?.status === "COMPLETED"}
      >
        Upload Documents
      </Button>
      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        size='xl'
        scrollBehavior='inside'
        closeOnOverlayClick={false}
      >
        <ModalOverlay />
        <ModalContent
          top='30%'
          transform='translate(-50%, -50%)'
        >
          <ModalHeader>Upload Documents</ModalHeader>
          <ModalBody
            px={4}
            pb={6}
          >
            {error && (
              <Alert
                status='error'
                mb={4}
                borderRadius={6}
              >
                <AlertIcon />
                <AlertDescription
                  whiteSpace='pre-wrap'
                  wordBreak='break-word'
                >
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <VStack
              spacing={6}
              align='stretch'
            >
              <Text textAlign='center'>{pendingDocMessage}</Text>
              <Text
                textAlign='center'
                color='gray.600'
              >
                {notification?.comment || ""}
              </Text>
              <form onSubmit={handleSubmit(onSubmit)}>
                <SimpleGrid
                  columns={columns}
                  spacing={4}
                >
                  {docRequests.map((doc) => (
                    <FormControl
                      key={doc?.id}
                      isInvalid={!!errors?.[doc?.id]}
                    >
                      <FormLabel
                        fontWeight='semibold'
                        textTransform='capitalize'
                      >
                        {doc?.title}
                        {uploadedDocumentIds?.[doc?.id] && (
                          <Text
                            as='span'
                            color='green.500'
                            ml={2}
                            fontSize='sm'
                          >
                            ✓
                          </Text>
                        )}
                      </FormLabel>
                      <Box
                        p={4}
                        borderWidth='2px'
                        borderStyle={
                          uploadedDocumentIds?.[doc?.id] ? "solid" : "dashed"
                        }
                        borderColor={
                          uploadedDocumentIds?.[doc?.id]
                            ? "green.300"
                            : uploadingFiles?.[doc?.id]
                              ? "blue.300"
                              : "gray.300"
                        }
                        borderRadius='md'
                        cursor={
                          uploadingFiles?.[doc?.id] ? "not-allowed" : "pointer"
                        }
                        onClick={() =>
                          !uploadingFiles?.[doc?.id] &&
                          document.getElementById(`file-${doc?.id}`)?.click()
                        }
                      >
                        <VisuallyHidden>
                          <Input
                            id={`file-${doc?.id}`}
                            type='file'
                            accept={allowedTypes?.join(",")}
                            {...register(doc?.id)}
                            disabled={uploadingFiles[doc?.id]}
                            onChange={(e) =>
                              handleFileChange(e.target.files, doc?.id)
                            }
                          />
                        </VisuallyHidden>
                        <VStack
                          spacing={2}
                          textAlign='center'
                        >
                          <Icon
                            as={UploadIcon}
                            boxSize={6}
                            color={
                              uploadedDocumentIds?.[doc?.id]
                                ? "green.500"
                                : uploadingFiles?.[doc?.id]
                                  ? "blue.500"
                                  : "gray.400"
                            }
                          />
                          <Text
                            fontSize={isMobile ? "sm" : "md"}
                            noOfLines={2}
                            color={
                              uploadedDocumentIds?.[doc?.id]
                                ? "green.700"
                                : "gray.700"
                            }
                          >
                            {uploadingFiles?.[doc?.id]
                              ? "Uploading..."
                              : fileDetails[doc?.id]
                                ? `${fileDetails[doc?.id]?.fileName} (${fileDetails[doc?.id]?.fileSize})`
                                : `Upload ${doc?.title}`}
                          </Text>
                          {isMobile &&
                            !fileDetails?.[doc?.id] &&
                            !uploadingFiles?.[doc?.id] && (
                              <Text
                                fontSize='xs'
                                color='gray.500'
                              >
                                Tap to select file
                              </Text>
                            )}
                        </VStack>
                      </Box>
                    </FormControl>
                  ))}
                </SimpleGrid>
                <Button
                  type='submit'
                  colorScheme='customPrimary'
                  size='lg'
                  mt={6}
                  width='full'
                  isLoading={isSubmitting}
                  isDisabled={
                    !docRequests.length || !allUploaded || anyUploading
                  }
                >
                  Submit Documents
                </Button>
              </form>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default UploadDocument;
