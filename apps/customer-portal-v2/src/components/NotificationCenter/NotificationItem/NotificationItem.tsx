"use client";

import { HStack, VStack, Box, Text } from "@chakra-ui/react";
import { format } from "date-fns";
import { Notification } from "@/src/types/notifications";

interface NotificationItemProps {
  notification: Notification;
  isSelected: boolean;
  onSelect: () => void;
}

const NotificationItem = ({
  notification,
  isSelected,
  onSelect,
}: NotificationItemProps) => {
  const bgColor = isSelected ? "gray.200" : "white";
  const hoverBg = "gray.50";
  const date = format(new Date(notification.date), "MMM dd, yyyy");

  return (
    <Box
      borderBottom='1px solid'
      borderColor='gray.200'
      py={2}
      _last={{
        borderBottom: "none",
      }}
    >
      <HStack
        spacing={3}
        alignItems='flex-start'
        bg={bgColor}
        _hover={{ bg: isSelected ? bgColor : hoverBg }}
        cursor='pointer'
        onClick={onSelect}
        p={4}
        borderRadius={6}
      >
        <VStack
          align='start'
          spacing={1}
          flex='1'
        >
          <HStack>
            <Text fontWeight={notification.read ? "normal" : "bold"}>
              {notification?.title}
            </Text>
          </HStack>
          <Text
            fontSize='sm'
            color='gray.500'
          >
            {date}
          </Text>
        </VStack>
      </HStack>
    </Box>
  );
};

export default NotificationItem;
