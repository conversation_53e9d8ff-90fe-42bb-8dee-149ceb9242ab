"use client";

import {
  VStack,
  Input,
  Text,
  Box,
  Heading,
  Skeleton,
  SkeletonText,
  Button,
} from "@chakra-ui/react";
import { useState, useMemo, useEffect } from "react";
import { Search } from "lucide-react";
import { NotificationItem } from "../NotificationItem";
import { Notification } from "@/src/types/notifications";
import useDebounce from "@/src/hooks/useDebounce";

interface NotificationListProps {
  notifications: Notification[];
  selectedId: string | null;
  onSelect: (notification: Notification) => void;
  onNotificationsChange: (notifications: Notification[]) => void;
}

const NotificationList = ({
  notifications,
  selectedId,
  onSelect,
}: NotificationListProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  const trimmedSearchTerm = searchTerm.trim();
  const debouncedSearchTerm = useDebounce(trimmedSearchTerm, 500);

  const filteredNotifications = useMemo(() => {
    return notifications
      .filter((n) =>
        n.title.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      )
      .sort((a, b) =>
        a.read === b.read
          ? new Date(b.date).getTime() - new Date(a.date).getTime()
          : a.read
            ? 1
            : -1
      );
  }, [notifications, debouncedSearchTerm]);

  useEffect(() => {
    if (trimmedSearchTerm !== debouncedSearchTerm) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  }, [trimmedSearchTerm, debouncedSearchTerm]);

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const renderSkeletonItems = () => (
    <>
      <Box
        p={4}
        borderBottom='1px'
        borderColor='gray.200'
      >
        <Skeleton
          mb={2}
          h={8}
        />
        <SkeletonText
          h={6}
          noOfLines={1}
          width='50%'
        />
      </Box>
    </>
  );

  const renderEmptyState = () => (
    <Box
      p={8}
      textAlign='center'
      display='flex'
      flexDirection='column'
      alignItems='center'
      gap={3}
    >
      <Search
        size={48}
        color='gray'
      />
      <Text
        color='gray.500'
        fontSize='lg'
      >
        {trimmedSearchTerm
          ? "No notifications found"
          : "No notifications available"}
      </Text>
      {trimmedSearchTerm && (
        <Text
          color='gray.400'
          fontSize='sm'
        >
          Try adjusting your search terms
        </Text>
      )}
    </Box>
  );
  return (
    <VStack
      align='stretch'
      h='full'
      w='full'
    >
      <Box
        px={4}
        mt={2}
      >
        <Heading
          size='md'
          my={4}
        >
          In-App Notifications
        </Heading>
        <Input
          placeholder='Search notifications...'
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          mb={3}
        />
        {trimmedSearchTerm && (
          <Button
            size='sm'
            colorScheme='red'
            variant='link'
            onClick={handleClearSearch}
            mb={3}
            _hover={{
              textDecoration: "none",
            }}
          >
            Clear Search
          </Button>
        )}
      </Box>

      <VStack
        align='stretch'
        spacing={0}
        overflowY='auto'
        flex='1'
        bg='white'
        px={4}
      >
        {isSearching
          ? renderSkeletonItems()
          : filteredNotifications.length === 0
            ? renderEmptyState()
            : filteredNotifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  isSelected={selectedId === notification?.id}
                  onSelect={() => onSelect(notification)}
                />
              ))}
      </VStack>
    </VStack>
  );
};

export default NotificationList;
