"use client";

import { useState, useEffect, useMemo, useRef } from "react";
import {
  Box,
  useBreakpointValue,
  Skeleton,
  Stack,
  VStack,
  Flex,
  Text,
} from "@chakra-ui/react";
import { Notification } from "@/src/types/notifications";
import { NotificationList } from "../NotificationList";
import { NotificationDetail } from "../NotificationDetail";
import { FileX } from "lucide-react";

interface NotificationPanelProps {
  initialNotifications: Notification[];
}

export default function NotificationPanel({
  initialNotifications,
}: NotificationPanelProps) {
  const [notifications, setNotifications] = useState(initialNotifications);
  const isMobile = useBreakpointValue({ base: true, lg: false });
  const [selectedNotification, setSelectedNotification] =
    useState<Notification | null>(null);
  const hasSetInitialDesktopSelection = useRef(false);

  useEffect(() => {
    if (
      !isMobile &&
      !hasSetInitialDesktopSelection.current &&
      notifications.length > 0
    ) {
      setSelectedNotification(notifications[0]);
      hasSetInitialDesktopSelection.current = true;
    }
  }, [isMobile, notifications]);

  const handleSelectNotification = (notification: Notification) => {
    setSelectedNotification(notification);
  };

  const updateNotifications = (newNotifications: Notification[]) => {
    setNotifications(newNotifications);

    if (selectedNotification) {
      const updated = newNotifications.find(
        (n) => n.id === selectedNotification.id
      );
      if (updated) setSelectedNotification(updated);
    }

    window.dispatchEvent(new Event("notifications-updated"));
  };

  const emptyState = useMemo(
    () => (
      <Flex
        p={8}
        gap={4}
        flex={1}
        flexDir='column'
        alignItems='center'
        justifyContent='center'
        color='gray.400'
      >
        <FileX
          width='60px'
          height='60px'
        />
        <Text
          fontSize='lg'
          textAlign='center'
          color='gray.600'
        >
          You currently have no notifications.
        </Text>
      </Flex>
    ),
    []
  );

  const skeletonState = useMemo(
    () => (
      <Box
        p={8}
        textAlign='center'
      >
        <Skeleton
          height='20px'
          mb={4}
        />
        <Skeleton height='100px' />
      </Box>
    ),
    []
  );

  // Mobile view: show either list or detail, not both
  if (isMobile) {
    return (
      <Box w='full'>
        {selectedNotification ? (
          <VStack
            overflow='auto'
            w='full'
            bg='white'
            borderRadius={6}
            maxH='700px'
          >
            <NotificationDetail
              notification={selectedNotification}
              onNotificationsChange={updateNotifications}
              onBack={() => setSelectedNotification(null)}
            />
          </VStack>
        ) : (
          <VStack
            overflow='auto'
            w='full'
            bg='white'
            borderRadius={6}
            maxH='700px'
          >
            <NotificationList
              notifications={notifications}
              selectedId=''
              onSelect={handleSelectNotification}
              onNotificationsChange={updateNotifications}
            />
          </VStack>
        )}
      </Box>
    );
  }

  // Desktop view: show both panels side by side
  return (
    <Stack
      spacing={8}
      w='full'
      direction='row'
    >
      <VStack
        overflow='auto'
        w='40%'
        bg='white'
        borderRadius={6}
        maxH='700px'
      >
        <NotificationList
          notifications={notifications}
          selectedId={selectedNotification?.id || ""}
          onSelect={handleSelectNotification}
          onNotificationsChange={updateNotifications}
        />
      </VStack>

      <VStack
        overflow='auto'
        w='100%'
        bg='white'
        borderRadius={6}
        h='100%'
      >
        {notifications.length > 0 ? (
          selectedNotification ? (
            <NotificationDetail
              notification={selectedNotification}
              onNotificationsChange={updateNotifications}
            />
          ) : (
            skeletonState
          )
        ) : (
          emptyState
        )}
      </VStack>
    </Stack>
  );
}
