import { LoanCategoryAttribute, Maybe } from "@/__generated/graphql";
import chroma from "chroma-js";
import { format } from "date-fns";

export const formatNumber = (number: number) => {
  return Intl.NumberFormat().format(number);
};

export const formatAmount = (number: number) => {
  return Intl.NumberFormat("en-NG", {
    style: "currency",
    currency: "NGN",
  }).format(number);
};

export const removeCommas = (string: string): string => {
  const hasComma = /,/;

  return hasComma.test(string) ? string.replace(/,/g, "") : string;
};

export const parseNumericValue = (value: string | number): number => {
  if (!value) return value as number;

  return parseFloat(removeCommas(value.toString()));
};

export const formatLoanDuration = (duration: string) => {
  switch (duration) {
    case "1 days":
      return "1 day";
    case "1 weeks":
      return "1 week";
    case "1 months":
      return "1 month";
    case "1 years":
      return "1 year";
    default:
      return duration;
  }
};

export const maskPhoneNumber = (phone: string) =>
  phone?.replace(phone.substring(5, 9), "****");

export const bytesToSize = (bytes: number) => {
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  if (bytes === 0) return "n/a";

  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  if (i === 0) return `${bytes} ${sizes[i]}`;

  return `${(bytes / 1024 ** i).toFixed(0)} ${sizes[i]}`;
};

export const isAbsolutePath = (url: string) => {
  if (url?.includes("http")) {
    return url;
  } else {
    return `//${url}`;
  }
};

export const normalizePath = (path: string) => path?.replace(/\/+$/, "");

export const getAttributrFromProduct = ({
  loanCategoryAttributes,
  namesSet,
}: {
  loanCategoryAttributes: Maybe<LoanCategoryAttribute>[];
  namesSet: Set<string>;
}) => {
  const result = {};
  loanCategoryAttributes?.forEach((item) => {
    const attrName = item?.attribute?.name!;
    if (namesSet.has(attrName)) {
      const rawValue = item?.value;
      // Handle null values
      if (rawValue === null) {
        (result as Record<string, any>)[attrName] = null;
      }
      // Handle boolean strings
      else if (rawValue === "true") {
        (result as Record<string, any>)[attrName] = true;
      } else if (rawValue === "false") {
        (result as Record<string, any>)[attrName] = false;
      }
      // Handle numeric strings
      else if (
        rawValue !== undefined &&
        !isNaN(Number(rawValue)) &&
        rawValue !== ""
      ) {
        (result as Record<string, any>)[attrName] = Number(rawValue);
      }
      // Keep other values as is
      else {
        (result as Record<string, any>)[attrName] = rawValue;
      }
    }
  });
  return result;
};

export const generateChakraScale = (baseColor: string) => {
  if (!baseColor) return {};

  const steps = [50, 100, 200, 300, 400, 500, 600, 700, 800, 900];

  const lightest = chroma?.mix(baseColor, "#ffffff", 0.9, "lab");
  const darkest = chroma?.mix(baseColor, "#000000", 0.9, "lab");

  if (!lightest || !darkest) return {};

  // Create a scale with explicit domain to position baseColor at 5/9 (500 position)
  const scale = chroma

    ?.scale([lightest, baseColor, darkest])
    ?.domain([0, 5 / 9, 1]) // Key change: positions baseColor at 5/9
    ?.mode("lab")
    ?.colors(steps.length);

  if (!scale) return {};

  // Map each step to the corresponding shade key
  const result = {};

  steps?.forEach((shade, i) => {
    if (shade != null && scale[i] != null) {
      (result as Record<number, string>)[shade] = scale[i];
    }
  });
  return result;
};

export const camelize = (text: string) => {
  return text.replace(/^([A-Z])|[\s-_]+(\w)/g, (match, p1, p2) => {
    if (p2) return p2.toUpperCase();
    return p1.toLowerCase();
  });
};

export const formatDate = ({
  date,
  dateFormat = "dd-MMMM-yyyy",
}: {
  date: Date | string | undefined;
  dateFormat?: string | undefined;
}) => {
  return date ? format(date, dateFormat) : "N/A";
};

export const removeEmptyObjects = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(removeEmptyObjects);
  }

  if (typeof obj === "object" && obj !== null) {
    if (obj?.name && obj?.fileDetails) {
      return obj;
    }

    const cleanedObj: any = {};
    for (const key in obj) {
      const value = obj[key];

      if (
        typeof value === "object" &&
        value !== null &&
        !(Array.isArray(value) && key === "files")
      ) {
        const cleanedValue = removeEmptyObjects(value);
        if (
          (typeof cleanedValue === "object" &&
            Object.keys(cleanedValue).length > 0) ||
          Array.isArray(cleanedValue)
        ) {
          cleanedObj[key] = cleanedValue;
        }
      } else if (
        value !== null &&
        value !== "" &&
        value !== undefined &&
        !(Array.isArray(value) && key === "files")
      ) {
        cleanedObj[key] = value;
      } else if (Array.isArray(value) && key === "files") {
        cleanedObj[key] = value;
      }
    }

    return cleanedObj;
  }

  return obj;
};

export const validateFile = (
  file: File,
  maxSize: number,
  allowedTypes: any
) => {
  if (!file) {
    return { isValid: false, error: "No file selected" };
  }

  if (file.size > maxSize) {
    return { isValid: false, error: "File size must be less than 10MB" };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error:
        "Invalid file type. Please upload PNG, JPEG, PDF, DOC, or DOCX files only",
    };
  }

  return { isValid: true, error: null };
};
