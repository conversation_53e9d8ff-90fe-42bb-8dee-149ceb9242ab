import { useApplicationDetailsContext } from '@/routes/applications/routes/details/context';
import { useGetRepaymentBreakdown } from '@/services/repayment-breakdown';
import { formatDate } from '@/utils/formatNumber';
import { processGraphQLError } from '@/utils/processGraphQLError';
import { sumColumn } from '@/utils/sumColumn';
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Button,
  Flex,
  Stack,
  useDisclosure,
} from '@chakra-ui/react';
import take from 'lodash/take';
import React from 'react';
import {
  RepaymentBreakdownItemSkeletonGroup,
  RepaymentDetailsWrapper,
} from '../../RepaymentBreakdown';
import { RepaymentBreakdownItem } from '../RepaymentBreakdownItem';

const GetRepaymentBreakdown = () => {
  const { application } = useApplicationDetailsContext();
  const { policy, amount, id, loanDuration, moratoriumData, portfolio } =
    application || {};
  const {
    repaymentBreakdowns,
    repaymentBreakdownsError,
    repaymentBreakdownsLoading,
  } = useGetRepaymentBreakdown({
    applicationId: id,
    policyId: policy?.id,
    duration: loanDuration,
    principalAmount: amount,
    moratoriumPeriod: moratoriumData?.numberOfPeriods,
  });

  const { repaymentBreakdown } = repaymentBreakdowns || {};

  const { isOpen, onToggle } = useDisclosure();

  const totalRepayments = repaymentBreakdown?.length;
  const repaymentsToShow = 6;

  const portfolioRepayments = portfolio?.repayments
    .filter(repayment => ['PENDING', 'PAID'].includes(repayment?.status?.name))
    .map(repayment => {
      return {
        principalPortion: repayment.principalPortion,
        dueDate: repayment.dueDate,
        interestPortion: repayment.interestPortion,
        expectedPayment: repayment.totalPayment,
      };
    });

  const displayedRepayments = take(
    repaymentBreakdown,
    isOpen ? totalRepayments : repaymentsToShow,
  );

  const portfolioRepaymentsBreakdown = take(
    portfolioRepayments,
    isOpen ? portfolioRepayments?.length : repaymentsToShow,
  );

  return (
    <Stack>
      {!!repaymentBreakdownsError && (
        <Alert status="error">
          <AlertIcon />
          <AlertDescription>
            {processGraphQLError(repaymentBreakdownsError)}
          </AlertDescription>
        </Alert>
      )}

      {!!repaymentBreakdowns && (
        <RepaymentDetailsWrapper>
          {(portfolioRepaymentsBreakdown.length > 0
            ? portfolioRepaymentsBreakdown
            : displayedRepayments
          )?.map(breakdown => (
            <RepaymentBreakdownItem
              key={breakdown.dueDate}
              totalPayment={breakdown.expectedPayment}
              interestPortion={breakdown.interestPortion}
              principalPortion={breakdown.principalPortion}
              repaymentDate={formatDate(breakdown.dueDate, true)}
            />
          ))}

          {(portfolioRepayments?.length || totalRepayments) >
            repaymentsToShow && (
            <Flex justifyContent="center">
              <Button
                size="sm"
                variant="ghost"
                onClick={onToggle}
                _focus={{}}
                _focusVisible={{
                  boxShadow: 'outline',
                }}
              >
                {portfolioRepayments?.length ===
                  portfolioRepaymentsBreakdown?.length ||
                totalRepayments === displayedRepayments?.length
                  ? 'Show fewer repayments'
                  : `Show all ${portfolioRepayments?.length || totalRepayments} repayments`}
              </Button>
            </Flex>
          )}
          <RepaymentBreakdownItem
            isTotalRow
            principalPortion={amount}
            totalPayment={sumColumn(
              repaymentBreakdowns?.repaymentBreakdown,
              'expectedPayment',
            )}
            interestPortion={sumColumn(
              repaymentBreakdowns?.repaymentBreakdown,
              'interestPortion',
            )}
          />
        </RepaymentDetailsWrapper>
      )}

      {repaymentBreakdownsLoading && <RepaymentBreakdownItemSkeletonGroup />}
    </Stack>
  );
};

export default GetRepaymentBreakdown;
