import { Input, NumericInput, SelectBank } from '@/components/forms';
import { useMakeManualRepayment } from '@/services/loans';
import {
  formatAmount,
  formatDate,
  parseNumericValue,
} from '@/utils/formatNumber';
import { processGraphQLError } from '@/utils/processGraphQLError';
import { MakeManualRepaymentInput } from '@/__generated/graphql';
import {
  Alert,
  AlertDescription,
  AlertIcon,
  Box,
  Button,
  ButtonGroup,
  ModalBody,
  ModalFooter,
  Stack,
  UseDisclosureProps,
  useToast,
} from '@chakra-ui/react';
import startCase from 'lodash/startCase';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import currency from 'currency.js';
import { SelectedLoanType } from '../../CashRepaymentModal';

type MakeRepaymentFormProps = UseDisclosureProps & {
  selectedLoan: SelectedLoanType;
  backToPreviousPage: () => void;
};

const MakeRepaymentForm = ({
  selectedLoan,
  backToPreviousPage,
  onClose,
}: MakeRepaymentFormProps) => {
  const toast = useToast();

  const {
    makeManualRepayment,
    makeManualRepaymentError,
    makeManualRepaymentLoading,
    makeManualRepaymentResponse,
  } = useMakeManualRepayment();
  const { reset, register, formState, control, handleSubmit } =
    useForm<MakeManualRepaymentInput>();

  const { errors } = formState;
  const outstandingAmount = currency(selectedLoan.fullAmount).subtract(
    currency(selectedLoan.amountPaid),
  ).value;

  const closeModal = () => {
    reset();
    onClose();
  };

  const onSubmit = (args: MakeManualRepaymentInput) => {
    makeManualRepayment({
      ...args,
      amount: parseNumericValue(args.amount),
      portfolioId: selectedLoan.id,
    });
  };

  useEffect(() => {
    if (makeManualRepaymentResponse?.success) {
      toast({
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top-right',
        title: startCase('Cash repayment successful'),
      });

      closeModal();
    }
  }, [makeManualRepaymentResponse]);

  const lastRepaymentAmount = selectedLoan?.transactions?.nodes[0]?.amount;
  const lastRepaymentDate = selectedLoan?.transactions?.nodes[0]?.createdAt;

  const validateMaxAmount = (value: number) => {
    const parsedValue = parseNumericValue(value);
    if (parsedValue > outstandingAmount) {
      return `Amount should not be more than ${formatAmount(
        outstandingAmount,
      )}`;
    }

    return true;
  };

  return (
    <Box as="form" onSubmit={handleSubmit(onSubmit as any)}>
      <ModalBody py={6}>
        <Stack spacing={6}>
          {makeManualRepaymentError &&
            !makeManualRepaymentResponse?.success && (
              <Alert status="error" p={2}>
                <AlertIcon />
                <AlertDescription>
                  {processGraphQLError(makeManualRepaymentError)}
                </AlertDescription>
              </Alert>
            )}

          <Controller
            control={control}
            name="amount"
            rules={{
              validate: {
                custom: value => validateMaxAmount(value),
              },
              required: 'Amount is required',
            }}
            render={({ field: { onChange, onBlur, name, value } }) => (
              <NumericInput
                type="tel"
                name={name}
                value={value}
                onBlur={onBlur}
                onChange={onChange}
                label="Payment amount"
                thousandSeparator={true}
                allowLeadingZeros={false}
                errorMessage={errors?.amount?.message}
                helperText={
                  lastRepaymentAmount &&
                  `Last repayment:
                ${formatAmount(lastRepaymentAmount)}
                ${formatDate(lastRepaymentDate)}
                `
                }
              />
            )}
          />

          <Controller
            name="bankId"
            control={control}
            rules={{
              required: 'Please select a bank',
            }}
            render={({ field: { onChange, name, value } }) => (
              <SelectBank
                name={name}
                value={value}
                onChange={onChange}
                label="Receiving bank"
                placeholder="-- Select bank --"
                errorMessage={errors?.bankId?.message}
              />
            )}
          />

          <Controller
            control={control}
            name="accountNumber"
            rules={{
              minLength: {
                value: 10,
                message: 'Account number must be 10',
              },
              maxLength: {
                value: 10,
                message: 'Account number must be 10',
              },
              required: 'Account number is required',
            }}
            render={({ field: { onChange, onBlur, name, value } }) => (
              <NumericInput
                type="tel"
                name={name}
                value={value}
                maxLength={10}
                onBlur={onBlur}
                allowLeadingZeros
                onChange={onChange}
                label="Account number"
                thousandSeparator={false}
                errorMessage={errors?.accountNumber?.message}
              />
            )}
          />

          <Input
            label="Reference"
            errorMessage={errors?.reference?.message}
            {...register('reference', {
              required: 'Please enter a reference',
              minLength: {
                value: 3,
                message: 'Reference must be at least 3 characters',
              },
            })}
          />
        </Stack>
      </ModalBody>

      <ModalFooter>
        <ButtonGroup spacing={4} flex={1}>
          <Button
            flex={2}
            variant="outline"
            colorScheme="gray"
            onClick={backToPreviousPage}
            isDisabled={makeManualRepaymentLoading}
            _focus={{}}
            _focusVisible={{
              boxShadow: 'outline',
            }}
          >
            Back
          </Button>

          <Button
            flex={3}
            type="submit"
            isLoading={makeManualRepaymentLoading}
            _focus={{}}
            _focusVisible={{
              boxShadow: 'outline',
            }}
          >
            Make Repayment
          </Button>
        </ButtonGroup>
      </ModalFooter>
    </Box>
  );
};
export default MakeRepaymentForm;
