@loans
Feature: My loans
    This feature is to validate details on my loans

  Background: Precondition
    Given I visit "/"

  @cpSanity @cpRegression @e2e
  Scenario: View details
    When I click on link "Apply for loan"
    Then I see text "Select a Loan Product"
    And I get element by selector "[data-testid='search-input']"
    And I type "<PERSON>"
    And I get element by selector ".chakra-radio"
    And I click
    And I get element by selector "[data-testid='loan-amount-input']"
    And I type "5000"
    And I click on button "Continue"
    Then I see text "What tenor would you prefer?"
    And I get element by selector "[data-testid='loan-tenor-select']"
    And I select "2 days"
    And I click on button "Proceed"
    # Then I do not see text "Email Validation"
    # And I get element by selector "[name='enterEmail']"
    # And I type random "Lorem Word"
    # And I click on button "Next"
    # And I get element by selector "[name='enterEmail']"
    # And I type random "Lorem Word"
    # And I click on button "Next"
    And I wait 5 seconds
    # And I get element by selector "#field-1"
    # And I type random "Lorem Word"
    # And I get element by selector "#field-2"
    # And I click
    # And I upload file "free-images.jpg"
    And I click on button "Next"
    Then I see text "Add Bank Information"
    And I click on button "Continue"
    Then I see text "Enter the Ticket Number and OTP/Password sent by your bank. (Check Email/SMS)"
    And I get element by selector "[data-testid='mbs-ticket-num-input']"
    And I type "*****************"
    And I get element by selector "[data-testid='password-input']"
    And I type "1234"
    And I click on button "Proceed"
    Then I see text "Confirm your loan application"
    And I get element by selector "[data-testid='confirm-application-policy-text']"
    And I click
    And I click on button "Complete Application"
  # @cpSanity @cpRegression @e2e
  # Scenario: View Repayment Breakdown
  #   When I click on text "View Details"
    # Then I should view details of repayment breakdown
# Scenario: Complete Application for a Pending Loan
#     When I click on Complete Application button
#     And I upload account statement pdf
#     Then I should view details of repayment breakdown
# Scenario: CREATE Loan: Verify automatic deny to loan
#     When I click on create loan button
#     And I apply for a loan that does not meet acceptable criteria
#     Then I should see that the loan application status is denied
# Scenario: CREATE Loan: E2E
#     When I click on create loan button
#     And I apply for a loan that meets acceptable criteria
#     Then I should see that the loan application status is pending
#     And I login to MP portal
#     When I click an application under review
#     And I click on Complete Review loan button
#     Then I comment with to review with button clicked
#     When I click an application under review
#     And I click on Approve loan button
#     Then I should be able to Approve a loan successfully
