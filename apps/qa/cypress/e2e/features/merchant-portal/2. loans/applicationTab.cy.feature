@loansPage
Feature: Loans
        This feature seek to verify the Application tab on the Loan feature on MP

  Background:
    Given I visit "/"

  @mpE2E @mpSanity @mpRegression
  Scenario: Filter Functionality by STATUS
    Given I visit "v1/applications"
    When I click on button "Filter"
    And I find element by name "status"
    And I select "Pending"
    And I click on button "Search"
    Then I see text "PEN"

  @mpE2E @mpSanity @mpRegression
  Scenario: Filter Functionality by APPLICANT Id
    When I get element by selector ':nth-child(1) > .css-7pf6at > .css-96niqf > :nth-child(3) > .css-if9ng8 > .css-1xntmnm'
    And I store element text as "applicantId"
    And I click on button "Filter"
    And I find input by name "applicationId"
    And I type stored "@applicantId"
    And I click on button "Search"
    And I wait 3 second
    And I get element by selector ":nth-child(1) > .css-7pf6at"
    Then I see "@applicantId" in the element

  @mpE2E @mpSanity @mpRegression
  Scenario: Filter Functionality by APPLICANT NAME
    When I get element by selector ':nth-child(1) > .css-7pf6at > .css-96niqf > :nth-child(4) > .css-if9ng8 > .css-5emoyq'
    And I store element text as "applicantName"
    And I click on button "Filter"
    And I find input by name "customerName"
    And I type stored "@applicantName"
    And I click on button "Search"
    And I wait 3 second
    And I get element by selector ":nth-child(1) > .css-7pf6at"
    Then I see "@applicantName" in the element

  @mpE2E @mpSanity @mpRegression
  Scenario: Filter Functionality by APPLICANT EMAIL
    When I get element by selector ':nth-child(1) > .css-7pf6at > .css-96niqf > :nth-child(4) > .css-if9ng8 > .css-1xa8ojw'
    And I store element text as "applicantEmail"
    And I click on button "Filter"
    And I find input by name "customerEmail"
    And I type stored "@applicantEmail"
    And I click on button "Search"
    And I wait 3 second
    And I get element by selector ":nth-child(1) > .css-7pf6at"
    Then I see "@applicantEmail" in the element
