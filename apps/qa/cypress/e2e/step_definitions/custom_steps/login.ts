import { Given } from "@badeball/cypress-cucumber-preprocessor";

export function loginToMP() {
  cy.session(
    "envLogin",
    () => {
      cy.visit("/");
      const email = Cypress.env("MP_EMAIL");
      const password = Cypress.env("MP_PASSWORD");
      cy.get("[type=email]").type(email);
      cy.get("[type=password]").type(password);
      cy.get("[type=submit]").should("be.visible").click();
      cy.wait(2000); // Consider using more reliable waiting methods
    },
    {
      cacheAcrossSpecs: true,
    }
  );
}
export function loginToCP() {
  cy.session(
    "envLogin",
    () => {
      cy.visit("/");
      const email = Cypress.env("CP_EMAIL");
      const password = Cypress.env("CP_PASSWORD");
      cy.get("[type=email]").type(email);
      cy.get("[type=password]").type(password);
      cy.get("[type=submit]").should("be.visible").click();
      cy.wait(2000); // Consider using more reliable waiting methods
    },
    {
      cacheAcrossSpecs: true,
    }
  );
}

beforeEach(() => {
  const cpUrl = Cypress.env("CP_BASE_URL");
  const mpUrl = Cypress.env("MP_BASE_URL");

  if (cpUrl) {
    loginToCP();
  } else if (mpUrl) {
    loginToMP();
  } else {
    throw new Error(
      "Neither CP_BASE_URL nor MP_BASE_URL is set in the environment."
    );
  }
});
